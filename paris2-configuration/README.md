# PARIS 2.0 Configuration

This repository keep static PARIS 2.0 Configuration that is used for deployment of services. It includes all common configuration for all environments.
Credentials are stored in parameter store and linked in "credentials_params" property in the configuration json.

## Configuration Structure

```json
{
    "env": "{environment name}",
    "aws_account_id": "{AWS account ID}",
    "region": "{AWS Region}",
    "web_app": {
        "website-s3-bucket": 
        "base_url": "{PARIS 2.0 website url}"
    },
    "api": { // PARIS 2.0 API related configuration
        "endpoint_type": "edge",
        "base_urls": {
             // Base Urls
        }
    },
    "file": {
        "s3_buckets": {
            "file_transfer": "paris2-file-transfer-uat2"
        },
        "efs": {
            "access_point_arn": "arn:aws:elasticfilesystem:ap-southeast-1:************:access-point/fsap-0b9cc7607641bda53",
            "file_system_arn": "arn:aws:elasticfilesystem:ap-southeast-1:************:file-system/fs-6cb5af2d"
        }
    },
    "network": { // network related
        "vpc": "vpc-09aaf87be2ec3842c",
        "lambda_vpc_config": {
            "securityGroupIds": ["sg-02c5ef9ad67f9ffee"],
            "subnetIds": ["subnet-0e72d216c668a5791", "subnet-04ddb003faf9d55d3", "subnet-001bb2ece3ed86750"]
        }
    },
    "auth": { // auth / keycloak related
        "jwt_public_key": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlVbw5zmZUJ0uqBJvWwGGHlHrCXv28foMQ0wH3Kb0lDu81bGBUM5o79Vni4mow3hGg3wvdVri58tsCX5BBVB29XoEKPV+BkhiB5xJI1GO23Z1wI8yRRZTuMT3gT0Cm0m9AeN2YSOfzoAZ7TbYi3QcdL12hg+iNkVn8viA5s3cbgoDYTURrUACpW4W6SJHDf2BvuUyY86+Q/I1rSc6+Qu0IupBk6fbA+CKhHvzizMJAjCJexd3JlOir9897NoBQer7WoM7DtnxGmzopJgfVJP0zPVn/5hEKI/j3wefuHCwM6X8nLISEyEOaW1qJdWTHCVdBeJyl4/6Trc1LzX9XDut4QIDAQAB\n-----END PUBLIC KEY-----",
        "host": "https://auth-uat2.fleetship.com"
    },
    "log": { // log related
        "log_level": "debug",
        "logstash": {
            "host": "logstash-uat2.fleetship.com"
        }
    },
    "external_services": {
        // external services config
        "fos": {
            "api_base_url": "https://api.fos.transas.com/cms/v3",
            "vessel_hardware_poll": "cron(0 0 * * ? *)",
            "role_id_fleet_viewer": "591d68761bae49a394402dba42c494f0"
        },
        "azure_ad": {
            "owner_client_id": "4d12072b-2ecf-40d5-bd5a-ef8157465ad8",
            "owner_tenant_id": "29759440-8980-4b57-bfde-2e85c69f9ee6"
        }
    },
    "params": {
        // ... some other params stored in Parameter Store that are in string type (not encrypted)
    },
    "credentials_params": {
        "paris1_db_connection": "/paris1-db-connection/live",
        // ... credentials stored in Parameter Store that are in secure string type (encrypted)
    }
}
```