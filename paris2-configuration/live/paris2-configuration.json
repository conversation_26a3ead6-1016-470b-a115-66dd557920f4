{"env": "live", "aws_account_id": "************", "region": "ap-southeast-1", "paris1": {"host": "https://paris.fleetship.com"}, "web_app": {"website_s3_bucket": "paris2-website-live", "base_url": "https://paris2.fleetship.com", "script_base_url": "https://paris2.fleetship.com", "cdn_base_url": "/npm-cdn", "share_point_cms_site_name": "PARIS20-CMS", "rss_url": "https://www.fleetship.com/feed", "inspections_audits_base": "inspections-audits"}, "api": {"endpoint_type": "edge", "base_urls": {"vessel": "https://paris-api-live.fleetship.com/vessel", "keycloak": "https://paris-api-live.fleetship.com/keycloak-admin", "notification": "https://notification-live.fleetship.com", "auth": "https://auth.fleetship.com/auth", "reference": "https://paris-api-live.fleetship.com/reference", "vessel_accounting_facade": "https://paris2-owner-financial-reporting-api-live.fleetship.com/vesselaccounting-facade", "ship_party": "https://paris-api-live.fleetship.com/ship-party", "risk_assessment": "https://paris-api-live.fleetship.com/risk-assessment", "tableau_proxy": "https://paris-api-live.fleetship.com/tableau", "seafarer": "https://paris-api-live.fleetship.com/seafarer", "data_import": "https://dataimport-live.fleetship.com/api/data-import", "file": "https://paris-api-live.fleetship.com/file", "inspection_hasura": "https://hasura-qms2-api-live.fleetship.com/v1/graphql", "inspection_lambda": "https://paris-api-live.fleetship.com/hasura-action-api", "inspection_create": "https://wvwrirwpoq5fb6a3kujtqnk4za0hwetj.lambda-url.ap-southeast-1.on.aws", "app_distribution": "https://paris-api-live.fleetship.com/app-distribution", "deficiency_hasura": "https://hasura-defects-api-live.fleetship.com/v1/graphql", "wss_base": "wss://hasura-qms2-api-live.fleetship.com/v1/graphql", "deficiency_ws": "wss://hasura-defects-api-live.fleetship.com/v1/graphql", "inspection_deficiency_reference": "https://paris-api-live.fleetship.com/inspection-deficiency-reference", "base_images_qmsv2": "https://paris-api-live.fleetship.com/qmsv2-images", "base_images_qmsv2_files": "https://paris-api-live.fleetship.com/qmsv2-images/files", "crew_assignment": "https://paris-api-live.fleetship.com/crew-assignment", "survey": "https://paris-api-live.fleetship.com/survey", "qhse": "https://paris-api-live.fleetship.com/qhse", "deficiencies": "https://paris-api-live.fleetship.com/deficiencies", "item_master": "https://paris-api-live.fleetship.com/item-master", "owner_reporting_s3": "https://paris2-ofr-live.s3.ap-southeast-1.amazonaws.com", "paris_api": "https://paris-api-live.fleetship.com/", "inspection": "https://paris-api-live.fleetship.com/inspection", "defects": "https://paris-api-live.fleetship.com/defects", "portagebill_facade": "https://paris2-portagebill-api-live.fleetship.com/portagebill-facade", "account": "https://paris-api-live.fleetship.com/account", "etl_facade": "https://paris2-portagebill-api-live.fleetship.com/etl-facade", "external_reporting_url": "https://paris2-external-reporting-live.fleetship.com/etl-service", "inventory_management": "https://paris-api-live.fleetship.com/inventory-management"}, "domain_name": "paris-api-live.fleetship.com"}, "ecr": {"registry": "************.dkr.ecr.ap-southeast-1.amazonaws.com", "repositories": {"tableau_proxy": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-tableau-proxy", "vessel_spare_sync": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-vessel-spare-sync", "data_import": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-data-import", "requisition": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-requisition", "notification": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-notification"}, "tags": {"keycloak": "9.0.3-v4", "notification": "live-48"}}, "tableau": {"host": "https://tableau-live.fleetship.com", "proxy_host": "https://tableau-proxy-live.fleetship.com", "proxy_uuid_namespace": "01511cf3-c29c-4af6-835f-36f937704a2b", "proxy_auth_image_tag": "nova-authServer-live-60", "proxy_web_image_tag": "nova-webServer-live-60"}, "notification": {"db_host": "paris2-vessel-live.cidtjjdw9luh.ap-southeast-1.rds.amazonaws.com", "jaeger_base_url": "https://jaeger-live-collector.fleetship.com/api/traces", "host": "notification-live.fleetship.com", "ingress_group_name": "live-alb", "cors_origin": "https://paris2-live.fleetship.com,https://paris2.fleetship.com"}, "file": {"s3_buckets": {"file_transfer": "paris2-file-transfer-live", "vessel": "paris2-vessel-files-live", "file": "paris2-files-live", "static": "paris2-static-live", "inspection_file": "paris2-inspection-file-upload-live", "inspection_image": "paris2-inspection-image-upload-live", "inspection_image_upload_event": "paris2-inspection-image-upload-event-live", "inspection_image_upload_lz4_event": "paris2-inspection-image-upload-lz4-event-live", "inspection_xls_upload": "paris2-inspection-docs-upload-live", "inspection_xls_download": "paris2-inspection-docs-download-xls-live", "inspection_mail": "paris2-inspection-mail-live", "inspection_payload": "paris2-inspection-payloads-live", "seafarer_files": "paris2-seafarer-files-live", "inventory_management_upload": "paris2-inventory-management-upload-live"}, "efs": {"local_mount_path": "/mnt/lambda-efs", "access_point_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:access-point/fsap-01deb0f89cb404633", "file_system_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:file-system/fs-823063c3"}, "token": {"upload": {"jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsLp5QwPOemhdchs7XpXQc1g2UaUOOXrZey2DdQ+GPUJRsK1t6vhJeFoyRVNwxM9waz5SZtQe5n/l2uv1WsIu+v53qX7ZG/DjGg3t+CRS/m6z7wedasWWXFth9G3bsyylGSI8V4gJxY+t4sMnZIIqoH4pkQ9N7k6I/AmKS3dwS6QIDAQAB\n-----E<PERSON> PUBLIC KEY-----", "expire_in": 300}, "download": {"expire_in": 900}}}, "network": {"vpc": "vpc-09aaf87be2ec3842c", "lambda_vpc_config": {"securityGroupIds": ["sg-024afd36bef98a2ad"], "subnetIds": ["subnet-02b4f029a2a8443bc", "subnet-0f1a9838aa7f64e58", "subnet-06133517382087711"]}}, "auth": {"jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlVbw5zmZUJ0uqBJvWwGGHlHrCXv28foMQ0wH3Kb0lDu81bGBUM5o79Vni4mow3hGg3wvdVri58tsCX5BBVB29XoEKPV+BkhiB5xJI1GO23Z1wI8yRRZTuMT3gT0Cm0m9AeN2YSOfzoAZ7TbYi3QcdL12hg+iNkVn8viA5s3cbgoDYTURrUACpW4W6SJHDf2BvuUyY86+Q/I1rSc6+Qu0IupBk6fbA+CKhHvzizMJAjCJexd3JlOir9897NoBQer7WoM7DtnxGmzopJgfVJP0zPVn/5hEKI/j3wefuHCwM6X8nLISEyEOaW1qJdWTHCVdBeJyl4/6Trc1LzX9XDut4QIDAQAB\n-----END PUBLIC KEY-----", "host": "https://auth.fleetship.com", "domain_name": "auth.fleetship.com", "keycloak_realm": "paris2", "client_id": "paris2-app", "ingress_group_name": "live-alb", "db_host": "paris2-keycloak-live.cidtjjdw9luh.ap-southeast-1.rds.amazonaws.com", "azure_ad_app_client_id": "6abc9716-211e-402c-8a4a-b95d1b6c44f1", "azure_ad_token_host": "https://login.microsoftonline.com/cf7f83ec-38ea-406d-b1df-71df17fe5c00/oauth2/v2.0/token", "sns_topic": "arn:aws:sns:ap-southeast-1:************:paris2-keycloak-assign-event-live"}, "log": {"log_level": "debug", "logstash": {"host": "logstash-live.fleetship.com"}}, "api_gateway_execution_log": {"defects_api_group": "API-Gateway-Execution-Logs_b25l3nfi80/live", "photo_api_group": "API-Gateway-Execution-Logs_zcv3f4q4dc/live"}, "data_import": {"host": "dataimport-live.fleetship.com", "ingress_group_name": "live-alb", "image_tag": "dataimport-live-49", "db_connection_key_id": "5241aea1-f0a9-443e-b5bb-aef9d5328192", "deployed_date": "1,637,804,783,033", "dynamodb_table_name_dataset": "paris2-data-import-dataset-live", "dynamodb_table_name_import_status": "paris2-data-import-status-live", "dynamodb_table_name_schema": "paris2-data-import-schema-live", "dynamodb_table_name_version": "paris2-data-import-version-live"}, "requisition": {"minimum_receive_date": "2022-08-01T00:00:00Z", "feedback_trace_back_time": 744, "requisition_sync_task_tag": "purchase-receipts-from-p1-live-76", "purchase_receipt_sync_task_url": "http://ad39da466991345b78f2504e1d8dd8e3-b76e283f8a4432a2.elb.ap-southeast-1.amazonaws.com:3000"}, "seafarer": {"enable_create_update_seafarer": true, "date_validate_all_seamanbook": "2022-03-26 19:00:00.000 +0800", "scoring_api": "https://oqi8jqg92m-vpce-0b72e77780ac0f567.execute-api.ap-southeast-1.amazonaws.com/dev/crew-scoring", "ocimf_sync_task_url": "http://aa2a35c44604d47e1827dcb52bf918df-2125097459.ap-southeast-1.elb.amazonaws.com:3000", "ocimf_sire_wsdl_url": "https://api.ocimf.org/OcimfServices.asmx?WSDL"}, "third_party": {"support_email": "<EMAIL>"}, "external_services": {"fos": {"api_base_url": "https://api.fos.transas.com/cms/v3", "wartsila_host": "https://fos.wartsila.com", "vessel_hardware_poll": "cron(0 0 * * ? *)", "role_id_fleet_viewer": "591d68761bae49a394402dba42c494f0", "client_id": "e74d7193-b51e-4147-a5e2-d8e78af43c49", "authority": "https://login.microsoftonline.com/b4c46be5-4c65-4b3e-aa67-f0a4a2479d7d", "scope": "48219217-bfa9-4e80-b724-183f3585ac53/.default", "username": "<EMAIL>"}, "azure_ad": {"owner_client_id": "4d12072b-2ecf-40d5-bd5a-ef8157465ad8", "owner_tenant_id": "29759440-8980-4b57-bfde-2e85c69f9ee6"}, "google_analytics": {"measurement_id": "UA-180565151-2", "ga_code": "G-N7V6X6G4LD", "ua_code": "UA-207732829-3", "gtm_code": "GTM-TJJZJHK"}, "google_map": {"api_key": "AIzaSyDjCfK_NSTNxWHuQPqmzaltduDISJWIvwY"}, "stratum_five": {"api_base_url": "https://system.stratumfive.com"}, "firebase": {"inspection": {"auth_domain": "vessel-inspection-report.firebaseapp.com", "project_id": "vessel-inspection-report", "storage_bucket": "vessel-inspection-report.appspot.com", "messaging_sender_id": "594778280952", "api_id": "1:594778280952:web:ab5a2c333e659305d6fc0d", "measurement_id": "G-M76Y0B078V", "api_key": "AIzaSyAGxNxI_bW2Cg1ru4B0BO7rOCe2uJSxbDI"}}, "share_point": {"domain_name": "fleetship.sharepoint.com", "tenant_id": "cf7f83ec-38ea-406d-b1df-71df17fe5c00", "grant_type": "client_credentials", "resource": "00000003-0000-0ff1-ce00-000000000000"}, "world_check_one": {"protocol": "https://", "host": "api-worldcheck.refinitiv.com", "base_path": "/v2/", "group_id": "5jb69ekr1m1p1fkg2n38vzyuq", "db_write_capacity": 10}, "ship_data_center": {"nk_verifier_api": "https://api.shipdatacenter.com/ios-op/v1/data-import/file-data"}, "azure_websites": {"abs_verifier_api": "to be fetched"}, "dnv": {"ovd_admin_host": "https://ovdadmin.veracityapp.com", "ovd_veracity_app_host": "https://ovd.veracityapp.com/api"}, "bloomberg": {"api_url_base": "https://api.bloomberg.com/eap/catalogs"}}, "schedule": {"cron_expression": {"15_mintutes": "rate(15 minutes)", "requisitions_sync_pilot_vessel_from_paris1": "cron(0 1 * * ? *)", "vessel_sync_default": "rate(15 minutes)", "requisitions_sync_from_paris1": "rate(30 minutes)", "requisitions_notify_changes": "cron(0 0,5,9 * * ? *)", "requisitions_sync_purchase_receipts_from_paris1": "cron(0 5,11,23 * * ? *)", "seafarer_sync_paris1_seafarer_cron_schedule": "cron(0 0 * * ? *)", "seafarer_sync_paris1_seafarer_dropdown_cron_schedule": "cron(23 0 * * ? *)", "seafarer_seafarer_automatic_archival_schedule": "cron(0 * * * ? *)", "seafarer_refresh_experience_summary_mv_cron_schedule": "cron(0 0 * * ? *)", "seafarer_sync_seafarer_status_cron_schedule": "cron(0-59/3 * * * ? *)", "seafarer_sync_seafarer_experience_cron_schedule": "cron(0-59/15 * * * ? *)", "crew_assignment_sync_payheads_paris2_to_paris1_cron_schedule": "cron(0 1 * * ? *)", "crew_assignment_sync_crew_assignment_details_cron_schedule": "cron(0-59/30 * * * ? *)", "crew_assignment_apply_wage_update_and_promotion_schedule": "cron(0 * * * ? *)", "crew_assignment_populate_new_vessel_ownership_cron_schedule": "cron(0 * * * ? *)", "crew_assignment_sync_master_appraisal_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_admin_report_cron_schedule": "cron(55 15 * * ? *)", "crew_assignment_supy_recommendation_approval_cron_schedule": "rate(5 minutes)", "crew_assignment_travel_agents_cron_schedule": "cron(* * * * ? *)", "crew_assignment_prepare_crewlist_report_data_cron_schedule": "rate(2 minutes)", "crew_assignment_prepare_crewlist_report_sheet_cron_schedule": "rate(3 minutes)", "crew_assignment_ocimf_cron_schedule": "cron(30 14 * * ? *)", "crew_assignment_mark_crew_plan_expire_cron_schedule": "cron(45 1 * * ? *)", "survey_populate_access_tables_cron_schedule": "cron(5 0 * * ? *)", "survey_offboarding_cron_schedule": "cron(5 0 * * ? *)", "survey_debriefing_cron_schedule": "cron(5 0 * * ? *)", "sync_inspection_dropdown": "cron(15 1 * * ? *)", "sync_hourly_working_days": "rate(1 hour)", "sync_active_deficiency": "rate(15 minutes)", "sync_pms": "cron(0 0 * * ? *)", "sync_position_report": "rate(15 minutes)", "sync_position_report_luboil_consumption": "cron(0 0 * * ? *)", "sync_osc_inventory_balance_update": "cron(0 0 * * ? *)", "sync_requisition": "rate(30 minutes)", "notify_requisition_status_changes": "cron(0 0,5,9 * * ? *)", "notify_rejected_inventory_update": "cron(0 0 * * ? *)", "sync_itinerary": "rate(15 minutes)", "vessel_navigational_equipment_sync_cron_schedule": "cron(15 1 * * ? *)", "inventory_management_sfi_master_paris2_sync_cron_schedule": "cron(0 12 * * ? *)", "inventory_management_sfi_master_paris1_sync_cron_schedule": "cron(15 12 * * ? *)", "sire_ocimf_matrix_sync_cron_schedule": "cron(0 1 ? * 7 *)", "precompute_ocimf_compliance_cron_schedule": "cron(0 19 ? * * *)"}}, "kms": {"keys": {"ssm": "key/b3acaf3a-78dd-41dc-a05a-b9141142f9b0"}}, "params": {}, "emails": {"inspection_approval_source": "<EMAIL>", "eu_ets_email_failure_recipients": "<EMAIL>", "eu_voyage_alert_email_cc": "<EMAIL>/<EMAIL>", "report_to_admin_failure_email": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "report_to_admin_from_email": "<EMAIL>", "report_to_admin_to_email": "<EMAIL>", "report_to_admin_cc_email": "<EMAIL>,<EMAIL>", "inventory_management_import_failure_cc": "<EMAIL>"}, "sqs": {"inventory_management_import_queue": "paris2-inventory-management-upload-live.fifo", "inventory_management_p1_sync_queue": "paris2-inventory-management-p1-sync-live.fifo"}, "kubernetes": {"ingress": {"alb_group_name": "live-alb"}, "replica_count": {"hasura_defects": 2, "hasura_qms": 2}}, "credentials_params": {"paris1_db_connection": "/paris1-db-connection/live", "paris1_db_password": "/paris1-db-password/live", "paris2_deficiency_db_connection": "/paris2-db-connection/deficiency/live", "paris2_inspection_db_connection": "/paris2-db-connection/inspection/live", "paris2_item_master_db_connection": "/paris2-db-connection/item_master/live", "paris2_ship_party_db_connection": "/paris2-db-connection/ship_party/live", "paris2_owner_financial_reporting_db_connection": "/paris2-db-connection/owner-financial-reporting/live", "paris2_auth_db_connection": "/paris2-auth-db-connection/live", "paris2_external_reporting_db_connection": "/paris2-db-connection/external-reporting/live", "paris2_vessel_db_connection": "/paris2-db-connection/vessel/live", "paris2_notification_db_connection": "/paris2-db-connection/notification/live", "paris2_vessel_db_password": "/paris2-vessel-db-password/live", "paris2_vessel_service_user": "/paris2-api-vessel-user/live", "paris2_item_master_user": "/paris2-api-item-master-user/live", "paris2_ship_party_user": "/paris2-api-ship-party-user/live", "paris2_service_client_secret": "/paris2-service-client-secret/live", "paris2_keycloak_password": "/paris2-auth-admin-password/live", "sonar_auth_token": "/paris2-sonar-auth-token/live", "keycloak_admin_user": "/paris2-api-keycloak-user/live", "stratum_five_api_credential": "/paris2-stratum-five-api-credential/live", "stratum_five_auth_key": "/paris2-stratum-five-auth-key/live", "paris2_survey_db_connection": "/paris2-db-connection/survey/live", "paris2_seafarer_db_connection": "/paris2-db-connection/seafarer/live", "paris2_portagebill_db_connection": "/paris2-db-connection/portagebill/live", "paris2_seafarer_db_password": "/paris2-seafarer-db-password/live", "paris2_risk_assessment_db_user": "/paris2-risk-assessment-db-user/live", "paris2_risk_assessment_db_password": "/paris2-risk-assessment-db-password/live", "paris2_vessel_db_host": "/paris2-vessel-db-host/live", "paris2_auth_db_password": "/paris2-auth-db-password/live", "paris2_service_user": "/paris2-service-auth-user/live", "username_hash_key": "/auth-username-hash-secret-key/live", "paris2_qhse_db_connection": "/paris2-db-connection/qhse/live", "paris2_qhse_user": "/paris2-api-qhse-user/live", "auth_admin_client_secret": "/paris2/auth/admin-client-secret/live", "hasura_inspection_admin_secret": "/paris2-hasura-inspections-admin-secret/live", "hasura_defects_admin_secret": "/paris2-hasura-defects-admin-secret/live", "world_check_one_api_key": "/world-check-one-api-key/live", "world_check_one_api_secret": "/world-check-one-api-secret/live", "paris2_inspection_service_user": "/paris2-api-inspection-user/live", "paris2_deficiency_sync_service_user": "/paris2-api-deficiency-sync-user/live", "oracle_finance_db_connection": "/oracle-financial-db-connection/live", "nk_verifier_api_credential": "/paris2-nk-verifier-api-credential/live", "abs_api_access_token": "/paris2-abs-api-access-token/live", "third_party_integration_ip_set": "/paris2-third-party-integration-ip-set/live", "api_key_client_secret": "/paris2-api-key-keycloak-client-secret/live", "currency_api_id": "/currency-api-id/live", "currency_api_key": "/currency-api-key/live", "bloomberg_api_account_id": "/bloomberg_api_account_id/live", "bloomberg_api_client_id": "/bloomberg_api_client_id/live", "bloomberg_api_client_secret": "/bloomberg_api_client_secret/live", "dnv_access_key": "/paris2-dnv-access-key/live", "dnv_company_id": "/paris2-dnv-company-id/live", "enabled_verifiers": "/paris2-enabled-verifiers/live", "paris2-inspection-form-b-secret": "/paris2-inspection-form-b-secret/live", "paris2_reference_db_connection": "/paris2-db-connection/reference/live", "paris2_reference_db_password": "/paris2-reference-db-password/live"}, "swagger": {"authorizationUrl": "https://auth.fleetship.com/auth/realms/paris2/protocol/openid-connect/auth", "account_url": "https://paris-api-live.fleetship.com/account", "data_import_url": "https://dataimport-live.fleetship.com/api/data-import"}, "inspection": {"form_b_jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvoxPTlJBisYIEL0KEffb\nide04w2gvLwOVWe5K9igXRKnNNThKP8d2c4c4HxVSBYxqLkMq4e3inpR7KJEKk11\n6tsOIp3ThvVF04M0AyT0QvImPkEKEf4TlwSxq2SvX4E3GJtO/CGrU441K3StH542\nSvydWtEwhFwvN6kZeTwH/OIOevaC2csoVw5IT1eX2F9ZywmxiNff6lfut0xgykuJ\nORSNmadZwBNHy0V640kbIe3vtYXqPaNMH74n9AGzwtW3wneCQTz+wVUJtlirKnpU\n5dzg4Mn9jhBTmqY5wf4Fcqho6pBnboqsMBq1cp8A0FUIjs1TKvvuYITwjcxvVvYM\nxQIDAQAB\n-----END PUBLIC KEY-----\n"}, "lambda_layer": {"oracle_instant_client": "arn:aws:lambda:ap-southeast-1:************:layer:oracle-instant-client:2", "chromium": "arn:aws:lambda:ap-southeast-1:************:layer:chromium:3"}}