{"env": "qa2", "aws_account_id": "************", "region": "ap-southeast-1", "paris1": {"host": "https://parisqa.fleetship.com"}, "web_app": {"website_s3_bucket": "paris2-website-qa2", "base_url": "https://paris2-qa2.fleetship.com", "script_base_url": "https://paris2-qa2.fleetship.com", "cdn_base_url": "/npm-cdn", "share_point_cms_site_name": "PARIS20-CMS", "rss_url": "https://www.fleetship.com/feed", "inspections_audits_base": "inspections-audits"}, "api": {"endpoint_type": "regional", "base_urls": {"vessel": "https://paris-api-qa2.fleetship.com/vessel", "keycloak": "https://paris-api-qa2.fleetship.com/keycloak-admin", "notification": "https://notification-qa2.fleetship.com", "auth": "https://auth-qa2.fleetship.com/auth", "reference": "https://paris-api-qa2.fleetship.com/reference", "vessel_accounting_facade": "https://paris2-owner-financial-reporting-api-qa2.fleetship.com/vesselaccounting-facade", "risk_assessment": "https://paris-api-qa2.fleetship.com/risk-assessment", "ship_party": "https://paris-api-qa2.fleetship.com/ship-party", "tableau_proxy": "https://paris-api-qa2.fleetship.com/tableau", "seafarer": "https://paris-api-qa2.fleetship.com/seafarer", "data_import": "https://dataimport-qa2.fleetship.com/api/data-import", "file": "https://paris-api-qa2.fleetship.com/file", "inspection_hasura": "https://hasura-qms2-api-qa2.fleetship.com/v1/graphql", "inspection_lambda": "https://paris-api-qa2.fleetship.com/hasura-action-api", "inspection_create": "https://i4llz3z5ga5fg6akigf6isvvom0iyptv.lambda-url.ap-southeast-1.on.aws", "app_distribution": "https://paris-api-qa2.fleetship.com/app-distribution", "deficiency_hasura": "https://hasura-defects-api-qa2.fleetship.com/v1/graphql", "wss_base": "wss://hasura-qms2-api-qa2.fleetship.com/v1/graphql", "deficiency_ws": "wss://hasura-defects-api-qa2.fleetship.com/v1/graphql", "inspection_deficiency_reference": "https://paris-api-qa2.fleetship.com/inspection-deficiency-reference", "base_images_qmsv2": "https://paris-api-qa2.fleetship.com/qmsv2-images", "base_images_qmsv2_files": "https://paris-api-qa2.fleetship.com/qmsv2-images/files", "crew_assignment": "https://paris-api-qa2.fleetship.com/crew-assignment", "survey": "https://paris-api-qa2.fleetship.com/survey", "qhse": "https://paris-api-qa2.fleetship.com/qhse", "deficiencies": "https://paris-api-qa2.fleetship.com/deficiencies", "item_master": "https://paris-api-qa2.fleetship.com/item-master", "owner_reporting_s3": "https://paris2-ofr-qa2.s3.ap-southeast-1.amazonaws.com", "paris_api": "https://paris-api-qa2.fleetship.com/", "inspection": "https://paris-api-qa2.fleetship.com/inspection", "defects": "https://paris-api-qa2.fleetship.com/defects", "portagebill_facade": "https://paris2-portagebill-api-qa2.fleetship.com/portagebill-facade", "account": "https://paris-api-qa2.fleetship.com/account", "etl_facade": "https://paris2-portagebill-api-qa2.fleetship.com/etl-facade", "external_reporting_url": "https://paris2-external-reporting-qa2.fleetship.com/etl-service", "inventory_management": "https://paris-api-qa2.fleetship.com/inventory-management"}, "domain_name": "paris-api-qa2.fleetship.com"}, "ecr": {"registry": "************.dkr.ecr.ap-southeast-1.amazonaws.com", "repositories": {"tableau_proxy": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-tableau-proxy", "vessel_spare_sync": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-vessel-spare-sync", "data_import": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-data-import", "requisition": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-requisition", "notification": "************.dkr.ecr.ap-southeast-1.amazonaws.com/paris2-notification"}, "tags": {"keycloak": "9.0.3-v4", "notification": "qa2-45"}}, "tableau": {"host": "https://tableau-qa2.fleetship.com", "proxy_host": "https://tableau-proxy-qa2.fleetship.com", "proxy_uuid_namespace": "80d6d0b8-0fe2-4573-a41c-7eb95f02d837", "proxy_auth_image_tag": "nova-authServer-qa2-60", "proxy_web_image_tag": "nova-webServer-qa2-60"}, "notification": {"db_host": "paris2-vessel-qa2.cnokqrjedngk.ap-southeast-1.rds.amazonaws.com", "jaeger_base_url": "https://jaeger-common-collector.fleetship.com/api/traces", "host": "notification-qa2.fleetship.com", "ingress_group_name": "common-alb", "cors_origin": "https://paris2-qa2.fleetship.com"}, "file": {"s3_buckets": {"file_transfer": "paris2-file-transfer-qa2", "vessel": "paris2-vessel-files-qa2", "file": "paris2-files-qa2", "static": "paris2-static-qa2", "inspection_file": "paris2-inspection-file-upload-qa2", "inspection_image": "paris2-inspection-image-upload-qa2", "inspection_image_upload_event": "paris2-inspection-image-upload-event-qa2", "inspection_image_upload_lz4_event": "paris2-inspection-image-upload-lz4-event-qa2", "inspection_xls_upload": "paris2-inspection-docs-upload-qa2", "inspection_xls_download": "paris2-inspection-docs-download-xls-qa2", "inspection_mail": "paris2-inspection-mail-qa2", "inspection_payload": "paris2-inspection-payloads-qa2", "seafarer_files": "paris2-seafarer-files-qa2", "inventory_management_upload": "paris2-inventory-management-upload-qa2"}, "efs": {"local_mount_path": "/mnt/lambda-efs", "access_point_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:access-point/fsap-0ed449d03a26d82d9", "file_system_arn": "arn:aws:elasticfilesystem:ap-southeast-1:684031101155:file-system/fs-14d03554"}, "token": {"upload": {"jwt_public_key": "-----B<PERSON>IN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDdMHX/FvuXbbFndztheOBXQfyfpeeuE+wJr4IrIqJxWs8zSssJ2LYbq7S/J4CNoawAE6ee4wwPdLrO5WY5zfTc5BZSR6d1JIaJxzXUv+YwNekiOm9XYLhlhyqIeS6NYdnGli5WQN89aIJhEPbCb4bOXyKXAZVMuEsWTy6qkKeDcQIDAQAB\n-----END PUBLIC KEY-----", "expire_in": 300}, "download": {"expire_in": 900}}}, "network": {"vpc_id": "vpc-01815c546a44b855a", "lambda_vpc_config": {"securityGroupIds": ["sg-00f0617d85b29ac85"], "subnetIds": ["subnet-0f1b715ad3eccd973", "subnet-0fcb5d300240fa26d", "subnet-013eb0dbb339a124a"]}}, "auth": {"jwt_public_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjkw7R3AW/F6aWl/VdMtlr3Tw50xkodSvUw3c8d8OmTI5XoWDIQxQNXH4JyRd5YhZq2Jn9018qDuXhEJHfWkkfvy6iaxrdAO2grFeZMsSOBvLWMKnFVcZ8uBG3oCAUmswBlDx3zS9WrmGF3SbNqVY1DUTiu4OrHC9tR0iBfvMlyp3JkrnxKOw5xllcUP+WmIgxTpMRZypovYAqrFyxYfBDiEAcBDPDDKgmn0DlE9fU19Dqa8+1FEjemZd9TEeV2dp7/HBgdFySxNYdQDl8fTBKHJFbmXFWyC18OQly8EuVxsW38EN97cb3wy+DF3WIF1DfbxYeEiIuB8uUceD1yBeBQIDAQAB\n-----END PUBLIC KEY-----", "host": "https://auth-qa2.fleetship.com", "domain_name": "auth-qa2.fleetship.com", "keycloak_realm": "paris2", "client_id": "paris2-app", "ingress_group_name": "common-alb", "db_host": "paris2-keycloak-qa2.cnokqrjedngk.ap-southeast-1.rds.amazonaws.com", "azure_ad_app_client_id": "6abc9716-211e-402c-8a4a-b95d1b6c44f1", "azure_ad_token_host": "https://login.microsoftonline.com/cf7f83ec-38ea-406d-b1df-71df17fe5c00/oauth2/v2.0/token", "sns_topic": "arn:aws:sns:ap-southeast-1:************:paris2-keycloak-assign-event-qa2"}, "log": {"log_level": "debug", "logstash": {"host": "logstash-qa2.fleetship.com"}}, "api_gateway_execution_log": {"defects_api_group": "API-Gateway-Execution-Logs_9rf14ffw2l/qa2", "photo_api_group": "API-Gateway-Execution-Logs_cr0vzqvj6j/qa2"}, "data_import": {"host": "dataimport-qa2.fleetship.com", "ingress_group_name": "common-alb", "image_tag": "dataimport-qa2-35", "db_connection_key_id": "46c412c2-f4ba-414d-a516-df59b0ba9a11", "deployed_date": "1,637,804,783,033", "dynamodb_table_name_dataset": "paris2-data-import-dataset-qa2", "dynamodb_table_name_import_status": "paris2-data-import-status-qa2", "dynamodb_table_name_schema": "paris2-data-import-schema-qa2", "dynamodb_table_name_version": "paris2-data-import-version-qa2"}, "requisition": {"minimum_receive_date": "2022-08-01T00:00:00Z", "feedback_trace_back_time": 744, "requisition_sync_task_tag": "purchase-receipts-from-p1-qa2-65", "purchase_receipt_sync_task_url": "http://a9bff7483f09f43c6be4f29c6e6da600-aa1a0d6d38dc854e.elb.ap-southeast-1.amazonaws.com:3000"}, "seafarer": {"enable_create_update_seafarer": true, "date_validate_all_seamanbook": "2022-03-21 14:00:00.000 +0800", "ocimf_sync_task_url": "http://ab68d20d1dc1540fdba605ebf93a3646-**********.ap-southeast-1.elb.amazonaws.com:3000", "ocimf_sire_wsdl_url": "https://staging-ws.ocimf.biz/OcimfServices.asmx?WSDL"}, "third_party": {"support_email": ""}, "external_services": {"fos": {"api_base_url": "https://accounts-stage.fos.transas.com/api/v3", "wartsila_host": "https://fos-test.eniram.fi", "vessel_hardware_poll": "cron(15 1 * * ? *)", "role_id_fleet_viewer": "2bc49a9508d8443d9ab6dd0427c57f72", "client_id": "b93676ac-e0be-49b5-bf7b-be3d32370e28", "authority": "https://login.microsoftonline.com/a9910275-d712-421f-9de7-621fa821db06", "scope": "4d217f9f-2647-4c89-90a2-90c2edd817f8/.default", "username": "<EMAIL>"}, "azure_ad": {"owner_client_id": "e1f393ce-01cb-4450-8e35-da94d885e139", "owner_tenant_id": "6490991e-a9a0-4472-9338-7fb242dc6562"}, "google_analytics": {"measurement_id": "UA-*********-1", "ga_code": "G-TRVZB5CX72", "ua_code": "UA-*********-1", "gtm_code": "GTM-M8FGQFW"}, "google_map": {"api_key": "AIzaSyDR_SeN4OQ7HgnbFZoKrDD75Qp1cBCcPso"}, "stratum_five": {"api_base_url": "https://system.stratumfive.com"}, "firebase": {"inspection": {"auth_domain": "vessel-inspection-report.firebaseapp.com", "project_id": "vessel-inspection-report", "storage_bucket": "vessel-inspection-report.appspot.com", "messaging_sender_id": "594778280952", "api_id": "1:594778280952:web:50c3654838218bf6d6fc0d", "measurement_id": "G-Y6W3YH24Q7", "api_key": "AIzaSyAGxNxI_bW2Cg1ru4B0BO7rOCe2uJSxbDI"}}, "share_point": {"domain_name": "fleetship.sharepoint.com", "tenant_id": "cf7f83ec-38ea-406d-b1df-71df17fe5c00", "grant_type": "client_credentials", "resource": "00000003-0000-0ff1-ce00-000000000000"}, "world_check_one": {"protocol": "https://", "host": "api-worldcheck.refinitiv.com", "base_path": "/v2/", "group_id": "5jb69ekr1m1p1fkg2n38vzyuq", "db_write_capacity": 3}, "ship_data_center": {"nk_verifier_api": "https://tri-api.shipdatacenter.com/ios-op/v1/data-import/file-data"}, "azure_websites": {"abs_verifier_api": "https://uat-mrv-api.azurewebsites.net/api/ImportReport"}, "dnv": {"ovd_admin_host": "https://ovdadmin.veracityapp.com", "ovd_veracity_app_host": "https://ovd.veracityapp.com/api"}, "bloomberg": {"api_url_base": "https://api.bloomberg.com/eap/catalogs"}}, "schedule": {"cron_expression": {"15_mintutes": "rate(15 minutes)", "requisitions_sync_pilot_vessel_from_paris1": "rate(30 minutes)", "vessel_sync_default": "rate(15 minutes)", "requisitions_sync_from_paris1": "rate(30 minutes)", "requisitions_notify_changes": "rate(30 minutes)", "requisitions_sync_purchase_receipts_from_paris1": "rate(30 minutes)", "seafarer_sync_paris1_seafarer_cron_schedule": "cron(15 1 * * ? *)", "seafarer_sync_paris1_seafarer_dropdown_cron_schedule": "cron(14 1 * * ? *)", "seafarer_seafarer_automatic_archival_schedule": "cron(0 * * * ? *)", "seafarer_refresh_experience_summary_mv_cron_schedule": "cron(0 1 * * ? *)", "seafarer_sync_seafarer_status_cron_schedule": "cron(0-59/3 * * * ? *)", "seafarer_sync_seafarer_experience_cron_schedule": "cron(0-59/15 * * * ? *)", "crew_assignment_sync_payheads_paris2_to_paris1_cron_schedule": "cron(0 1 * * ? *)", "crew_assignment_sync_crew_assignment_details_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_apply_wage_update_and_promotion_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_populate_new_vessel_ownership_cron_schedule": "cron(0-59/10 * * * ? *)", "crew_assignment_sync_master_appraisal_cron_schedule": "cron(0-59/10 * * * ? *)", "survey_populate_access_tables_cron_schedule": "cron(5 1 * * ? *)", "survey_offboarding_cron_schedule": "cron(5 1 * * ? *)", "survey_debriefing_cron_schedule": "cron(5 1 * * ? *)", "sync_inspection_dropdown": "cron(15 1 * * ? *)", "sync_hourly_working_days": "cron(0/60 * ? * MON-FRI *)", "sync_active_deficiency": "cron(0/15 * ? * MON-FRI *)", "sync_pms": "cron(15 1 * * ? *)", "sync_position_report": "rate(15 minutes)", "sync_position_report_luboil_consumption": "cron(15 1 * * ? *)", "sync_osc_inventory_balance_update": "cron(15 1 * * ? *)", "sync_requisition": "rate(30 minutes)", "notify_requisition_status_changes": "rate(30 minutes)", "notify_rejected_inventory_update": "cron(15 1 * * ? *)", "sync_itinerary": "rate(15 minutes)", "inventory_management_sfi_master_paris2_sync_cron_schedule": "cron(15 1 * * ? *)", "inventory_management_sfi_master_paris1_sync_cron_schedule": "cron(30 1 * * ? *)", "vessel_navigational_equipment_sync_cron_schedule": "cron(15 1 * * ? *)", "sire_ocimf_matrix_sync_cron_schedule": "cron(0 1 ? * 7 *)", "precompute_ocimf_compliance_cron_schedule": "cron(0 19 ? * * *)"}}, "kms": {"keys": {"ssm": "key/2e41c50f-b8ba-40e1-a9a5-44ed1aad1ddb"}}, "params": {}, "emails": {"inspection_approval_source": "<EMAIL>", "eu_ets_email_failure_recipients": "<EMAIL>/<EMAIL>", "eu_voyage_alert_email_cc": "<EMAIL>/<EMAIL>/<EMAIL>", "inventory_management_import_failure_cc": "<EMAIL>/<EMAIL>/<EMAIL>/<EMAIL>"}, "sqs": {"inventory_management_import_queue": "paris2-inventory-management-upload-qa2.fifo", "inventory_management_p1_sync_queue": "paris2-inventory-management-p1-sync-qa2.fifo"}, "kubernetes": {"ingress": {"alb_group_name": "common-alb"}, "replica_count": {"hasura_defects": 1, "hasura_qms": 1}}, "credentials_params": {"paris1_db_connection": "/paris1-db-connection/qa2", "paris1_db_password": "/paris1-db-password/qa2", "paris2_deficiency_db_connection": "/paris2-db-connection/deficiency/qa2", "paris2_inspection_db_connection": "/paris2-db-connection/inspection/qa2", "paris2_item_master_db_connection": "/paris2-db-connection/item_master/qa2", "paris2_ship_party_db_connection": "/paris2-db-connection/ship_party/qa2", "paris2_external_reporting_db_connection": "/paris2-db-connection/external-reporting/qa2", "paris2_owner_financial_reporting_db_connection": "/paris2-db-connection/owner-financial-reporting/qa2", "paris2_auth_db_connection": "/paris2-auth-db-connection/qa2", "paris2_vessel_db_connection": "/paris2-db-connection/vessel/qa2", "paris2_vessel_db_password": "/paris2-vessel-db-password/qa2", "paris2_vessel_service_user": "/paris2-api-vessel-user/qa2", "paris2_item_master_user": "/paris2-api-item-master-user/qa2", "paris2_ship_party_user": "/paris2-api-ship-party-user/qa2", "paris2_service_client_secret": "/paris2-service-client-secret/qa2", "paris2_keycloak_password": "/paris2-auth-admin-password/qa2", "sonar_auth_token": "/paris2-sonar-auth-token/qa2", "keycloak_admin_user": "/paris2-api-keycloak-user/qa2", "stratum_five_api_credential": "/paris2-stratum-five-api-credential/qa2", "stratum_five_auth_key": "/paris2-stratum-five-auth-key/qa2", "paris2_survey_db_connection": "/paris2-db-connection/survey/qa2", "paris2_seafarer_db_connection": "/paris2-db-connection/seafarer/qa2", "paris2_portagebill_db_connection": "/paris2-db-connection/portagebill/qa2", "paris2_seafarer_db_password": "/paris2-seafarer-db-password/qa2", "paris2_risk_assessment_db_user": "/paris2-risk-assessment-db-user/qa2", "paris2_risk_assessment_db_password": "/paris2-risk-assessment-db-password/qa2", "paris2_vessel_db_host": "/paris2-vessel-db-host/qa2", "paris2_auth_db_password": "/paris2-auth-db-password/qa2", "paris2_service_user": "/paris2-service-auth-user/qa2", "username_hash_key": "/auth-username-hash-secret-key/qa2", "paris2_qhse_db_connection": "/paris2-db-connection/qhse/qa2", "paris2_qhse_user": "/paris2-api-qhse-user/qa2", "auth_admin_client_secret": "/paris2/auth/admin-client-secret/qa2", "hasura_inspection_admin_secret": "/paris2-hasura-inspections-admin-secret/qa2", "hasura_defects_admin_secret": "/paris2-hasura-defects-admin-secret/qa2", "world_check_one_api_key": "/world-check-one-api-key/qa2", "world_check_one_api_secret": "/world-check-one-api-secret/qa2", "paris2_inspection_service_user": "/paris2-api-inspection-user/qa2", "paris2_deficiency_sync_service_user": "/paris2-api-deficiency-sync-user/qa2", "oracle_finance_db_connection": "/oracle-financial-db-connection/qa2", "nk_verifier_api_credential": "/paris2-nk-verifier-api-credential/qa2", "abs_api_access_token": "/paris2-abs-api-access-token/qa2", "third_party_integration_ip_set": "/paris2-third-party-integration-ip-set/qa2", "api_key_client_secret": "/paris2-api-key-keycloak-client-secret/qa2", "currency_api_id": "/currency-api-id/qa2", "currency_api_key": "/currency-api-key/qa2", "bloomberg_api_account_id": "/bloomberg_api_account_id/qa2", "bloomberg_api_client_id": "/bloomberg_api_client_id/qa2", "bloomberg_api_client_secret": "/bloomberg_api_client_secret/qa2", "dnv_access_key": "/paris2-dnv-access-key/qa2", "dnv_company_id": "/paris2-dnv-company-id/qa2", "enabled_verifiers": "/paris2-enabled-verifiers/qa2", "paris2_reference_db_connection": "/paris2-db-connection/reference/qa2", "paris2_reference_db_password": "/paris2-reference-db-password/qa2"}, "swagger": {"authorizationUrl": "https://auth-qa2.fleetship.com/auth/realms/paris2/protocol/openid-connect/auth", "account_url": "https://paris-api-qa2.fleetship.com/account", "data_import_url": "https://dataimport-qa2.fleetship.com/api/data-import"}, "lambda_layer": {"oracle_instant_client": "arn:aws:lambda:ap-southeast-1:************:layer:oracle-instant-client:15", "chromium": "arn:aws:lambda:ap-southeast-1:************:layer:chromium:4"}}