import httpService from '../../src/services/http-service';
import axios from 'axios';

// Mock axios
jest.mock('axios');
jest.mock('@paris2/auth');

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('httpService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedAxios.isCancel.mockReturnValue(false);
  });

  describe('Basic exports', () => {
    it('should export HttpMethods with correct values', () => {
      expect(httpService.HttpMethods).toEqual({
        GET: 'GET',
        POST: 'POST',
        DELETE: 'DELETE',
      });
    });

    it('should return an axios instance from getAxiosClient', () => {
      const axiosClient = httpService.getAxiosClient();
      expect(axiosClient).toBeInstanceOf(Object);
      expect(axiosClient).toBeDefined();
    });

    it('should export the axios module', () => {
      expect(httpService.axios).toBe(axios);
    });

    it('should export cancelPreviousRequest function', () => {
      expect(typeof httpService.cancelPreviousRequest).toBe('function');
    });
  });

  describe('Axios instance configuration', () => {
    it('should create axios instance with interceptors', () => {
      const axiosClient = httpService.getAxiosClient();

      expect(axiosClient).toBeDefined();
      expect(axiosClient.interceptors).toBeDefined();
      expect(axiosClient.interceptors.request).toBeDefined();
      expect(axiosClient.interceptors.response).toBeDefined();
    });

    it('should have interceptor methods available', () => {
      const axiosClient = httpService.getAxiosClient();

      expect(typeof axiosClient.interceptors.request.use).toBe('function');
      expect(typeof axiosClient.interceptors.response.use).toBe('function');
    });

    it('should return same instance on multiple calls', () => {
      const axiosClient1 = httpService.getAxiosClient();
      const axiosClient2 = httpService.getAxiosClient();

      expect(axiosClient1).toBe(axiosClient2);
    });
  });

  describe('Error handling utilities', () => {
    it('should identify cancellation errors correctly', () => {
      const cancelError = new Error('Request cancelled');
      mockedAxios.isCancel.mockReturnValue(true);

      const result = mockedAxios.isCancel(cancelError);

      expect(result).toBe(true);
      expect(mockedAxios.isCancel).toHaveBeenCalledWith(cancelError);
    });

    it('should identify non-cancellation errors correctly', () => {
      const networkError = new Error('Network error');
      mockedAxios.isCancel.mockReturnValue(false);

      const result = mockedAxios.isCancel(networkError);

      expect(result).toBe(false);
      expect(mockedAxios.isCancel).toHaveBeenCalledWith(networkError);
    });
  });

  describe('cancelPreviousRequest', () => {
    let originalAbortController: typeof AbortController;
    let mockAbortController: jest.Mocked<AbortController>;

    beforeEach(() => {
      // Mock AbortController
      originalAbortController = global.AbortController;
      mockAbortController = {
        abort: jest.fn(),
        signal: {
          aborted: false,
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
          onabort: null,
          reason: undefined,
          throwIfAborted: jest.fn(),
        } as AbortSignal,
      };
      global.AbortController = jest.fn(() => mockAbortController) as any;
    });

    afterEach(() => {
      global.AbortController = originalAbortController;
    });

    it('should create new controller for first request to endpoint', () => {
      const signal = httpService.cancelPreviousRequest('test-endpoint');

      expect(global.AbortController).toHaveBeenCalledTimes(1);
      expect(signal).toBe(mockAbortController.signal);
    });

    it('should cancel previous request and create new controller for same endpoint', () => {
      // First request
      const signal1 = httpService.cancelPreviousRequest('test-endpoint');
      expect(global.AbortController).toHaveBeenCalledTimes(1);

      // Second request to same endpoint
      const signal2 = httpService.cancelPreviousRequest('test-endpoint');

      expect(mockAbortController.abort).toHaveBeenCalledTimes(1);
      expect(global.AbortController).toHaveBeenCalledTimes(2);
      expect(signal2).toBe(mockAbortController.signal);
    });

    it('should handle multiple different endpoints independently', () => {
      const signal1 = httpService.cancelPreviousRequest('endpoint-1');
      const signal2 = httpService.cancelPreviousRequest('endpoint-2');

      expect(global.AbortController).toHaveBeenCalledTimes(2);
      expect(mockAbortController.abort).not.toHaveBeenCalled();
      expect(signal1).toBe(mockAbortController.signal);
      expect(signal2).toBe(mockAbortController.signal);
    });

    it('should only cancel controller for specific endpoint', () => {
      // Setup requests for two endpoints
      httpService.cancelPreviousRequest('endpoint-1');
      httpService.cancelPreviousRequest('endpoint-2');

      // Reset mock to track new calls
      jest.clearAllMocks();

      // Cancel only endpoint-1
      httpService.cancelPreviousRequest('endpoint-1');

      expect(mockAbortController.abort).toHaveBeenCalledTimes(1);
      expect(global.AbortController).toHaveBeenCalledTimes(1);
    });

    it('should handle empty endpoint string', () => {
      const signal = httpService.cancelPreviousRequest('');

      expect(global.AbortController).toHaveBeenCalledTimes(1);
      expect(signal).toBe(mockAbortController.signal);
    });

    it('should handle special characters in endpoint names', () => {
      const endpoints = [
        'endpoint/with/slashes',
        'endpoint-with-dashes',
        'endpoint_with_underscores',
        'endpoint with spaces',
        'endpoint?with=query&params=true',
      ];

      endpoints.forEach(endpoint => {
        const signal = httpService.cancelPreviousRequest(endpoint);
        expect(signal).toBe(mockAbortController.signal);
      });

      expect(global.AbortController).toHaveBeenCalledTimes(endpoints.length);
    });
  });

  describe('Integration Tests', () => {
    it('should maintain separate axios instance from global axios', () => {
      const axiosClient = httpService.getAxiosClient();

      expect(axiosClient).not.toBe(axios);
      expect(axiosClient).toBeDefined();
    });

    it('should have all required methods on axios instance', () => {
      const axiosClient = httpService.getAxiosClient();

      expect(axiosClient.get).toBeDefined();
      expect(axiosClient.post).toBeDefined();
      expect(axiosClient.delete).toBeDefined();
      expect(axiosClient.interceptors).toBeDefined();
    });
  });

  describe('Module structure', () => {
    it('should export all required properties', () => {
      expect(httpService).toHaveProperty('HttpMethods');
      expect(httpService).toHaveProperty('getAxiosClient');
      expect(httpService).toHaveProperty('axios');
      expect(httpService).toHaveProperty('cancelPreviousRequest');
    });

    it('should have HttpMethods as an object with string values', () => {
      const methods = httpService.HttpMethods;

      expect(typeof methods).toBe('object');
      expect(typeof methods.GET).toBe('string');
      expect(typeof methods.POST).toBe('string');
      expect(typeof methods.DELETE).toBe('string');
    });

    it('should have getAxiosClient as a function', () => {
      expect(typeof httpService.getAxiosClient).toBe('function');
    });

    it('should have cancelPreviousRequest as a function', () => {
      expect(typeof httpService.cancelPreviousRequest).toBe('function');
    });
  });

  describe('Interceptor functionality verification', () => {
    it('should have interceptors configured on axios instance', () => {
      const axiosClient = httpService.getAxiosClient();

      // Verify interceptors exist and have the use method
      expect(axiosClient.interceptors.request.use).toBeDefined();
      expect(axiosClient.interceptors.response.use).toBeDefined();

      // Verify they are functions (indicating they've been set up)
      expect(typeof axiosClient.interceptors.request.use).toBe('function');
      expect(typeof axiosClient.interceptors.response.use).toBe('function');
    });

    it('should handle axios isCancel method correctly', () => {
      // Test with a cancellation error
      const cancelError = new Error('Request cancelled');
      mockedAxios.isCancel.mockReturnValue(true);

      expect(mockedAxios.isCancel(cancelError)).toBe(true);

      // Test with a regular error
      const regularError = new Error('Network error');
      mockedAxios.isCancel.mockReturnValue(false);

      expect(mockedAxios.isCancel(regularError)).toBe(false);
    });
  });

  describe('Edge cases and error scenarios', () => {
    it('should handle multiple calls to getAxiosClient', () => {
      const client1 = httpService.getAxiosClient();
      const client2 = httpService.getAxiosClient();
      const client3 = httpService.getAxiosClient();

      expect(client1).toBe(client2);
      expect(client2).toBe(client3);
      expect(client1).toBe(client3);
    });

    it('should handle cancelPreviousRequest with various endpoint formats', () => {
      const endpoints = [
        'simple-endpoint',
        '/api/endpoint',
        'https://example.com/api/endpoint',
        'endpoint-with-123-numbers',
        'endpoint_with_underscores',
        'endpoint.with.dots',
      ];

      endpoints.forEach(endpoint => {
        const signal = httpService.cancelPreviousRequest(endpoint);
        expect(signal).toBeDefined();
        expect(signal.aborted).toBe(false);
      });
    });

    it('should maintain separate controllers for different endpoints', () => {
      const endpoint1 = 'endpoint-1';
      const endpoint2 = 'endpoint-2';

      const signal1 = httpService.cancelPreviousRequest(endpoint1);
      const signal2 = httpService.cancelPreviousRequest(endpoint2);

      expect(signal1).toBeDefined();
      expect(signal2).toBeDefined();
      expect(signal1).not.toBe(signal2);
    });
  });

  describe('httpService uncovered/error branches', () => {
    let axiosInstance: any;
    let origCreate: any;
    let origGetToken: any;
    beforeEach(() => {
      jest.resetModules();
      origCreate = require('axios').create;
      origGetToken = require('@paris2/auth').getToken;
      axiosInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() },
        },
        get: jest.fn(),
        post: jest.fn(),
        patch: jest.fn(),
        delete: jest.fn(),
      };
      require('axios').create = jest.fn(() => axiosInstance);
    });
    afterEach(() => {
      require('axios').create = origCreate;
      require('@paris2/auth').getToken = origGetToken;
    });

    it('request interceptor adds Authorization if token present', async () => {
      require('@paris2/auth').getToken = jest.fn(() => Promise.resolve('tok123'));
      const httpService = require('../../src/services/http-service').default;
      const config = { headers: { 'X-Test': '1' } };
      const result = await axiosInstance.interceptors.request.use.mock.calls[0][0](config);
      expect(result.headers.Authorization).toBe('Bearer tok123');
      expect(result.headers['X-Test']).toBe('1');
    });

    it('request interceptor leaves headers if no token', async () => {
      require('@paris2/auth').getToken = jest.fn(() => Promise.resolve(undefined));
      const httpService = require('../../src/services/http-service').default;
      const config = { headers: { 'X-Test': '1' } };
      const result = await axiosInstance.interceptors.request.use.mock.calls[0][0](config);
      expect(result.headers['X-Test']).toBe('1');
    });

    it('request interceptor passes through error', async () => {
      const httpService = require('../../src/services/http-service').default;
      const error = new Error('fail');
      const out = await axiosInstance.interceptors.request.use.mock.calls[0][1](error).catch(e => e);
      expect(out).toBe(error);
    });

    it('response interceptor returns response', async () => {
      const httpService = require('../../src/services/http-service').default;
      const resp = { data: 1 };
      const out = await axiosInstance.interceptors.response.use.mock.calls[0][0](resp);
      expect(out).toBe(resp);
    });

    it('response interceptor handles axios.isCancel', async () => {
      require('axios').isCancel = jest.fn(() => true);
      const httpService = require('../../src/services/http-service').default;
      const error = new Error('cancelled');
      const out = await axiosInstance.interceptors.response.use.mock.calls[0][1](error).catch(e => e);
      expect(out.message).toBe('Request cancelled');
    });

    it('response interceptor passes through non-cancel errors', async () => {
      require('axios').isCancel = jest.fn(() => false);
      const httpService = require('../../src/services/http-service').default;
      const error = new Error('fail');
      const out = await axiosInstance.interceptors.response.use.mock.calls[0][1](error).catch(e => e);
      expect(out).toBe(error);
    });

    it('cancelPreviousRequest aborts and replaces controllers', () => {
      const httpService = require('../../src/services/http-service').default;
      const signal1 = httpService.cancelPreviousRequest('ep1');
      const signal2 = httpService.cancelPreviousRequest('ep1');
      expect(signal1).not.toBe(signal2);
    });
  });
});
