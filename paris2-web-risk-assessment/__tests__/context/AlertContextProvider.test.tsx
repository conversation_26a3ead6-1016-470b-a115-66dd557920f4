import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import AlertContextProvider, {
  AlertContext,
  useAlertContext,
} from '../../src/context/AlertContextProvider';

// A helper component to test context usage
const TestComponent = () => {
  const {alert} = useAlertContext();
  return (
    <>
      <button onClick={() => alert('success', 'Success message')}>
        Show Success Alert
      </button>
    </>
  );
};

describe('AlertContextProvider', () => {
  it('renders children correctly', () => {
    render(
      <AlertContextProvider>
        <div>Child content</div>
      </AlertContextProvider>,
    );
    expect(screen.getByText('Child content')).toBeInTheDocument();
  });

  it('shows an alert when alert() is called', () => {
    render(
      <AlertContextProvider>
        <TestComponent />
      </AlertContextProvider>,
    );

    fireEvent.click(screen.getByText('Show Success Alert'));

    expect(screen.getByText('Success message')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toHaveClass('custom-alert');
    expect(screen.getByRole('alert')).toHaveClass('alert-success'); // Bootstrap alert-success class applied for variant success
  });

  it('alertDetails is null initially', () => {
    let contextValue: any = null;

    const Consumer = () => {
      contextValue = React.useContext(AlertContext);
      return null;
    };

    render(
      <AlertContextProvider>
        <Consumer />
      </AlertContextProvider>,
    );

    expect(contextValue.alertDetails).toBeNull();
  });
});
