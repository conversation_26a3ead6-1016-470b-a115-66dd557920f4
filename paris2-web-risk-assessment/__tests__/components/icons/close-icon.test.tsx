import React from 'react';
import {render} from '@testing-library/react';
import CloseIcon from '../../../src/components/icons/close-icon';

describe('CloseIcon', () => {
  it('renders without crashing', () => {
    const {container} = render(<CloseIcon />);
    expect(container.querySelector('svg')).toBeInTheDocument();
  });

  it('applies passed props correctly', () => {
    const {container} = render(
      <CloseIcon data-testid="close-icon" className="custom-class" />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('data-testid', 'close-icon');
    expect(svg).toHaveClass('custom-class');
  });

  it('has correct width, height and viewBox attributes', () => {
    const {container} = render(<CloseIcon />);
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '20');
    expect(svg).toHaveAttribute('height', '20');
    expect(svg).toHaveAttribute('viewBox', '0 0 20 20');
  });
});
