import React from 'react';
import {render, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import ThreeDotsMenu from '../../../src/components/icons/three-dot-menu-icon';

describe('ThreeDotsMenu', () => {
  it('renders with default size and color', () => {
    const {container} = render(<ThreeDotsMenu />);
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '20');
    expect(svg).toHaveAttribute('height', '20');
    const paths = container.querySelectorAll('path');
    expect(paths.length).toBe(3);
    paths.forEach(path => {
      expect(path).toHaveAttribute('fill', '#1C1F4A');
    });
  });

  it('applies custom width, height, and color', () => {
    const {container} = render(
      <ThreeDotsMenu width={24} height={24} color="#ff0000" />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '24');
    expect(svg).toHaveAttribute('height', '24');
    const paths = container.querySelectorAll('path');
    paths.forEach(path => {
      expect(path).toHaveAttribute('fill', '#ff0000');
    });
  });

  it('applies custom className', () => {
    const {container} = render(<ThreeDotsMenu className="menu-icon" />);
    const svg = container.querySelector('svg');
    expect(svg).toHaveClass('menu-icon');
  });

  it('triggers onClick when clicked', () => {
    const handleClick = jest.fn();
    const {container} = render(<ThreeDotsMenu onClick={handleClick} />);
    const svg = container.querySelector('svg');
    fireEvent.click(svg!);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
