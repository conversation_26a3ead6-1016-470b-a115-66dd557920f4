import {render} from '@testing-library/react';
import {
  PlusIcon,
  ThreeDotsMenuIcon,
  CloseIcon,
  CalendarIcon,
  TrashIcon,
  ExternalLinkIcon,
} from '../../../src/components/icons/index';

describe('Icons Index', () => {
  it('exports PlusIcon component', () => {
    expect(PlusIcon).toBeDefined();
    expect(typeof PlusIcon).toBe('function');
  });

  it('exports ThreeDotsMenuIcon component', () => {
    expect(ThreeDotsMenuIcon).toBeDefined();
    expect(typeof ThreeDotsMenuIcon).toBe('function');
  });

  it('exports CloseIcon component', () => {
    expect(CloseIcon).toBeDefined();
    expect(typeof CloseIcon).toBe('function');
  });

  it('exports CalendarIcon component', () => {
    expect(CalendarIcon).toBeDefined();
    expect(typeof CalendarIcon).toBe('function');
  });

  it('exports TrashIcon component', () => {
    expect(TrashIcon).toBeDefined();
    expect(typeof TrashIcon).toBe('function');
  });

  it('exports ExternalLinkIcon component', () => {
    expect(ExternalLinkIcon).toBeDefined();
    expect(typeof ExternalLinkIcon).toBe('function');
  });

  it('all exported components are React components', () => {
    const components = [
      PlusIcon,
      ThreeDotsMenuIcon,
      CloseIcon,
      CalendarIcon,
      TrashIcon,
      ExternalLinkIcon,
    ];

    components.forEach(Component => {
      expect(typeof Component).toBe('function');
      // Check that it's a React component by checking if it has a displayName or can be called
      expect(Component.length).toBeGreaterThanOrEqual(0); // React components accept props
    });
  });

  it('can render all exported components', () => {
    // This test ensures that all exports from the index file are actually usable
    const components = [
      PlusIcon,
      ThreeDotsMenuIcon,
      CloseIcon,
      CalendarIcon,
      TrashIcon,
      ExternalLinkIcon,
    ];

    components.forEach(Component => {
      const {container} = render(<Component />);
      expect(container.querySelector('svg')).toBeInTheDocument();
    });
  });
});
