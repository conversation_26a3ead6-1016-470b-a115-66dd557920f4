import React from 'react';
import { render } from '@testing-library/react';
import SvgUserOutline from '../../../src/components/icons/reassign-user-icon';

describe('SvgUserOutline (reassign-user-icon)', () => {
  it('renders an SVG element with correct attributes', () => {
    const { container } = render(<SvgUserOutline data-testid="user-icon" />);
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '16');
    expect(svg).toHaveAttribute('height', '16');
    expect(svg).toHaveAttribute('viewBox', '0 0 16 16');
    expect(svg).toHaveAttribute('fill', 'none');
    expect(svg).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
  });

  it('spreads additional props to the svg element', () => {
    const { getByTestId } = render(
      <SvgUserOutline data-testid="user-icon" aria-label="user icon" />
    );
    const svg = getByTestId('user-icon');
    expect(svg).toHaveAttribute('aria-label', 'user icon');
  });

  it('renders all expected <path> elements', () => {
    const { container } = render(<SvgUserOutline />);
    const paths = container.querySelectorAll('svg path');
    // The icon should have at least one path (actual count may vary)
    expect(paths.length).toBeGreaterThan(0);
  });
});
