import React from 'react';
import {render, fireEvent, screen} from '@testing-library/react';
import CustomDatePickerWithRange from '../../src/components/CustomDatePickerWithRange';
import {format} from 'date-fns';

// Mocked dates (prefixed with `mock` to be safe)
const mockStartDate = new Date('2023-01-01');
const mockEndDate = new Date('2023-01-15');
const mockMinDate = new Date('2022-12-01');
const mockMaxDate = new Date('2023-12-31');

// Mock react-datepicker
jest.mock('react-datepicker', () => {
  const React = require('react');
  return {
    __esModule: true,
    default: ({onChange, ...props}: any) => {
      // Expose the onChange handler through a test helper
      const handleDateSelect = () => onChange([mockStartDate, mockEndDate]);
      const handleClear = () => onChange([null, null]);

      return (
        <div data-testid="mock-datepicker-wrapper">
          <input
            data-testid="mock-datepicker"
            aria-label={props['aria-label']}
            placeholder={props.placeholderText}
            value={props.selected ? props.selected.toDateString() : ''}
            onChange={handleDateSelect}
            onKeyDown={props.onKeyDown}
            readOnly
          />
          <button
            data-testid="mock-datepicker-select"
            onClick={handleDateSelect}
          >
            Select Date
          </button>
          <button data-testid="mock-datepicker-clear" onClick={handleClear}>
            Clear
          </button>
        </div>
      );
    },
  };
});

describe('CustomDatePickerWithRange', () => {
  const defaultProps = {
    label: 'Date Range',
    onChange: jest.fn(),
    controlId: 'date-picker',
    placeholder: 'Select Date Range',
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders with label and placeholder', () => {
    render(<CustomDatePickerWithRange {...defaultProps} />);

    expect(screen.getByLabelText('Date Range')).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText('Select Date Range'),
    ).toBeInTheDocument();
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
  });

  test('displays clear icon when start or end date exists', () => {
    render(
      <CustomDatePickerWithRange
        {...defaultProps}
        startDate={mockStartDate}
        endDate={mockEndDate}
      />,
    );

    expect(screen.getByTestId('clear-icon')).toBeInTheDocument();
  });

  test('clears the date range when clear icon is clicked', () => {
    render(
      <CustomDatePickerWithRange
        {...defaultProps}
        startDate={mockStartDate}
        endDate={mockEndDate}
      />,
    );

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(defaultProps.onChange).toHaveBeenCalledWith([undefined, undefined]);
  });

  test('does not show clear icon when no dates selected', () => {
    render(<CustomDatePickerWithRange {...defaultProps} />);
    expect(screen.queryByTestId('clear-icon')).not.toBeInTheDocument();
  });

  test('applies aria-label for accessibility', () => {
    render(<CustomDatePickerWithRange {...defaultProps} />);
    expect(screen.getByLabelText('Date Range')).toBeInTheDocument();
  });

  test('handles date selection and formats dates correctly', () => {
    const onChangeMock = jest.fn();
    render(
      <CustomDatePickerWithRange {...defaultProps} onChange={onChangeMock} />,
    );

    // Use the select button to trigger date selection
    const selectButton = screen.getByTestId('mock-datepicker-select');
    fireEvent.click(selectButton);

    const expectedStartDate = format(mockStartDate, 'yyyy-MM-dd');
    const expectedEndDate = format(mockEndDate, 'yyyy-MM-dd');
    expect(onChangeMock).toHaveBeenCalledWith([
      expectedStartDate,
      expectedEndDate,
    ]);
  });

  test('handles null date values correctly', () => {
    const onChangeMock = jest.fn();
    render(
      <CustomDatePickerWithRange {...defaultProps} onChange={onChangeMock} />,
    );

    const clearButton = screen.getByTestId('mock-datepicker-clear');
    fireEvent.click(clearButton);

    expect(onChangeMock).toHaveBeenCalledWith([undefined, undefined]);
  });

  test('prevents keyboard input', () => {
    render(<CustomDatePickerWithRange {...defaultProps} />);
    const datePicker = screen.getByTestId('mock-datepicker');
    const event = new KeyboardEvent('keydown', {key: 'A'});

    const prevented = !fireEvent.keyDown(datePicker, event);
    expect(prevented).toBe(true);
  });

  test('respects min and max date constraints', () => {
    render(
      <CustomDatePickerWithRange
        {...defaultProps}
        minDate={mockMinDate}
        maxDate={mockMaxDate}
      />,
    );

    const datePicker = screen.getByTestId('mock-datepicker');
    expect(datePicker).toBeInTheDocument();
  });

  test('clear button has appropriate accessibility label', () => {
    render(
      <CustomDatePickerWithRange
        {...defaultProps}
        startDate={mockStartDate}
        endDate={mockEndDate}
      />,
    );

    const clearButton = screen.getByLabelText('Clear date range');
    expect(clearButton).toBeInTheDocument();
  });

  test('calendar icon has appropriate accessibility label', () => {
    render(<CustomDatePickerWithRange {...defaultProps} />);

    const calendarIcon = screen.getByLabelText('Select date range');
    expect(calendarIcon).toBeInTheDocument();
  });
});
