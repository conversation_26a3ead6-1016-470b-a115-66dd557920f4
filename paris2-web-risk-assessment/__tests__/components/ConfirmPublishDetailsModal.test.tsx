import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {ConfirmPublishDetailsModal} from '../../src/components/ConfirmPublishDetailsModal';

// Mock useDataStoreContext to prevent provider errors
jest.mock('../../src/context', () => ({
  ...jest.requireActual('../../src/context'),
  useDataStoreContext: () => ({
    setDataStore: jest.fn(),
    ga4EventTrigger: jest.fn(),
    roleConfig: {riskAssessment: {canExportPDF: false}},
  }),
}));

describe('ConfirmPublishDetailsModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSave = jest.fn();

  const renderModal = (keywords: string[] = ['Initial']) => {
    render(
      <ConfirmPublishDetailsModal
        onClose={mockOnClose}
        onSave={mockOnSave}
        keywords={keywords}
      />,
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal with initial keywords', () => {
    renderModal(['React', 'TypeScript']);
    expect(screen.getByText('Confirm Details to Publish')).toBeInTheDocument();
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('TypeScript')).toBeInTheDocument();
  });

  it('adds a new keyword on Enter key press', () => {
    renderModal();
    const input = screen.getByPlaceholderText(/Type the Keyword/i);
    fireEvent.change(input, {target: {value: 'NewKeyword'}});
    fireEvent.keyDown(input, {key: 'Enter', code: 'Enter'});
    expect(screen.getByText('NewKeyword')).toBeInTheDocument();
  });

  it('does not add duplicate keywords', () => {
    renderModal(['React']);
    const input = screen.getByPlaceholderText(/Type the Keyword/i);
    fireEvent.change(input, {target: {value: 'React'}});
    fireEvent.keyDown(input, {key: 'Enter', code: 'Enter'});
    const badges = screen.getAllByText('React');
    expect(badges.length).toBe(1); // only one instance
  });

  it('removes keyword when X icon is clicked', () => {
    renderModal(['DeleteMe']);
    const removeIcon = screen.getByTestId('remove-keyword');
    fireEvent.click(removeIcon);
    expect(screen.queryByText('DeleteMe')).not.toBeInTheDocument();
  });

  it('calls onSave with keywords and closes on confirm', () => {
    renderModal(['Final']);
    const confirmButton = screen.getByText('Confirm');
    fireEvent.click(confirmButton);
    expect(mockOnSave).toHaveBeenCalledWith(['Final']);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onClose without saving on cancel', () => {
    renderModal(['Any']);
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnClose).toHaveBeenCalled();
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('disables confirm button when no keywords', () => {
    renderModal([]);
    const confirmButton = screen.getByText('Confirm') as HTMLButtonElement;
    expect(confirmButton.disabled).toBe(true);
  });

  it('enables confirm button when keywords are present', () => {
    renderModal(['Valid']);
    const confirmButton = screen.getByText('Confirm') as HTMLButtonElement;
    expect(confirmButton.disabled).toBe(false);
  });

  it('adds keyword only when Enter key is pressed and input is not empty', () => {
    renderModal();

    const input = screen.getByPlaceholderText(/Type the Keyword/i);

    fireEvent.change(input, {target: {value: 'NewKeyword'}});
    fireEvent.keyDown(input, {key: 'Enter', code: 'Enter'});
    expect(screen.getByText('NewKeyword')).toBeInTheDocument();
  });

  it('does not add keyword on other key presses', () => {
    renderModal();
    const input = screen.getByPlaceholderText(/Type the Keyword/i);
    fireEvent.change(input, {target: {value: 'Another'}});
    fireEvent.keyDown(input, {key: 'Space', code: 'Space'});
    expect(screen.queryByText('Another')).not.toBeInTheDocument();
  });
});
