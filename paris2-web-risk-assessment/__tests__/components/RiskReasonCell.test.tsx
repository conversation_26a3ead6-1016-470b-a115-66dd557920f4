import React from 'react';
import {render, screen} from '@testing-library/react';
import {RiskReasonCell} from '../../src/components/RiskReasonCell';

describe('RiskReasonCell', () => {
  it('renders four rows for parameter_type_id 1-4', () => {
    render(<RiskReasonCell rating={[]} />);
    const spans = screen.getAllByText('---');
    expect(spans).toHaveLength(4);
  });

  it('renders reasons for matching parameter_type_id', () => {
    const rating = [
      {parameter_type_id: 1, reason: 'Reason 1'},
      {parameter_type_id: 2, reason: 'Reason 2'},
      {parameter_type_id: 3, reason: 'Reason 3'},
      {parameter_type_id: 4, reason: 'Reason 4'},
    ];
    render(<RiskReasonCell rating={rating} />);
    expect(screen.getByText('Reason 1')).toBeInTheDocument();
    expect(screen.getByText('Reason 2')).toBeInTheDocument();
    expect(screen.getByText('Reason 3')).toBeInTheDocument();
    expect(screen.getByText('Reason 4')).toBeInTheDocument();
  });

  it('renders <hr> between rows except after the last', () => {
    render(<RiskReasonCell rating={[]} />);
    const hrs = screen.getAllByRole('separator');
    expect(hrs).toHaveLength(3);
  });

  it('renders correct order and keys', () => {
    const rating = [
      {parameter_type_id: 3, reason: 'Three'},
      {parameter_type_id: 1, reason: 'One'},
    ];
    render(<RiskReasonCell rating={rating} />);
    const spans = screen.getAllByText(/One|Three|---/);
    // Should render in order: 1,2,3,4
    expect(spans[0]).toHaveTextContent('One');
    expect(spans[1]).toHaveTextContent('---');
    expect(spans[2]).toHaveTextContent('Three');
    expect(spans[3]).toHaveTextContent('---');
  });
});
