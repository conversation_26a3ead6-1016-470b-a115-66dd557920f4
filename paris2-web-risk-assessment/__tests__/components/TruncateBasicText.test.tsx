import React from 'react';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TruncateText from '../../src/components/TruncateBasicText';

describe('TruncateText', () => {
  it('renders full text when text length is within maxLength', () => {
    render(<TruncateText text="Hello" maxLength={10} />);
    expect(screen.getByText('Hello')).toBeInTheDocument();
  });

  it('renders truncated text when text length exceeds maxLength', () => {
    render(<TruncateText text="Hello, this is a long text." maxLength={10} />);
    expect(screen.getByText('Hello, thi...')).toBeInTheDocument();
  });

  it('does not show popover if text is not truncated', async () => {
    render(<TruncateText text="Short" maxLength={10} />);
    const span = screen.getByText('Short');
    await userEvent.hover(span);
    expect(screen.queryByText('Short')).toBeInTheDocument(); // Still visible, but not from popover
  });

  it('shows full text in popover on hover when text is truncated', async () => {
    const fullText = 'This is a very long string that should be truncated.';
    render(<TruncateText text={fullText} maxLength={10} />);
    const trigger = screen.getByText('This is a ...');

    await userEvent.hover(trigger);

    const popoverText = await screen.findByText(fullText);
    expect(popoverText).toBeInTheDocument();
  });
});
