import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {DiscardDraftModal} from '../../src/components/DiscardDraftModal';
import {deleteRiskById, deleteTemplateById} from '../../src/services/services';

// Mock the services
jest.mock('../../src/services/services', () => ({
  deleteRiskById: jest.fn(),
  deleteTemplateById: jest.fn(),
}));

// Mock console.error to avoid noise in tests
const mockConsoleError = jest
  .spyOn(console, 'error')
  .mockImplementation(() => {});

describe('DiscardDraftModal', () => {
  const mockOnClose = jest.fn();
  const mockDeleteRiskById = deleteRiskById as jest.MockedFunction<
    typeof deleteRiskById
  >;
  const mockDeleteTemplateById = deleteTemplateById as jest.MockedFunction<
    typeof deleteTemplateById
  >;

  const defaultProps = {
    onClose: mockOnClose,
    id: 123,
    activeTab: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockConsoleError.mockClear();
  });

  afterAll(() => {
    mockConsoleError.mockRestore();
  });

  describe('Component Rendering', () => {
    it('renders modal with correct title and content', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      expect(screen.getByText('Discard Draft')).toBeInTheDocument();
      expect(
        screen.getByText('Are you sure you want to discard this draft?'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('All entered information will be lost permanently.'),
      ).toBeInTheDocument();
    });

    it('renders modal with correct buttons', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      expect(screen.getByRole('button', {name: 'Discard'})).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
    });

    it('renders modal with correct attributes', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();

      const alertElement = screen.getByTestId('error-alert');
      expect(alertElement).toHaveClass(
        'alert',
        'd-flex',
        'align-items-center',
        'fs-14',
        'ra-alert-warning',
      );
      expect(alertElement).toHaveAttribute('role', 'alert');
    });

    it('renders buttons with correct classes', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      const cancelButton = screen.getByRole('button', {name: 'Cancel'});

      expect(discardButton).toHaveClass('me-2', 'fs-14');
      expect(cancelButton).toHaveClass('me-2', 'fs-14');
    });
  });

  describe('Button States', () => {
    it('enables both buttons initially', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      const cancelButton = screen.getByRole('button', {name: 'Cancel'});

      expect(discardButton).not.toBeDisabled();
      expect(cancelButton).not.toBeDisabled();
    });

    it('disables both buttons when deleting', async () => {
      mockDeleteRiskById.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100)),
      );

      render(<DiscardDraftModal {...defaultProps} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      const cancelButton = screen.getByRole('button', {name: 'Cancel'});

      fireEvent.click(discardButton);

      expect(discardButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();

      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('Cancel Functionality', () => {
    it('calls onClose without parameters when cancel is clicked', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const cancelButton = screen.getByRole('button', {name: 'Cancel'});
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledWith();
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('does not call delete services when cancel is clicked', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const cancelButton = screen.getByRole('button', {name: 'Cancel'});
      fireEvent.click(cancelButton);

      expect(mockDeleteRiskById).not.toHaveBeenCalled();
      expect(mockDeleteTemplateById).not.toHaveBeenCalled();
    });
  });

  describe('Delete Risk Functionality (activeTab !== 2)', () => {
    it('calls deleteRiskById when activeTab is 1 and discard is clicked', async () => {
      mockDeleteRiskById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} activeTab={1} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(123);
        expect(mockDeleteTemplateById).not.toHaveBeenCalled();
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('calls deleteRiskById when activeTab is 0 and discard is clicked', async () => {
      mockDeleteRiskById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} activeTab={0} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(123);
        expect(mockDeleteTemplateById).not.toHaveBeenCalled();
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('calls deleteRiskById when activeTab is 3 and discard is clicked', async () => {
      mockDeleteRiskById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} activeTab={3} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(123);
        expect(mockDeleteTemplateById).not.toHaveBeenCalled();
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('Delete Template Functionality (activeTab === 2)', () => {
    it('calls deleteTemplateById when activeTab is 2 and discard is clicked', async () => {
      mockDeleteTemplateById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} activeTab={2} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteTemplateById).toHaveBeenCalledWith(123);
        expect(mockDeleteRiskById).not.toHaveBeenCalled();
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('Error Handling', () => {
    it('handles deleteRiskById error and still calls onClose', async () => {
      const mockError = new Error('Delete risk failed');
      mockDeleteRiskById.mockRejectedValue(mockError);

      render(<DiscardDraftModal {...defaultProps} activeTab={1} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(123);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('handles deleteTemplateById error and still calls onClose', async () => {
      const mockError = new Error('Delete template failed');
      mockDeleteTemplateById.mockRejectedValue(mockError);

      render(<DiscardDraftModal {...defaultProps} activeTab={2} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteTemplateById).toHaveBeenCalledWith(123);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('resets isDeleting state after error', async () => {
      const mockError = new Error('Network error');
      mockDeleteRiskById.mockRejectedValue(mockError);

      render(<DiscardDraftModal {...defaultProps} activeTab={1} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      const cancelButton = screen.getByRole('button', {name: 'Cancel'});

      fireEvent.click(discardButton);

      // Buttons should be disabled during deletion
      expect(discardButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();

      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('Props Validation', () => {
    it('works with different id values', async () => {
      mockDeleteRiskById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} id={456} activeTab={1} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(456);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('works with id value 0', async () => {
      mockDeleteRiskById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} id={0} activeTab={1} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(0);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('works with negative id values', async () => {
      mockDeleteTemplateById.mockResolvedValue({});

      render(<DiscardDraftModal {...defaultProps} id={-1} activeTab={2} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      await waitFor(() => {
        expect(mockDeleteTemplateById).toHaveBeenCalledWith(-1);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });

  describe('Modal Behavior', () => {
    it('renders modal with show prop set to true', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
    });

    it('renders modal body with correct class', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const modalBody = screen
        .getByText('Are you sure you want to discard this draft?')
        .closest('.modal-body');
      expect(modalBody).toHaveClass('complete-project-modal');
    });

    it('renders modal with proper structure', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      // Check that modal header, body, and footer are present
      expect(screen.getByText('Discard Draft')).toBeInTheDocument();
      expect(
        screen.getByText('Are you sure you want to discard this draft?'),
      ).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Discard'})).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper alert role for warning message', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const alertElement = screen.getByTestId('error-alert');
      expect(alertElement).toHaveAttribute('role', 'alert');
    });

    it('has proper modal structure', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Discard Draft')).toBeInTheDocument();
    });

    it('buttons are focusable', () => {
      render(<DiscardDraftModal {...defaultProps} />);

      const discardButton = screen.getByRole('button', {name: 'Discard'});
      const cancelButton = screen.getByRole('button', {name: 'Cancel'});

      expect(discardButton).toBeVisible();
      expect(cancelButton).toBeVisible();

      discardButton.focus();
      expect(discardButton).toHaveFocus();

      cancelButton.focus();
      expect(cancelButton).toHaveFocus();
    });
  });

  describe('Integration Tests', () => {
    it('completes full delete risk workflow', async () => {
      mockDeleteRiskById.mockResolvedValue({success: true});

      render(<DiscardDraftModal {...defaultProps} id={789} activeTab={1} />);

      // Verify initial state
      expect(screen.getByText('Discard Draft')).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Discard'})).not.toBeDisabled();
      expect(screen.getByRole('button', {name: 'Cancel'})).not.toBeDisabled();

      // Click discard
      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      // Verify loading state
      expect(discardButton).toBeDisabled();
      expect(screen.getByRole('button', {name: 'Cancel'})).toBeDisabled();

      // Wait for completion
      await waitFor(() => {
        expect(mockDeleteRiskById).toHaveBeenCalledWith(789);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });

    it('completes full delete template workflow', async () => {
      mockDeleteTemplateById.mockResolvedValue({success: true});

      render(<DiscardDraftModal {...defaultProps} id={999} activeTab={2} />);

      // Verify initial state
      expect(screen.getByText('Discard Draft')).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Discard'})).not.toBeDisabled();
      expect(screen.getByRole('button', {name: 'Cancel'})).not.toBeDisabled();

      // Click discard
      const discardButton = screen.getByRole('button', {name: 'Discard'});
      fireEvent.click(discardButton);

      // Verify loading state
      expect(discardButton).toBeDisabled();
      expect(screen.getByRole('button', {name: 'Cancel'})).toBeDisabled();

      // Wait for completion
      await waitFor(() => {
        expect(mockDeleteTemplateById).toHaveBeenCalledWith(999);
        expect(mockOnClose).toHaveBeenCalledWith(true);
      });
    });
  });
});
