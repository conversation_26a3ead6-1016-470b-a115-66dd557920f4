import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import GroupedCheckboxGrid from '../../src/components/GroupedCheckboxGrid';

const mockGroups = [
  {
    id: 1,
    label: 'Group A',
    options: [
      {id: 101, label: 'Option A1'},
      {id: 102, label: 'Option A2'},
      {id: 103, label: 'Option A3'},
    ],
    columns: 2,
  },
];

const mockValue = [
  {
    is_other: false,
    parameter_type_id: 1,
    parameter_id: [],
    value: '',
  },
];

describe('GroupedCheckboxGrid', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders title, subtitle and group labels', () => {
    render(
      <GroupedCheckboxGrid
        title="Test Title"
        subtitle="Test Subtitle"
        groups={mockGroups}
        value={mockValue}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
    expect(screen.getByText('Group A')).toBeInTheDocument();
    expect(screen.getByLabelText('Option A1')).toBeInTheDocument();
    expect(screen.getByLabelText('Option A2')).toBeInTheDocument();
    expect(screen.getByLabelText('Option A3')).toBeInTheDocument();
    expect(screen.getByLabelText('Others')).toBeInTheDocument();
  });

  it('calls onChange when a checkbox is selected', () => {
    render(
      <GroupedCheckboxGrid
        title="Title"
        subtitle="Subtitle"
        groups={mockGroups}
        value={mockValue}
        onChange={mockOnChange}
      />,
    );

    const checkbox = screen.getByLabelText('Option A1');
    fireEvent.click(checkbox);

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        parameter_type_id: 1,
        parameter_id: [101],
        is_other: false,
        value: '',
      }),
    ]);
  });

  it('toggles "Others" checkbox and inputs text', () => {
    render(
      <GroupedCheckboxGrid
        title="Title"
        subtitle="Subtitle"
        groups={mockGroups}
        value={mockValue}
        onChange={mockOnChange}
      />,
    );

    const othersCheckbox = screen.getByLabelText('Others');
    fireEvent.click(othersCheckbox);

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        is_other: true,
        value: '',
      }),
    ]);

    const input = screen.getByPlaceholderText('List the Group a at Risk');
    fireEvent.change(input, {target: {value: 'Custom option'}});

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        is_other: true,
        value: 'Custom option',
      }),
    ]);
  });

  it('trims "Others" input to max length', () => {
    render(
      <GroupedCheckboxGrid
        title="Title"
        subtitle="Subtitle"
        groups={mockGroups}
        value={mockValue}
        onChange={mockOnChange}
        othersMaxLength={5}
      />,
    );

    const othersCheckbox = screen.getByLabelText('Others');
    fireEvent.click(othersCheckbox);

    const input = screen.getByPlaceholderText('List the Group a at Risk');
    fireEvent.change(input, {target: {value: 'TooLongInput'}});

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        value: 'TooLo',
      }),
    ]);
  });

  it('removes "Others" text when unchecked', () => {
    render(
      <GroupedCheckboxGrid
        title="Title"
        subtitle="Subtitle"
        groups={mockGroups}
        value={[
          {
            parameter_type_id: 1,
            is_other: true,
            parameter_id: [],
            value: 'Some text',
          },
        ]}
        onChange={mockOnChange}
      />,
    );

    const othersCheckbox = screen.getByLabelText('Others');
    expect(screen.getByPlaceholderText('List the Group a at Risk')).toHaveValue(
      'Some text',
    );

    fireEvent.click(othersCheckbox); // Uncheck it

    expect(mockOnChange).toHaveBeenCalledWith([
      expect.objectContaining({
        is_other: false,
        value: '',
      }),
    ]);
  });
});
