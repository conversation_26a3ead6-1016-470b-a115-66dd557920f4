import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import FormCheckRadio from '../../src/components/FormCheckRadio';

jest.mock('../../src/utils/svgIcons', () => ({
  RadioCheckedIcon: () => <svg data-testid="checked-icon" />,
  RadioUncheckedIcon: () => <svg data-testid="unchecked-icon" />,
}));

describe('FormCheckRadio', () => {
  const defaultProps = {
    name: 'test-radio',
    value: 'option1',
    onChange: jest.fn(),
  };

  it('renders unchecked radio button', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} />);

    expect(screen.getByTestId('unchecked-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('checked-icon')).not.toBeInTheDocument();
  });

  it('renders checked radio button', () => {
    render(<FormCheckRadio {...defaultProps} checked={true} />);

    expect(screen.getByTestId('checked-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('unchecked-icon')).not.toBeInTheDocument();
  });

  it('renders the label when provided', () => {
    render(
      <FormCheckRadio {...defaultProps} checked={true} label="Option 1" />,
    );

    expect(screen.getByText('Option 1')).toBeInTheDocument();
  });

  it('does not render label when not provided', () => {
    render(<FormCheckRadio {...defaultProps} checked={true} />);

    expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
  });

  it('applies disabled styling when disabled', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} disabled={true} />);

    const label = document.querySelector('.form-check-radio');
    expect(label).toHaveStyle({ cursor: 'not-allowed' });
  });

  it('applies pointer cursor when not disabled', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} disabled={false} />);

    const label = document.querySelector('.form-check-radio');
    expect(label).toHaveStyle({ cursor: 'pointer' });
  });

  it('applies default pointer cursor when disabled prop is not provided', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} />);

    const label = document.querySelector('.form-check-radio');
    expect(label).toHaveStyle({ cursor: 'pointer' });
  });

  it('passes disabled prop to input element', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} disabled={true} />);

    const input = document.querySelector('input[type="radio"]') as HTMLInputElement;
    expect(input).toBeDisabled();
  });

  it('handles onChange event', () => {
    const mockOnChange = jest.fn();
    render(<FormCheckRadio {...defaultProps} checked={false} onChange={mockOnChange} />);

    const input = document.querySelector('input[type="radio"]') as HTMLInputElement;
    fireEvent.click(input);

    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });

  it('applies custom className', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} className="custom-class" />);

    const label = document.querySelector('.form-check-radio');
    expect(label).toHaveClass('custom-class');
  });

  it('uses custom id when provided', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} id="custom-id" />);

    const label = document.querySelector('#custom-id');
    expect(label).toBeInTheDocument();
  });

  it('uses default id when not provided', () => {
    render(<FormCheckRadio {...defaultProps} checked={false} />);

    const label = document.querySelector('#form-check-radio');
    expect(label).toBeInTheDocument();
  });
});
