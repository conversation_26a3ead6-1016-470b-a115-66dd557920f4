import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {UnitSelectInputComponent} from '../../src/components/UnitSelectInputComponent';

describe('UnitSelectInputComponent', () => {
  const defaultProps = {
    form: {
      deadweight: '1000',
      deadweightUnit: 'kg',
    },
    label: 'Deadweight',
    name: 'deadweight',
    unitFeildName: 'deadweightUnit',
    unitOptions: ['kg', 'tons'],
    onChange: jest.fn(),
    value: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders label and input with default value', () => {
    render(<UnitSelectInputComponent {...defaultProps} />);
    expect(screen.getByLabelText('Deadweight')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1000')).toBeInTheDocument();
  });

  it('renders unit options in select dropdown', () => {
    render(<UnitSelectInputComponent {...defaultProps} />);
    const select = screen.getByTestId('deadweight-unit-select');
    expect(select).toBeInTheDocument();
    expect(select).toHaveValue('kg');
    expect(select).toContainHTML('<option value="kg">kg</option>');
    expect(select).toContainHTML('<option value="tons">tons</option>');
  });

  it('calls onChange when input value changes', () => {
    render(<UnitSelectInputComponent {...defaultProps} />);
    const input = screen.getByRole('textbox');
    fireEvent.change(input, {target: {value: '1200'}});
    expect(defaultProps.onChange).toHaveBeenCalled();
  });

  it('calls onChange when select value changes', () => {
    render(<UnitSelectInputComponent {...defaultProps} />);
    const select = screen.getByTestId('deadweight-unit-select');
    fireEvent.change(select, {target: {value: 'tons'}});
    expect(defaultProps.onChange).toHaveBeenCalled();
  });

  it('displays error message if error prop is passed', () => {
    render(
      <UnitSelectInputComponent {...defaultProps} error="Required field" />,
    );
    expect(screen.getByText('Required field')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toHaveClass('is-invalid');
  });

  it('displays help text and max length counter', () => {
    render(
      <UnitSelectInputComponent
        {...defaultProps}
        helpText="This is help text"
        maxLength={10}
      />,
    );
    expect(screen.getByText('This is help text')).toBeInTheDocument();
    expect(screen.getByText('4/10')).toBeInTheDocument(); // '1000'.length = 4
  });

  it('respects disabled prop on input', () => {
    render(<UnitSelectInputComponent {...defaultProps} disabled />);
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('renders with custom placeholder', () => {
    render(
      <UnitSelectInputComponent {...defaultProps} placeholder="Enter weight" />,
    );
    expect(screen.getByPlaceholderText('Enter weight')).toBeInTheDocument();
  });
});
