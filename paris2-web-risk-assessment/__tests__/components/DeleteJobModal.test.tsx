import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {DeleteJobModal} from '../../src/components/DeleteJobModal';
import {TemplateForm} from '../../src/types/template';
import {RiskForm} from '../../src/types/risk';

describe('DeleteJobModal', () => {
  const mockSetForm = jest.fn();
  const mockOnClose = jest.fn();

  const mockForm: TemplateForm = {
    template_job: [
      {
        job_id: 'job-1',
        job_step: 'First Job Step',
        job_hazard: 'First Hazard',
      },
      {
        job_id: 'job-2',
        job_step: 'Second Job Step',
        job_hazard: 'Second Hazard',
      },
      {
        job_id: 'job-3',
        job_step: 'Third Job Step',
        job_hazard: 'Third Hazard',
      },
    ],
  } as TemplateForm;

  const defaultProps = {
    onClose: mockOnClose,
    jobId: 'job-2',
    form: mockForm,
    setForm: mockSetForm,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal with correct title and content', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    expect(screen.getByText('Deleting a Job Step')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete this job step?')).toBeInTheDocument();
    expect(screen.getByText('This action is permamnent and cannot be undone.')).toBeInTheDocument();
  });

  it('renders Delete Job Step and Cancel buttons', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    expect(screen.getByRole('button', {name: 'Delete Job Step'})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
  });

  it('displays correct job information for the selected job', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    // Should show JOB 2 (index 1 + 1)
    expect(screen.getByText('JOB 2')).toBeInTheDocument();
    expect(screen.getByText('Second Job Step - Second Hazard')).toBeInTheDocument();
  });

  it('displays correct job information for first job', () => {
    render(<DeleteJobModal {...defaultProps} jobId="job-1" />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    expect(screen.getByText('First Job Step - First Hazard')).toBeInTheDocument();
  });

  it('displays correct job information for last job', () => {
    render(<DeleteJobModal {...defaultProps} jobId="job-3" />);
    
    expect(screen.getByText('JOB 3')).toBeInTheDocument();
    expect(screen.getByText('Third Job Step - Third Hazard')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const cancelButton = screen.getByRole('button', {name: 'Cancel'});
    fireEvent.click(cancelButton);
    
    expect(mockOnClose).toHaveBeenCalledWith(false);
  });

  it('removes job from form and calls onClose when Delete Job Step is clicked', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);
    
    expect(mockOnClose).toHaveBeenCalledWith(true, expect.any(Object));
    
    // Component now calls onClose with updated form - no need to test setForm
  });

  it('handles empty template_job array', () => {
    const emptyForm = {...mockForm, template_job: []};
    render(<DeleteJobModal {...defaultProps} form={emptyForm} />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    // Should handle gracefully when no job exists
  });

  it('handles non-existent jobId', () => {
    render(<DeleteJobModal {...defaultProps} jobId="non-existent-job" />);
    
    // Should default to index 0
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
    expect(screen.getByText('First Job Step - First Hazard')).toBeInTheDocument();
  });

  it('handles undefined template_job', () => {
    const formWithoutJobs = {...mockForm, template_job: undefined};
    render(<DeleteJobModal {...defaultProps} form={formWithoutJobs} />);
    
    expect(screen.getByText('JOB 1')).toBeInTheDocument();
  });

  it('renders with correct CSS classes and structure', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const alert = screen.getByTestId('error-alert');
    expect(alert).toHaveClass('alert', 'd-flex', 'align-items-center', 'fs-14', 'ra-alert-warning');
    expect(alert).toHaveAttribute('role', 'alert');
    
    const deleteJobBorder = document.querySelector('.delete-job-border');
    expect(deleteJobBorder).toBeInTheDocument();
    
    const jobNumber = document.querySelector('.secondary-color.fs-14.fw-500');
    expect(jobNumber).toBeInTheDocument();
    
    const jobDetails = document.querySelector('.fs-16.fw-600');
    expect(jobDetails).toBeInTheDocument();
  });

  it('renders modal with correct properties', () => {
    render(<DeleteJobModal {...defaultProps} />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toBeInTheDocument();
    
    const modalBody = screen.getByText('Are you sure you want to delete this job step?').closest('.modal-body');
    expect(modalBody).toHaveClass('complete-project-modal');
  });

  it('handles job deletion when jobId is empty string', () => {
    render(<DeleteJobModal {...defaultProps} jobId="" />);
    
    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);
    
    // Should still call onClose even if jobId is empty
    expect(mockOnClose).toHaveBeenCalledWith(true, undefined);
  });

  it('preserves other form properties when deleting job', () => {
    const formWithOtherProps = {
      ...mockForm,
      template_name: 'Test Template',
      template_description: 'Test Description',
    };

    render(<DeleteJobModal {...defaultProps} form={formWithOtherProps} />);

    const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
    fireEvent.click(deleteButton);

    expect(mockOnClose).toHaveBeenCalledWith(true, expect.any(Object));
  });

  describe('Risk Form Tests', () => {
    const mockRiskForm: RiskForm = {
      risk_job: [
        {
          job_step: 'Risk Job Step 1',
          job_hazard: 'Risk Hazard 1',
          job_nature_of_risk: 'Risk Nature 1',
          job_existing_control: 'Risk Control 1',
          job_additional_mitigation: 'Risk Mitigation 1',
          job_close_out_date: '2024-01-01',
          job_close_out_responsibility_id: '1',
          risk_job_initial_risk_rating: [],
          risk_job_residual_risk_rating: [],
        },
        {
          job_step: 'Risk Job Step 2',
          job_hazard: 'Risk Hazard 2',
          job_nature_of_risk: 'Risk Nature 2',
          job_existing_control: 'Risk Control 2',
          job_additional_mitigation: 'Risk Mitigation 2',
          job_close_out_date: '2024-01-02',
          job_close_out_responsibility_id: '2',
          risk_job_initial_risk_rating: [],
          risk_job_residual_risk_rating: [],
        },
        {
          job_step: 'Risk Job Step 3',
          job_hazard: 'Risk Hazard 3',
          job_nature_of_risk: 'Risk Nature 3',
          job_existing_control: 'Risk Control 3',
          job_additional_mitigation: 'Risk Mitigation 3',
          job_close_out_date: '2024-01-03',
          job_close_out_responsibility_id: '3',
          risk_job_initial_risk_rating: [],
          risk_job_residual_risk_rating: [],
        },
      ],
    } as RiskForm;

    const riskFormProps = {
      onClose: mockOnClose,
      jobId: '1', // Index-based for risk forms
      form: mockRiskForm,
      setForm: mockSetForm,
      type: 'risk' as const,
    };

    it('renders modal correctly for risk form', () => {
      render(<DeleteJobModal {...riskFormProps} />);

      expect(screen.getByText('Deleting a Job Step')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete this job step?')).toBeInTheDocument();
      expect(screen.getByText('This action is permamnent and cannot be undone.')).toBeInTheDocument();
    });

    it('displays correct job information for risk form', () => {
      render(<DeleteJobModal {...riskFormProps} />);

      // Should show JOB 2 (index 1 + 1)
      expect(screen.getByText('JOB 2')).toBeInTheDocument();
      expect(screen.getByText('Risk Job Step 2 - Risk Hazard 2')).toBeInTheDocument();
    });

    it('displays correct job information for first risk job', () => {
      render(<DeleteJobModal {...riskFormProps} jobId="0" />);

      expect(screen.getByText('JOB 1')).toBeInTheDocument();
      expect(screen.getByText('Risk Job Step 1 - Risk Hazard 1')).toBeInTheDocument();
    });

    it('displays correct job information for last risk job', () => {
      render(<DeleteJobModal {...riskFormProps} jobId="2" />);

      expect(screen.getByText('JOB 3')).toBeInTheDocument();
      expect(screen.getByText('Risk Job Step 3 - Risk Hazard 3')).toBeInTheDocument();
    });

    it('removes job from risk form and calls onClose when Delete Job Step is clicked', () => {
      render(<DeleteJobModal {...riskFormProps} />);

      const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
      fireEvent.click(deleteButton);

      expect(mockOnClose).toHaveBeenCalledWith(true, expect.any(Object));
    });

    it('handles invalid jobId for risk form', () => {
      render(<DeleteJobModal {...riskFormProps} jobId="invalid" />);

      // Should default to index 0
      expect(screen.getByText('JOB 1')).toBeInTheDocument();
      expect(screen.getByText('Risk Job Step 1 - Risk Hazard 1')).toBeInTheDocument();
    });

    it('handles out of bounds jobId for risk form', () => {
      render(<DeleteJobModal {...riskFormProps} jobId="10" />);

      // Should default to index 0
      expect(screen.getByText('JOB 1')).toBeInTheDocument();
      expect(screen.getByText('Risk Job Step 1 - Risk Hazard 1')).toBeInTheDocument();
    });

    it('handles empty risk_job array', () => {
      const emptyRiskForm = {...mockRiskForm, risk_job: []};
      render(<DeleteJobModal {...riskFormProps} form={emptyRiskForm} />);

      expect(screen.getByText('JOB 1')).toBeInTheDocument();
      // Should handle gracefully when no job exists
    });

    it('handles undefined risk_job', () => {
      const formWithoutJobs = {...mockRiskForm, risk_job: undefined};
      render(<DeleteJobModal {...riskFormProps} form={formWithoutJobs} />);

      expect(screen.getByText('JOB 1')).toBeInTheDocument();
    });

    it('preserves other risk form properties when deleting job', () => {
      const formWithOtherProps = {
        ...mockRiskForm,
        task_requiring_ra: 'Test Risk Task',
        assessor: 123,
      };

      render(<DeleteJobModal {...riskFormProps} form={formWithOtherProps} />);

      const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
      fireEvent.click(deleteButton);

      expect(mockOnClose).toHaveBeenCalledWith(true, expect.any(Object));
    });

    it('handles job deletion when jobId is negative for risk form', () => {
      render(<DeleteJobModal {...riskFormProps} jobId="-1" />);

      const deleteButton = screen.getByRole('button', {name: 'Delete Job Step'});
      fireEvent.click(deleteButton);

      // Should still call onClose even if jobId is invalid
      expect(mockOnClose).toHaveBeenCalledWith(true, undefined);
    });
  });
});
