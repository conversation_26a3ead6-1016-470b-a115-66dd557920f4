import React from 'react';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SingleBadgePopover from '../../src/components/SingleBadgePopover';

describe('SingleBadgePopover', () => {
  const items = ['Alice', '<PERSON>', '<PERSON>'];
  const label = '+3 More';

  it('renders the badge label', () => {
    render(<SingleBadgePopover items={items} label={label} />);
    expect(screen.getByText(label)).toBeInTheDocument();
  });

  it('shows popover with items on hover', async () => {
    render(<SingleBadgePopover items={items} label={label} />);
    const triggerElement = screen.getByText(label);
    await userEvent.hover(triggerElement);

    // Popover content will be rendered in a portal — wait for it
    const popoverContent = await screen.findByText('<PERSON>, <PERSON>, <PERSON>');
    expect(popoverContent).toBeInTheDocument();
  });
});
