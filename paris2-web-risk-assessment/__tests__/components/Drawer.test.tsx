import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import Drawer from '../../src/components/Drawer';

// Mock the CloseIcon component
jest.mock('../../src/components/icons', () => ({
  CloseIcon: ({className}: {className?: string}) => (
    <svg data-testid="close-icon" className={className}>
      <path d="close" />
    </svg>
  ),
}));

// Mock portal rendering
beforeAll(() => {
  // Ensure the test has access to the portal container
  const portalRoot = document.createElement('div');
  portalRoot.setAttribute('id', 'portal-root');
  document.body.appendChild(portalRoot);
});

describe('Drawer component', () => {
  it('renders trigger and toggles drawer open/close', () => {
    render(
      <Drawer title="Test Drawer" trigger={<button>Open Drawer</button>}>
        <p>Drawer content</p>
      </Drawer>,
    );

    expect(screen.getByText('Open Drawer')).toBeInTheDocument();
    expect(screen.queryByText('Drawer content')).not.toBeInTheDocument();

    fireEvent.click(screen.getByText('Open Drawer'));
    expect(screen.getByText('Drawer content')).toBeInTheDocument();

    fireEvent.click(screen.getByRole('button', {name: ''}));
    expect(screen.queryByText('Drawer content')).not.toBeInTheDocument();
  });

  it('renders children as function and allows closing via context', () => {
    render(
      <Drawer title="Function Drawer" trigger={<button>Open Drawer</button>}>
        {({closeDrawer}) => (
          <div>
            <p>Function content</p>
            <button onClick={closeDrawer}>Close from inside</button>
          </div>
        )}
      </Drawer>,
    );

    fireEvent.click(screen.getByText('Open Drawer'));
    expect(screen.getByText('Function content')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Close from inside'));
    expect(screen.queryByText('Function content')).not.toBeInTheDocument();
  });

  it('applies custom className and position', () => {
    render(
      <Drawer
        title="Styled Drawer"
        trigger={<button>Open Drawer</button>}
        className="custom-class"
        position="start"
      >
        <p>Content</p>
      </Drawer>,
    );

    fireEvent.click(screen.getByText('Open Drawer'));

    const drawer = screen.getByText('Styled Drawer').closest('.ra-drawer');
    expect(drawer).toHaveClass('custom-class');
    expect(drawer).toHaveClass('start-0');
  });

  it('hides content when closed via close button', () => {
    render(
      <Drawer title="Closable Drawer" trigger={<button>Open Drawer</button>}>
        <p>To be closed</p>
      </Drawer>,
    );

    fireEvent.click(screen.getByText('Open Drawer'));
    const closeBtn = screen.getByRole('button', {name: ''});
    fireEvent.click(closeBtn);

    expect(screen.queryByText('To be closed')).not.toBeInTheDocument();
  });

  describe('Portal behavior', () => {
    it('renders drawer in portal by default', () => {
      render(
        <Drawer title="Portal Drawer" trigger={<button>Open Portal</button>}>
          <p>Portal content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Portal'));

      // Content should be rendered in document.body via portal
      expect(screen.getByText('Portal content')).toBeInTheDocument();
      expect(screen.getByText('Portal Drawer')).toBeInTheDocument();
    });

    it('renders drawer without portal when notUsePortal is true', () => {
      render(
        <Drawer
          title="No Portal Drawer"
          trigger={<button>Open No Portal</button>}
          notUsePortal={true}
        >
          <p>No portal content</p>
        </Drawer>,
      );

      // When notUsePortal is true, the drawer is rendered inline but initially hidden
      // The trigger is not rendered in this case, so we need to check the drawer structure
      const drawer = document.querySelector('.ra-drawer');
      expect(drawer).toBeInTheDocument();
      expect(drawer).toHaveClass('hide'); // Initially hidden

      // The drawer should contain the title and content structure
      expect(screen.getByText('No Portal Drawer')).toBeInTheDocument();

      // Content should not be visible when closed
      expect(screen.queryByText('No portal content')).not.toBeInTheDocument();
    });
  });

  describe('Position variants', () => {
    it('applies end position by default', () => {
      render(
        <Drawer title="End Drawer" trigger={<button>Open End</button>}>
          <p>End content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open End'));

      const drawer = screen.getByText('End Drawer').closest('.ra-drawer');
      expect(drawer).toHaveClass('end-0');
    });

    it('applies start position when specified', () => {
      render(
        <Drawer
          title="Start Drawer"
          trigger={<button>Open Start</button>}
          position="start"
        >
          <p>Start content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Start'));

      const drawer = screen.getByText('Start Drawer').closest('.ra-drawer');
      expect(drawer).toHaveClass('start-0');
    });
  });

  describe('Drawer structure and styling', () => {
    it('has correct CSS classes and structure', () => {
      render(
        <Drawer
          title="Structure Test"
          trigger={<button>Open Structure</button>}
        >
          <p>Structure content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Structure'));

      const drawer = screen.getByText('Structure Test').closest('.ra-drawer');
      expect(drawer).toHaveClass('ra-drawer');
      expect(drawer).toHaveClass('end-0');
      expect(drawer).not.toHaveClass('hide');

      // Check header structure
      const header = drawer?.querySelector('.ra-drawer-header');
      expect(header).toBeInTheDocument();
      expect(header).toHaveTextContent('Structure Test');

      // Check content structure
      const content = drawer?.querySelector('.ra-drawer-content');
      expect(content).toBeInTheDocument();
      expect(content).toHaveTextContent('Structure content');

      // Check close button
      const closeButton = drawer?.querySelector('.ra-drawer-close');
      expect(closeButton).toBeInTheDocument();
    });

    it('applies custom className correctly', () => {
      render(
        <Drawer
          title="Custom Class"
          trigger={<button>Open Custom</button>}
          className="my-custom-class another-class"
        >
          <p>Custom content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Custom'));

      const drawer = screen.getByText('Custom Class').closest('.ra-drawer');
      expect(drawer).toHaveClass('my-custom-class');
      expect(drawer).toHaveClass('another-class');
      expect(drawer).toHaveClass('ra-drawer');
    });

    it('renders close icon with correct styling', () => {
      render(
        <Drawer title="Icon Test" trigger={<button>Open Icon</button>}>
          <p>Icon content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Icon'));

      const closeIcon = screen.getByTestId('close-icon');
      expect(closeIcon).toBeInTheDocument();
      expect(closeIcon).toHaveClass('cursor-pointer');
    });
  });

  describe('Children rendering', () => {
    it('renders static children correctly', () => {
      render(
        <Drawer title="Static Children" trigger={<button>Open Static</button>}>
          <div>
            <h2>Static Title</h2>
            <p>Static paragraph</p>
            <button>Static button</button>
          </div>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Static'));

      expect(screen.getByText('Static Title')).toBeInTheDocument();
      expect(screen.getByText('Static paragraph')).toBeInTheDocument();
      expect(screen.getByText('Static button')).toBeInTheDocument();
    });

    it('renders function children with closeDrawer prop', () => {
      render(
        <Drawer
          title="Function Children"
          trigger={<button>Open Function</button>}
        >
          {({closeDrawer}) => (
            <div>
              <p>Function rendered content</p>
              <button onClick={closeDrawer} data-testid="custom-close">
                Custom Close
              </button>
            </div>
          )}
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Open Function'));

      expect(screen.getByText('Function rendered content')).toBeInTheDocument();

      const customCloseButton = screen.getByTestId('custom-close');
      expect(customCloseButton).toBeInTheDocument();

      // Test that custom close button works
      fireEvent.click(customCloseButton);
      expect(
        screen.queryByText('Function rendered content'),
      ).not.toBeInTheDocument();
    });

    it('does not render children when drawer is closed', () => {
      render(
        <Drawer title="Closed Test" trigger={<button>Open Closed</button>}>
          <p>Should not be visible</p>
        </Drawer>,
      );

      // Don't click the trigger - drawer should remain closed
      expect(
        screen.queryByText('Should not be visible'),
      ).not.toBeInTheDocument();
      expect(screen.queryByText('Closed Test')).not.toBeInTheDocument();
    });
  });

  describe('Trigger behavior', () => {
    it('clones trigger element with onClick handler', () => {
      const originalOnClick = jest.fn();

      render(
        <Drawer
          title="Trigger Test"
          trigger={<button onClick={originalOnClick}>Custom Trigger</button>}
        >
          <p>Trigger content</p>
        </Drawer>,
      );

      const trigger = screen.getByText('Custom Trigger');
      fireEvent.click(trigger);

      // Drawer should open
      expect(screen.getByText('Trigger content')).toBeInTheDocument();

      // Original onClick should still be preserved (React.cloneElement behavior)
      // Note: The original onClick might not be called due to how cloneElement works
      expect(trigger).toBeInTheDocument();
    });

    it('handles trigger with existing props', () => {
      render(
        <Drawer
          title="Props Test"
          trigger={
            <button
              className="existing-class"
              disabled={false}
              data-testid="trigger-with-props"
            >
              Trigger with Props
            </button>
          }
        >
          <p>Props content</p>
        </Drawer>,
      );

      const trigger = screen.getByTestId('trigger-with-props');
      expect(trigger).toHaveClass('existing-class');
      expect(trigger).not.toBeDisabled();

      fireEvent.click(trigger);
      expect(screen.getByText('Props content')).toBeInTheDocument();
    });
  });

  describe('State management', () => {
    it('toggles open state correctly', () => {
      render(
        <Drawer title="Toggle Test" trigger={<button>Toggle Trigger</button>}>
          <p>Toggle content</p>
        </Drawer>,
      );

      const trigger = screen.getByText('Toggle Trigger');

      // Initially closed
      expect(screen.queryByText('Toggle content')).not.toBeInTheDocument();

      // Open
      fireEvent.click(trigger);
      expect(screen.getByText('Toggle content')).toBeInTheDocument();

      // Close via close button
      const closeButton = screen.getByTestId('close-icon').closest('button');
      fireEvent.click(closeButton!);
      expect(screen.queryByText('Toggle content')).not.toBeInTheDocument();

      // Open again
      fireEvent.click(trigger);
      expect(screen.getByText('Toggle content')).toBeInTheDocument();
    });

    it('handles transition end event when drawer is open', () => {
      render(
        <Drawer
          title="Open Transition Test"
          trigger={<button>Open Transition</button>}
        >
          <p>Open transition content</p>
        </Drawer>,
      );

      const trigger = screen.getByText('Open Transition');

      // Open drawer
      fireEvent.click(trigger);
      expect(screen.getByText('Open transition content')).toBeInTheDocument();

      // Find the drawer element
      const drawer = document.querySelector('.ra-drawer');
      expect(drawer).toBeInTheDocument();
      expect(drawer).not.toHaveClass('hide');

      // Simulate transition end event when drawer is open (isOpen = true)
      fireEvent.transitionEnd(drawer!);

      // When drawer is open, onTransitionEnd should not call toggle
      // Content should still be visible
      expect(screen.getByText('Open transition content')).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles empty title', () => {
      render(
        <Drawer title="" trigger={<button>Empty Title</button>}>
          <p>Empty title content</p>
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Empty Title'));

      const header = document.querySelector('.ra-drawer-header');
      expect(header).toBeInTheDocument();
      expect(header?.textContent).toContain(''); // Empty title
    });

    it('handles undefined children', () => {
      render(
        <Drawer title="No Children" trigger={<button>No Children</button>}>
          {undefined}
        </Drawer>,
      );

      fireEvent.click(screen.getByText('No Children'));

      const content = document.querySelector('.ra-drawer-content');
      expect(content).toBeInTheDocument();
      expect(content?.textContent).toBe('');
    });

    it('handles null children', () => {
      render(
        <Drawer title="Null Children" trigger={<button>Null Children</button>}>
          {null}
        </Drawer>,
      );

      fireEvent.click(screen.getByText('Null Children'));

      const content = document.querySelector('.ra-drawer-content');
      expect(content).toBeInTheDocument();
      expect(content?.textContent).toBe('');
    });
  });
});
