import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import SearchDropdown from '../../src/components/SearchDropdown';

jest.mock('../../src/components/SearchInput', () => (props: any) => (
  <input data-testid="mock-search-input" value={props.value} onChange={e => props.onSearch(e.target.value)} />
));
jest.mock('../../src/components/CheckboxComponent', () => (props: any) => (
  <input type="checkbox" data-testid={props.id} checked={props.checked} onChange={props.onChange} />
));
jest.mock('../../src/components/SingleBadgePopover', () => (props: any) => <span data-testid="mock-badge">{props.label}</span>);
jest.mock('../../src/components/icons/close-icon', () => () => <span data-testid="close-icon">×</span>);

const options = [
  { label: 'One', value: 1 },
  { label: 'Two', value: 2 },
  { label: 'Three', value: 3 },
];

describe('SearchDropdown', () => {
  it('renders placeholder when nothing selected', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} placeholder="Pick" />
    );
    expect(screen.getByText('Pick')).toBeInTheDocument();
  });

  it('renders selected values', () => {
    render(
      <SearchDropdown selected={[1, 2]} options={options} onChange={jest.fn()} />
    );
    // Check for both names individually
    expect(screen.getByText('One')).toBeInTheDocument();
    expect(screen.getByText('+1 More')).toBeInTheDocument();
  });

  it('opens dropdown and filters options', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'Two' } });
    expect(screen.getByText('Two')).toBeInTheDocument();
    expect(screen.queryByText('One')).not.toBeInTheDocument();
  });

  it('calls onChange when option is selected', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('One'));
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('shows select all/clear all button', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('Select all')).toBeInTheDocument();
  });

  it('shows "No options found." when search yields no results', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'nonexistent' } });
    expect(screen.getByText('No options found.')).toBeInTheDocument();
  });

  it('calls onChange for select all', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Select all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('calls onChange for clear all', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={options.map(o => o.value)} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('Clear all'));
    expect(onChange).toHaveBeenCalled();
  });

  it('handles empty options array', () => {
    render(
      <SearchDropdown selected={[]} options={[]} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByText('No options found.')).toBeInTheDocument();
  });

  it('closes dropdown on outside click', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.mouseDown(document);
    // Dropdown should close, so input should not be in the document
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
  });

  it('keyboard accessibility: Enter, Space, Escape', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    const combobox = screen.getByRole('combobox');
    fireEvent.keyDown(combobox, { key: 'Enter' });
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    fireEvent.keyDown(combobox, { key: 'Escape' });
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
    fireEvent.keyDown(combobox, { key: ' ' });
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
  });

  it('has proper aria attributes', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    const combobox = screen.getByRole('combobox');
    expect(combobox).toHaveAttribute('aria-haspopup', 'listbox');
    expect(combobox).toHaveAttribute('aria-expanded');
    expect(combobox).toHaveAttribute('role', 'combobox');
  });

  it('shows counter and SingleBadgePopover when more than maxDisplayNames selected', () => {
    render(
      <SearchDropdown selected={[1, 2, 3]} options={options} onChange={jest.fn()} maxDisplayNames={1} />
    );
    expect(screen.getByText('+2 More')).toBeInTheDocument();
    expect(screen.getByTestId('mock-badge')).toBeInTheDocument();
  });

  it('does not crash if refs are not set in useLayoutEffect', () => {
    // Simulate missing refs by rendering and not opening dropdown
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('does not call onChange for select all/clear all when no options', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={[]} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    const btn = screen.queryByRole('button', { name: /select all|clear all/i });
    if (btn) {
      fireEvent.click(btn);
    }
    expect(onChange).not.toHaveBeenCalled();
  });

  it('calls onChange when CheckboxComponent onChange is triggered', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    // Click the checkbox directly
    fireEvent.click(screen.getByTestId('option-check-1'));
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('does not close dropdown on outside click if dropdownRef is not set', () => {
    // Simulate missing ref by not opening dropdown
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur on outside click
    fireEvent.mouseDown(document);
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('handles setMaxDisplayNames when namesMeasureRef has no children', () => {
    // This is hard to simulate directly, but we can at least ensure no crash
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    // No error should occur
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  // Test single select mode
  it('works in single select mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} multiple={false} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.click(screen.getByText('One'));
    expect(onChange).toHaveBeenCalledWith([1]);
    // Should close dropdown after selection in single mode
    expect(screen.queryByTestId('mock-search-input')).not.toBeInTheDocument();
  });

  it('shows clear icon in single select mode when item is selected', () => {
    render(
      <SearchDropdown selected={[1]} options={options} onChange={jest.fn()} multiple={false} />
    );
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('shows chevron in single select mode when no item is selected', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} multiple={false} />
    );
    // Should show chevron (no close icon)
    expect(screen.queryByTestId('close-icon')).not.toBeInTheDocument();
  });

  it('clears selection when clear icon is clicked in single select mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[1]} options={options} onChange={onChange} multiple={false} />
    );
    fireEvent.click(screen.getByTestId('close-icon'));
    expect(onChange).toHaveBeenCalledWith([]);
  });

  it('clears selection when clear icon is activated with keyboard in single select mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[1]} options={options} onChange={onChange} multiple={false} />
    );
    const clearIcon = screen.getByTestId('close-icon').parentElement;
    fireEvent.keyDown(clearIcon!, { key: 'Enter' });
    expect(onChange).toHaveBeenCalledWith([]);
  });

  it('clears selection when clear icon is activated with spacebar in single select mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[1]} options={options} onChange={onChange} multiple={false} />
    );
    const clearIcon = screen.getByTestId('close-icon').parentElement;
    fireEvent.keyDown(clearIcon!, { key: ' ' });
    expect(onChange).toHaveBeenCalledWith([]);
  });

  it('prevents event propagation when clear icon is clicked', () => {
    const onChange = jest.fn();
    const onDropdownToggle = jest.fn();
    render(
      <div onClick={onDropdownToggle}>
        <SearchDropdown selected={[1]} options={options} onChange={onChange} multiple={false} />
      </div>
    );
    fireEvent.click(screen.getByTestId('close-icon'));
    expect(onChange).toHaveBeenCalledWith([]);
    expect(onDropdownToggle).not.toHaveBeenCalled();
  });

  // Test keyboard navigation in dropdown options
  it('handles keyboard navigation on dropdown options', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    const firstOption = screen.getByText('One').parentElement;
    fireEvent.keyDown(firstOption!, { key: 'Enter' });
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('handles spacebar on dropdown options', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    const firstOption = screen.getByText('One').parentElement;
    fireEvent.keyDown(firstOption!, { key: ' ' });
    expect(onChange).toHaveBeenCalledWith([1]);
  });

  it('ignores other keys on dropdown options', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    const firstOption = screen.getByText('One').parentElement;
    fireEvent.keyDown(firstOption!, { key: 'Tab' });
    expect(onChange).not.toHaveBeenCalled();
  });

  // Test deselection in multiple mode
  it('deselects option when already selected in multiple mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[1]} options={options} onChange={onChange} multiple={true} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    // Click on the option in the dropdown (not the selected display)
    const dropdownOptions = screen.getAllByText('One');
    const dropdownOption = dropdownOptions.find(el => el.closest('[role="option"]'));
    fireEvent.click(dropdownOption!);
    expect(onChange).toHaveBeenCalledWith([]);
  });

  // Test select all with filtered options
  it('selects all filtered options when some are already selected', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[1]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'T' } });
    fireEvent.click(screen.getByText('Select all'));
    expect(onChange).toHaveBeenCalledWith([1, 2, 3]);
  });

  it('clears all filtered options when all are selected', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[2, 3]} options={options} onChange={onChange} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    fireEvent.change(screen.getByTestId('mock-search-input'), { target: { value: 'T' } });
    fireEvent.click(screen.getByText('Clear all'));
    expect(onChange).toHaveBeenCalledWith([]);
  });

  // Test null selected prop
  it('handles null selected prop', () => {
    render(
      <SearchDropdown selected={null} options={options} onChange={jest.fn()} />
    );
    expect(screen.getByText('Select option')).toBeInTheDocument();
  });

  // Test window resize handler
  it('handles window resize', () => {
    render(
      <SearchDropdown selected={[1, 2]} options={options} onChange={jest.fn()} />
    );

    act(() => {
      window.dispatchEvent(new Event('resize'));
    });

    // Should not crash
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  // Test useLayoutEffect with selected options
  it('triggers useLayoutEffect when dropdown opens with selected options', () => {
    render(
      <SearchDropdown selected={[1, 2, 3]} options={options} onChange={jest.fn()} />
    );

    // Open dropdown to trigger useLayoutEffect
    fireEvent.click(screen.getByRole('combobox'));

    // Should not crash and should show the dropdown
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
  });

  // Test aria-selected attribute
  it('sets aria-selected correctly on options', () => {
    render(
      <SearchDropdown selected={[1]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    const allOptions = screen.getAllByRole('option');
    const selectedOption = allOptions.find(option => option.getAttribute('aria-selected') === 'true');
    const unselectedOption = allOptions.find(option => option.getAttribute('aria-selected') === 'false');

    expect(selectedOption).toHaveAttribute('aria-selected', 'true');
    expect(unselectedOption).toHaveAttribute('aria-selected', 'false');
  });

  // Test className application for single select
  it('applies correct className for single select options', () => {
    render(
      <SearchDropdown selected={[1]} options={options} onChange={jest.fn()} multiple={false} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    const allOptions = screen.getAllByRole('option');
    const selectedOption = allOptions.find(option => option.getAttribute('aria-selected') === 'true');
    const unselectedOption = allOptions.find(option => option.getAttribute('aria-selected') === 'false');

    expect(selectedOption).toHaveClass('option-list-item', 'single-select', 'selected');
    expect(unselectedOption).toHaveClass('option-list-item', 'single-select');
    expect(unselectedOption).not.toHaveClass('selected');
  });

  // Test that checkboxes are not rendered in single select mode
  it('does not render checkboxes in single select mode', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} multiple={false} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    expect(screen.queryByTestId('option-check-1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('option-check-2')).not.toBeInTheDocument();
    expect(screen.queryByTestId('option-check-3')).not.toBeInTheDocument();
  });

  // Test that select all button is not rendered in single select mode
  it('does not render select all button in single select mode', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} multiple={false} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    expect(screen.queryByText('Select all')).not.toBeInTheDocument();
  });

  // Test search functionality with null/empty search
  it('handles null search value', () => {
    render(
      <SearchDropdown selected={[]} options={options} onChange={jest.fn()} />
    );
    fireEvent.click(screen.getByRole('combobox'));

    // Simulate SearchInput calling onSearch with null
    const searchInput = screen.getByTestId('mock-search-input');
    fireEvent.change(searchInput, { target: { value: '' } });

    // All options should be visible
    expect(screen.getByText('One')).toBeInTheDocument();
    expect(screen.getByText('Two')).toBeInTheDocument();
    expect(screen.getByText('Three')).toBeInTheDocument();
  });

  // Test adding an option in multiple mode (line 47)
  it('adds option when not already selected in multiple mode', () => {
    const onChange = jest.fn();
    render(
      <SearchDropdown selected={[2]} options={options} onChange={onChange} multiple={true} />
    );
    fireEvent.click(screen.getByRole('combobox'));
    // Click on an unselected option
    const dropdownOptions = screen.getAllByText('One');
    const dropdownOption = dropdownOptions.find(el => el.closest('[role="option"]'));
    fireEvent.click(dropdownOption!);
    expect(onChange).toHaveBeenCalledWith([2, 1]);
  });

  // Test useLayoutEffect width calculation (lines 197-198)
  it('calculates display names based on available width', () => {
    // Mock offsetWidth to simulate width calculations
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: function() {
        if (this.className?.includes('option-multiselect-input')) {
          return 200; // input width
        }
        if (this.style?.marginRight === '4px') {
          return 30; // child width
        }
        return 50; // default width
      }
    });

    render(
      <SearchDropdown selected={[1, 2, 3]} options={options} onChange={jest.fn()} />
    );

    // Open dropdown to trigger useLayoutEffect
    fireEvent.click(screen.getByRole('combobox'));

    // Should not crash and should show the dropdown
    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
  });

  // Test window resize with inputRef.current (lines 206-207)
  it('handles window resize when inputRef is available', async () => {
    render(
      <SearchDropdown selected={[1, 2]} options={options} onChange={jest.fn()} />
    );

    // Trigger resize event
    act(() => {
      window.dispatchEvent(new Event('resize'));
    });

    // Wait for the setTimeout to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 150));
    });

    // Should not crash
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

});
