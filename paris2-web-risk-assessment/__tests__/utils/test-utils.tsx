import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import DataStoreProvider from '../../src/context/DataStoreProvider';
import { UserRoleControllerConfig } from '../../src/types';

// Create a mock navigate function that can be accessed by tests
const mockNavigate = jest.fn();

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock role config for tests
const mockRoleConfig: UserRoleControllerConfig = {
  user: {
    user_id: 1,
    name: 'Test User',
    email: '<EMAIL>',
  },
  riskAssessment: {
    hasPermision: true,
  },
  vessel: {
    hasPermision: true,
  },
  owner: {
    hasPermision: true,
  },
  newBuilding: {
    hasPermision: true,
  },
  ownerReporting: {
    hasPermision: true,
  },
};

const mockGa4EventTrigger = jest.fn();

interface AllTheProvidersProps {
  children: React.ReactNode;
  initialEntries?: string[];
  roleConfig?: UserRoleControllerConfig;
}

const AllTheProviders = ({ 
  children, 
  initialEntries = ['/'], 
  roleConfig = mockRoleConfig 
}: AllTheProvidersProps) => {
  return (
    <MemoryRouter initialEntries={initialEntries}>
      <DataStoreProvider 
        roleConfig={roleConfig} 
        ga4EventTrigger={mockGa4EventTrigger}
      >
        {children}
      </DataStoreProvider>
    </MemoryRouter>
  );
};

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  roleConfig?: UserRoleControllerConfig;
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, roleConfig, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders 
        initialEntries={initialEntries} 
        roleConfig={roleConfig}
      >
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  });
};

// Re-export everything
export * from '@testing-library/react';

// Override render method
export { customRender as render };

// Export utilities
export { mockRoleConfig, mockGa4EventTrigger, AllTheProviders, mockNavigate };
