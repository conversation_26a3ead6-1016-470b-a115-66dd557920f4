import React from 'react';
import {bootstrap, mount, unmount} from '../src/paris2-risk-assessment';

jest.mock('single-spa-react', () => {
  const mockSingleSpaReact = jest.fn(() => ({
    errorBoundary: jest.fn((err, info, props) => (
      <div>Something went wrong!</div>
    )),
  }));
  return mockSingleSpaReact;
});

jest.mock('react-dom/client', () => ({
  createRoot: jest.fn(() => ({
    render: jest.fn(),
    unmount: jest.fn(),
  })),
}));
jest.mock('../src/root.component', () =>
  jest.fn(() => <div>Mock Root Component</div>),
);
jest.mock('../src/paris2-risk-assessment', () => ({
  bootstrap: jest.fn(),
  mount: jest.fn(),
  unmount: jest.fn(),
}));

describe('paris2-risk-assessment lifecycles', () => {
  it('should export bootstrap, mount, and unmount lifecycle functions', () => {
    expect(bootstrap).toBeDefined();
    expect(mount).toBeDefined();
    expect(unmount).toBeDefined();
  });
});
