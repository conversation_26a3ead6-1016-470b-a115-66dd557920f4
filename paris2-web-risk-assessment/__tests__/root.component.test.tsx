jest.mock('../src/context/DataStoreProvider', () => ({
  __esModule: true,
  default: ({children, ga4EventTrigger}: any) => {
    // Store the ga4EventTrigger function for testing
    (global as any).testGa4EventTrigger = ga4EventTrigger;
    return <div data-testid="datastore-provider">{children}</div>;
  },
}));
jest.mock('../src/context/AlertContextProvider', () => ({
  __esModule: true,
  default: ({children}: any) => <div data-testid="alert-context">{children}</div>,
}));
jest.mock('../src/routes/rootRoutes', () => () => <div data-testid="app-routes">AppRoutes</div>);
jest.mock('react-toastify', () => ({ ToastContainer: () => <div data-testid="toast-container" /> }));

import React from 'react';
import {render, screen, waitFor} from '@testing-library/react';
import Root from '../src/root.component';
import userService from '../src/services/user.service';
import UserRoleController from '../src/controller/user-role-controller';

const mockUserService = userService as jest.Mocked<typeof userService>;

jest.mock('../src/controller/user-role-controller', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      getConfig: jest.fn().mockReturnValue({
        user: { userId: 1 },
        riskAssessment: {
          canCreateNewTemplate: true,
          hasPermision: true,
        },
      }),
    })),
  };
});

describe('Root Component', () => {
  const kcMock = {};
  const ga4reactMock = {
    event: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUserService.init.mockResolvedValue(undefined);
    (UserRoleController as jest.Mock).mockImplementation(() => ({
      getConfig: jest.fn().mockReturnValue({ role: 'user' }),
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading spinner while initializing user service', async () => {
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    await waitFor(() => expect(mockUserService.init).toHaveBeenCalled());
  });

  it('renders loading spinner while roleConfig is null', async () => {
    // Force roleConfig to be null initially
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    // Wait for async effect to complete
    await waitFor(() => expect(screen.getByTestId('datastore-provider')).toBeInTheDocument());
  });

  it('renders main app structure after loading', async () => {
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);
    await waitFor(() => expect(screen.getByTestId('datastore-provider')).toBeInTheDocument());
    expect(screen.getByTestId('alert-context')).toBeInTheDocument();
    expect(screen.getByTestId('datastore-provider')).toBeInTheDocument();
    expect(screen.getByTestId('app-routes')).toBeInTheDocument();
    expect(screen.getByTestId('toast-container')).toBeInTheDocument();
  });

  it('calls ga4EventTrigger successfully', async () => {
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);
    await waitFor(() => expect(screen.getByTestId('datastore-provider')).toBeInTheDocument());

    // Access the ga4EventTrigger function that was passed to DataStoreProvider
    const ga4EventTrigger = (global as any).testGa4EventTrigger;
    expect(ga4EventTrigger).toBeDefined();

    // Test successful ga4 event trigger
    ga4EventTrigger('test-action', 'test-label', 'test-category');
    expect(ga4reactMock.event).toHaveBeenCalledWith('test-action', 'test-label', 'test-category', false);
  });

  it('handles ga4EventTrigger errors gracefully', async () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const errorGa4 = { event: () => { throw new Error('GA4 error'); } };

    render(<Root kc={kcMock} ga4react={errorGa4} />);
    await waitFor(() => expect(screen.getByTestId('datastore-provider')).toBeInTheDocument());

    // Access the ga4EventTrigger function that was passed to DataStoreProvider
    const ga4EventTrigger = (global as any).testGa4EventTrigger;
    expect(ga4EventTrigger).toBeDefined();

    // Test error handling in ga4EventTrigger
    ga4EventTrigger('test-action', 'test-label', 'test-category');
    expect(errorSpy).toHaveBeenCalledWith('GA4 Event Trigger Error:', expect.any(Error));

    errorSpy.mockRestore();
  });

  it('renders loading spinner with correct accessibility attributes', () => {
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);
    const spinner = screen.getByRole('status', { hidden: true });
    expect(spinner).toHaveClass('spinner-border');
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('handles missing ga4react gracefully', async () => {
    render(<Root kc={kcMock} ga4react={undefined as any} />);
    await waitFor(() => expect(screen.getByTestId('datastore-provider')).toBeInTheDocument());

    // Access the ga4EventTrigger function that was passed to DataStoreProvider
    const ga4EventTrigger = (global as any).testGa4EventTrigger;
    expect(ga4EventTrigger).toBeDefined();

    // Test that calling ga4EventTrigger with undefined ga4react doesn't throw
    expect(() => {
      ga4EventTrigger('test-action', 'test-label', 'test-category');
    }).not.toThrow();
  });
});
