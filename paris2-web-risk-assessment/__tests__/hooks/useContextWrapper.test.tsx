import React, {createContext} from 'react';
import {render} from '@testing-library/react';
import useContextWrapper from '../../src/hooks/useContextWrapper';

const TestContext = createContext<{value: string} | undefined>(undefined);

const config = {
  contextName: 'TestContext',
  providerName: 'TestProvider',
};

function ConsumerComponent() {
  const context = useContextWrapper(TestContext, config);
  return <div data-testid="context-value">{context.value}</div>;
}

describe('useContextWrapper', () => {
  it('returns context value when inside provider', () => {
    const {getByTestId} = render(
      <TestContext.Provider value={{value: 'hello'}}>
        <ConsumerComponent />
      </TestContext.Provider>,
    );
    expect(getByTestId('context-value')).toHaveTextContent('hello');
  });

  it('throws error when used outside provider', () => {
    // Suppress error output for this test
    const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
    expect(() => render(<ConsumerComponent />)).toThrow(
      'TestContext must be used within a TestProvider',
    );
    spy.mockRestore();
  });
});
