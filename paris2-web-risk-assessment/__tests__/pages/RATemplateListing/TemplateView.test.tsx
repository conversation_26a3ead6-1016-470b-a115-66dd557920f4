import React from 'react';
import {screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {render, mockNavigate} from '../../utils/test-utils';
import TemplateView from '../../../src/pages/RATemplateListing/TemplateView';
import {getTemplateById} from '../../../src/services/services';
import {createFormFromData} from '../../../src/utils/helper';
import {TemplateFormStatus} from '../../../src/enums';

// Mock dependencies
jest.mock('../../../src/services/services');
jest.mock('../../../src/utils/helper');
jest.mock('../../../src/components/Loader', () => {
  return function MockLoader({isOverlayLoader}: {isOverlayLoader?: boolean}) {
    return <div data-testid="loader" data-overlay={isOverlayLoader}>Loading...</div>;
  };
});
jest.mock('../../../src/pages/CreateRA/PreviewFormDetails', () => {
  return function MockPreviewFormDetails(props: any) {
    return (
      <div data-testid="preview-form-details">
        <div data-testid="form-task">{props.form?.task_requiring_ra}</div>
        <div data-testid="form-type">{props.type}</div>
        <div data-testid="preview-only">{props.previewOnly ? 'true' : 'false'}</div>
        <div data-testid="show-breadcrumb">{props.showBreadCrumb ? 'true' : 'false'}</div>
        <div data-testid="show-use-template">{props.showUseTemplate ? 'true' : 'false'}</div>
        <div data-testid="breadcrumb-items">
          {props.breadcrumbOptions?.items?.map((item: any, index: number) => (
            <div key={index} data-testid={`breadcrumb-item-${index}`}>
              {item.title} - {item.link || 'no-link'}
            </div>
          ))}
        </div>
      </div>
    );
  };
});

// Mock react-router-dom
const mockUseParams = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => mockUseParams(),
}));

const mockGetTemplateById = getTemplateById as jest.MockedFunction<typeof getTemplateById>;
const mockCreateFormFromData = createFormFromData as jest.MockedFunction<typeof createFormFromData>;

describe('TemplateView', () => {
  const mockTemplateData = {
    id: 1,
    task_requiring_ra: 'Test Template Task',
    task_duration: '2 hours',
    status: TemplateFormStatus.PUBLISHED,
    template_category: {
      category_id: [1],
      is_other: false,
      value: 'Test Category',
    },
    template_hazard: {
      hazard_id: [1],
      is_other: false,
      value: 'Test Hazard',
    },
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: ['test'],
  };

  const mockFormData = {
    task_requiring_ra: 'Test Template Task',
    task_duration: '2 hours',
    task_alternative_consideration: '',
    task_rejection_reason: '',
    worst_case_scenario: '',
    recovery_measures: '',
    status: TemplateFormStatus.PUBLISHED,
    template_category: {
      category_id: [1],
      is_other: false,
      value: 'Test Category',
    },
    template_hazard: {
      hazard_id: [1],
      is_other: false,
      value: 'Test Hazard',
    },
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: ['test'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateFormFromData.mockReturnValue(mockFormData);
    mockUseParams.mockReturnValue({id: '1'}); // Default return value
  });

  describe('Component Rendering', () => {
    it('renders loading state initially', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<TemplateView />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.getByTestId('loader')).toHaveAttribute('data-overlay', 'true');
    });

    it('renders template data after successful fetch', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
      expect(screen.getByTestId('form-task')).toHaveTextContent('Test Template Task');
      expect(screen.getByTestId('form-type')).toHaveTextContent('template');
      expect(screen.getByTestId('preview-only')).toHaveTextContent('true');
      expect(screen.getByTestId('show-breadcrumb')).toHaveTextContent('true');
      expect(screen.getByTestId('show-use-template')).toHaveTextContent('true');
    });

    it('renders breadcrumb options correctly', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByTestId('breadcrumb-item-0')).toHaveTextContent('Risk Assessment - /risk-assessment');
      expect(screen.getByTestId('breadcrumb-item-1')).toHaveTextContent('Templates - /risk-assessment/template-listing');
      expect(screen.getByTestId('breadcrumb-item-2')).toHaveTextContent('Test Template Task - no-link');
    });
  });

  describe('Data Fetching', () => {
    it('calls createFormFromData with template data', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(mockCreateFormFromData).toHaveBeenCalledWith(mockTemplateData);
      });
    });

    it('calls getTemplateById when id is provided', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(mockGetTemplateById).toHaveBeenCalled();
      });
    });


  });

  describe('Error Handling', () => {
    it('handles API error gracefully', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockGetTemplateById.mockRejectedValue(new Error('API Error'));

      render(<TemplateView />);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching draft', expect.any(Error));
      });

      expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();

      consoleErrorSpy.mockRestore();
    });

    it('continues rendering even after fetch error', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      jest.spyOn(console, 'error').mockImplementation(() => {});
      mockGetTemplateById.mockRejectedValue(new Error('Network Error'));

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });

      expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loader during initial render', () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockImplementation(() => new Promise(() => {}));

      render(<TemplateView />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });

    it('hides loader after successful data fetch', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });

    it('hides loader after failed data fetch', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      jest.spyOn(console, 'error').mockImplementation(() => {});
      mockGetTemplateById.mockRejectedValue(new Error('Error'));

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
      });
    });
  });

  describe('PreviewFormDetails Props', () => {
    it('passes correct props to PreviewFormDetails', async () => {
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: mockTemplateData,
      });

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.getByTestId('preview-form-details')).toBeInTheDocument();
      });

      expect(screen.getByTestId('form-type')).toHaveTextContent('template');
      expect(screen.getByTestId('preview-only')).toHaveTextContent('true');
      expect(screen.getByTestId('show-breadcrumb')).toHaveTextContent('true');
      expect(screen.getByTestId('show-use-template')).toHaveTextContent('true');
    });

    it('handles empty task_requiring_ra in breadcrumb', async () => {
      const emptyTaskData = {...mockTemplateData, task_requiring_ra: ''};
      mockUseParams.mockReturnValue({id: '1'});
      mockGetTemplateById.mockResolvedValue({
        message: 'Success',
        result: emptyTaskData,
      });
      mockCreateFormFromData.mockReturnValue({...mockFormData, task_requiring_ra: ''});

      render(<TemplateView />);

      await waitFor(() => {
        expect(screen.getByTestId('breadcrumb-item-2')).toHaveTextContent('- no-link');
      });
    });
  });
});
