import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import ReAssignApproverModal from '../../../src/pages/CreateRA/ReAssignApproverModal';

// Mock AsyncSearchCrewMember to simulate user selection
jest.mock('../../../src/components/SearchCrewMember', () => ({
  __esModule: true,
  AsyncSearchCrewMember: ({ onChange, value, placeholder }: any) => (
    <input
      data-testid="mock-search"
      placeholder={placeholder}
      value={value[0] || ''}
      onChange={e => {
        // Simulate selecting a user with user_id '1'
        onChange([e.target.value], [
          { user_id: '1', email: '<EMAIL>', first_name: 'User' },
          { user_id: '2', email: '<EMAIL>', first_name: 'User2' },
        ]);
      }}
    />
  ),
}));

describe('ReAssignApproverModal', () => {
  const triggerText = 'Open Modal';
  const trigger: React.ReactElement = <button>{triggerText}</button>;
  const approverOrder = 2;
  const fetchQuery = jest.fn();
  const officeApprover: import('../../../src/types').OfficeApprover = { user_id: '1', first_name: 'User', email: '<EMAIL>' };
  const onConfirm = jest.fn(() => Promise.resolve());

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('opens modal on trigger click and closes on cancel', async () => {
    render(
      <ReAssignApproverModal
        onConfirm={onConfirm}
        trigger={trigger}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Re-Assigning Reviewer')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => {
      expect(screen.queryByText('Re-Assigning Reviewer')).not.toBeInTheDocument();
    });
  });

  it('disables Confirm button until a user is selected', () => {
    render(
      <ReAssignApproverModal
        onConfirm={onConfirm}
        trigger={trigger}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    fireEvent.click(screen.getByText(triggerText));
    const confirmBtn = screen.getByText('Confirm');
    expect(confirmBtn).toBeDisabled();
    fireEvent.change(screen.getByTestId('mock-search'), { target: { value: '1' } });
    expect(confirmBtn).not.toBeDisabled();
  });

  it('calls onConfirm with selected user and order, disables buttons while loading, and closes modal', async () => {
    const onConfirmAsync = jest.fn(() => new Promise<void>(res => setTimeout(() => res(undefined), 100)));
    render(
      <ReAssignApproverModal
        onConfirm={onConfirmAsync}
        trigger={trigger}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-search'), { target: { value: '1' } });
    const confirmBtn = screen.getByText('Confirm');
    fireEvent.click(confirmBtn);
    expect(confirmBtn).toBeDisabled();
    expect(screen.getByText('Cancel')).toBeDisabled();
    await waitFor(() => expect(onConfirmAsync).toHaveBeenCalledWith(
      expect.objectContaining({ user_id: '1', email: '<EMAIL>' }),
      approverOrder
    ));
    await waitFor(() => {
      expect(screen.queryByText('Re-Assigning Reviewer')).not.toBeInTheDocument();
    });
  });

  it('resets selectedUser on close', async () => {
    render(
      <ReAssignApproverModal
        onConfirm={onConfirm}
        trigger={trigger}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-search'), { target: { value: '1' } });
    expect(screen.getByText('Confirm')).not.toBeDisabled();
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => {
      expect(screen.queryByText('Re-Assigning Reviewer')).not.toBeInTheDocument();
    });
    // Reopen and check selectedUser is reset
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Confirm')).toBeDisabled();
  });

  it('handles error in onConfirm gracefully and closes modal', async () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const onConfirmError = jest.fn(() => Promise.reject(new Error('fail')));
    render(
      <ReAssignApproverModal
        onConfirm={onConfirmError}
        trigger={trigger}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-search'), { target: { value: '1' } });
    fireEvent.click(screen.getByText('Confirm'));
    await waitFor(() => expect(onConfirmError).toHaveBeenCalled());
    await waitFor(() => {
      expect(screen.queryByText('Re-Assigning Reviewer')).not.toBeInTheDocument();
    });
    expect(errorSpy).toHaveBeenCalledWith(
      'Error re-assigning reviewer:',
      expect.any(Error)
    );
    errorSpy.mockRestore();
  });

  it('does not break if trigger is null', () => {
    render(
      <ReAssignApproverModal
        onConfirm={onConfirm}
        trigger={null as any}
        approverOrder={approverOrder}
        fetchQuery={fetchQuery}
      />
    );
    // Should not throw or render modal
    expect(screen.queryByText('Re-Assigning Reviewer')).not.toBeInTheDocument();
  });
});
