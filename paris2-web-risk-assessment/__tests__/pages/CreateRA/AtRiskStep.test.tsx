import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import {AtRiskStep} from '../../../src/pages/CreateRA/AtRiskStep';
import {TemplateForm} from '../../../src/types/template';

// Mock the dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/utils/helper', () => ({
  generateGroupedOptions: jest.fn(),
}));

jest.mock('../../../src/components/GroupedCheckboxGrid', () => {
  return function MockGroupedCheckboxGrid({
    title,
    subtitle,
    groups,
    value,
    onChange,
    othersPlaceholder,
    othersMaxLength,
  }: {
    title: string;
    subtitle: string;
    groups: any[];
    value: any[];
    onChange: (params: any[]) => void;
    othersPlaceholder?: string;
    othersMaxLength?: number;
  }) {
    return (
      <div data-testid="grouped-checkbox-grid">
        <div data-testid="title">{title}</div>
        <div data-testid="subtitle">{subtitle}</div>
        <div data-testid="others-placeholder">{othersPlaceholder}</div>
        <div data-testid="others-max-length">{othersMaxLength}</div>
        <div data-testid="groups-count">{groups.length}</div>
        <div data-testid="value-count">{value.length}</div>
        <button
          data-testid="trigger-change"
          onClick={() =>
            onChange([
              {
                is_other: false,
                parameter_type_id: 1,
                parameter_id: [101],
                value: '',
              },
            ])
          }
        >
          Trigger Change
        </button>
        <button
          data-testid="trigger-others-change"
          onClick={() =>
            onChange([
              {
                is_other: true,
                parameter_type_id: 1,
                parameter_id: [],
                value: 'Custom risk parameter',
              },
            ])
          }
        >
          Trigger Others Change
        </button>
      </div>
    );
  };
});

const mockUseDataStoreContext =
  require('../../../src/context').useDataStoreContext;
const mockGenerateGroupedOptions =
  require('../../../src/utils/helper').generateGroupedOptions;

describe('AtRiskStep Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();

  const mockRiskParameterType = [
    {
      id: 1,
      name: 'Personnel',
      parameters: [
        {id: 101, name: 'Crew Members'},
        {id: 102, name: 'Passengers'},
      ],
    },
    {
      id: 2,
      name: 'Environment',
      parameters: [
        {id: 201, name: 'Marine Life'},
        {id: 202, name: 'Water Quality'},
      ],
    },
  ];

  const mockGroups = [
    {
      id: 1,
      label: 'PERSONNEL',
      options: [
        {id: 101, label: 'Crew Members'},
        {id: 102, label: 'Passengers'},
      ],
      columns: 3,
    },
    {
      id: 2,
      label: 'ENVIRONMENT',
      options: [
        {id: 201, label: 'Marine Life'},
        {id: 202, label: 'Water Quality'},
      ],
      columns: 3,
    },
  ];

  const defaultForm: TemplateForm = {
    task_requiring_ra: 'Test Task',
    task_duration: '2',
    task_duration_unit: 'hours',
    task_alternative_consideration: '',
    task_rejection_reason: '',
    worst_case_scenario: '',
    recovery_measures: '',
    status: 'draft',
    parameters: [],
    risk_template_category: {
      is_other: false,
      value: '',
      risk_template_category_id: [],
    },
    risk_template_hazard: {
      is_other: false,
      value: '',
      risk_template_hazard_id: [],
    },
    risk_template_job: [],
    risk_template_task_reliability_assessment: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockUseDataStoreContext.mockReturnValue({
      dataStore: {
        riskParameterType: mockRiskParameterType,
      },
    });

    mockGenerateGroupedOptions.mockReturnValue(mockGroups);
  });

  it('renders correctly with default props', () => {
    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('grouped-checkbox-grid')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('Test Task');
    expect(screen.getByTestId('subtitle')).toHaveTextContent(
      'Give consideration to Groups of Individuals at Risk from the Hazards Identified',
    );
    expect(screen.getByTestId('others-placeholder')).toHaveTextContent(
      'List the People at Risk',
    );
    expect(screen.getByTestId('others-max-length')).toHaveTextContent('200');
  });

  it('calls generateGroupedOptions with correct parameters', () => {
    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(mockGenerateGroupedOptions).toHaveBeenCalledWith(
      mockRiskParameterType,
      3,
    );
  });

  it('passes correct props to GroupedCheckboxGrid', () => {
    const formWithParameters = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [101],
          value: '',
        },
      ],
    };

    render(
      <AtRiskStep
        form={formWithParameters}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('groups-count')).toHaveTextContent('2');
    expect(screen.getByTestId('value-count')).toHaveTextContent('1');
  });

  it('handles form changes correctly', () => {
    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const triggerChangeButton = screen.getByTestId('trigger-change');
    fireEvent.click(triggerChangeButton);

    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const result = setFormCall(defaultForm);

    expect(result).toEqual({
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [101],
          value: '',
        },
      ],
    });
  });

  it('validates correctly when no parameters are selected', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('validates correctly when parameters are selected', () => {
    const formWithSelectedParams = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [101, 102],
          value: '',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithSelectedParams}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('validates correctly when others option is selected with text', () => {
    const formWithOthersText = {
      ...defaultForm,
      parameters: [
        {
          is_other: true,
          parameter_type_id: 1,
          parameter_id: [],
          value: 'Custom risk parameter',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('validates correctly when others option is selected but text is empty', () => {
    const formWithEmptyOthersText = {
      ...defaultForm,
      parameters: [
        {
          is_other: true,
          parameter_type_id: 1,
          parameter_id: [],
          value: '',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithEmptyOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('validates correctly when others option has only whitespace', () => {
    const formWithWhitespaceOthersText = {
      ...defaultForm,
      parameters: [
        {
          is_other: true,
          parameter_type_id: 1,
          parameter_id: [],
          value: '   ',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithWhitespaceOthersText}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('handles mixed validation scenarios correctly', () => {
    const formWithMixedParams = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [],
          value: '',
        },
        {
          is_other: true,
          parameter_type_id: 2,
          parameter_id: [],
          value: 'Valid custom parameter',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithMixedParams}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('handles empty parameters array correctly', () => {
    const formWithUndefinedParams = {
      ...defaultForm,
      parameters: undefined as any,
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithUndefinedParams}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('handles non-array parameters correctly', () => {
    const formWithInvalidParams = {
      ...defaultForm,
      parameters: 'invalid' as any,
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithInvalidParams}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('calls onValidate on form parameter changes', () => {
    const {rerender} = render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    // Clear previous calls
    mockOnValidate.mockClear();

    const updatedForm = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [101],
          value: '',
        },
      ],
    };

    rerender(
      <AtRiskStep
        form={updatedForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('works without onValidate callback', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    expect(() => {
      render(<AtRiskStep ref={ref} form={defaultForm} setForm={mockSetForm} />);
    }).not.toThrow();

    expect(() => {
      ref.current?.validate();
    }).not.toThrow();
  });

  it('handles empty task_requiring_ra correctly', () => {
    const formWithEmptyTask = {
      ...defaultForm,
      task_requiring_ra: '',
    };

    render(
      <AtRiskStep
        form={formWithEmptyTask}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('title')).toHaveTextContent('');
  });

  it('handles undefined task_requiring_ra correctly', () => {
    const formWithUndefinedTask = {
      ...defaultForm,
      task_requiring_ra: undefined as any,
    };

    render(
      <AtRiskStep
        form={formWithUndefinedTask}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('title')).toHaveTextContent('');
  });

  it('handles others change correctly', () => {
    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const triggerOthersChangeButton = screen.getByTestId(
      'trigger-others-change',
    );
    fireEvent.click(triggerOthersChangeButton);

    expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

    // Test the function passed to setForm
    const setFormCall = mockSetForm.mock.calls[0][0];
    const result = setFormCall(defaultForm);

    expect(result).toEqual({
      ...defaultForm,
      parameters: [
        {
          is_other: true,
          parameter_type_id: 1,
          parameter_id: [],
          value: 'Custom risk parameter',
        },
      ],
    });
  });

  it('exposes validate method through ref', () => {
    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(ref.current).toBeDefined();
    expect(typeof ref.current?.validate).toBe('function');
  });

  it('handles complex parameter validation scenarios', () => {
    const complexForm = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: [101, 102],
          value: '',
        },
        {
          is_other: false,
          parameter_type_id: 2,
          parameter_id: [],
          value: '',
        },
        {
          is_other: true,
          parameter_type_id: 3,
          parameter_id: [],
          value: 'Valid custom text',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={complexForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true);
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('handles validation with null parameter_id arrays', () => {
    const formWithNullParameterIds = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: null as any,
          value: '',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithNullParameterIds}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('handles validation with undefined parameter_id arrays', () => {
    const formWithUndefinedParameterIds = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 1,
          parameter_id: undefined as any,
          value: '',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithUndefinedParameterIds}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(false);
    expect(mockOnValidate).toHaveBeenCalledWith(false);
  });

  it('handles validation when riskParameterType is empty', () => {
    mockUseDataStoreContext.mockReturnValue({
      dataStore: {
        riskParameterType: [],
      },
    });

    mockGenerateGroupedOptions.mockReturnValue([]);

    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('groups-count')).toHaveTextContent('0');
    expect(mockGenerateGroupedOptions).toHaveBeenCalledWith([], 3);
  });

  it('handles validation when riskParameterType is undefined', () => {
    mockUseDataStoreContext.mockReturnValue({
      dataStore: {
        riskParameterType: undefined,
      },
    });

    mockGenerateGroupedOptions.mockReturnValue([]);

    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(mockGenerateGroupedOptions).toHaveBeenCalledWith(undefined, 3);
  });

  it('validates correctly with correct parameters', () => {
    const formWithMixedValidityParams = {
      ...defaultForm,
      parameters: [
        {
          is_other: false,
          parameter_type_id: 2,
          parameter_id: [201],
          value: '',
        },
      ],
    };

    const ref = React.createRef<{validate: () => boolean}>();

    render(
      <AtRiskStep
        ref={ref}
        form={formWithMixedValidityParams}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const isValid = ref.current?.validate();
    expect(isValid).toBe(true); // Should be true because second parameter has valid parameter_id
    expect(mockOnValidate).toHaveBeenCalledWith(true);
  });

  it('handles form updates correctly when setForm is called multiple times', () => {
    render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    const triggerChangeButton = screen.getByTestId('trigger-change');

    // Click multiple times
    fireEvent.click(triggerChangeButton);
    fireEvent.click(triggerChangeButton);
    fireEvent.click(triggerChangeButton);

    expect(mockSetForm).toHaveBeenCalledTimes(3);
  });

  it('maintains component stability when props change', () => {
    const {rerender} = render(
      <AtRiskStep
        form={defaultForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    // Change form props
    const newForm = {
      ...defaultForm,
      task_requiring_ra: 'Updated Task',
    };

    rerender(
      <AtRiskStep
        form={newForm}
        setForm={mockSetForm}
        onValidate={mockOnValidate}
      />,
    );

    expect(screen.getByTestId('title')).toHaveTextContent('Updated Task');
    expect(screen.getByTestId('grouped-checkbox-grid')).toBeInTheDocument();
  });
});
