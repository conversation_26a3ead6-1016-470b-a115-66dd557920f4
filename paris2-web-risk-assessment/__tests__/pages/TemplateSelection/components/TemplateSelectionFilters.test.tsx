import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import TemplateSelectionFilter from '../../../../src/pages/TemplateSelection/components/TemplateSelectionFilters';

jest.mock('../../../../src/components/SearchInput', () => (props: any) => (
  <input
    data-testid="search-input"
    value={props.value}
    onChange={e => props.onSearch(e.target.value)}
    placeholder={props.placeholder}
  />
));

jest.mock('../../../../src/components/CategoriesFiltersDrawer', () => (props: any) => (
  <div data-testid="categories-filters-drawer">
    <button onClick={() => props.onFilterChange('ra_categories', [1, 2])}>Set RA Categories</button>
    <button onClick={() => props.onFilterChange('hazard_categories', [3, 4])}>Set Hazard Categories</button>
  </div>
));

jest.mock('../../../../src/context', () => ({
  useDataStoreContext: () => ({setDataStore: jest.fn()}),
}));

jest.mock('../../../../src/services/services', () => ({
  getRiskCategoryList: jest.fn(() => Promise.resolve([])),
  getHazardsList: jest.fn(() => Promise.resolve([])),
}));

jest.mock('react-toastify', () => ({toast: {error: jest.fn()}}));

describe('TemplateSelectionFilter', () => {
  const baseFilters = {search: '', ra_categories: null, hazard_categories: null};
  it('renders search input and categories drawer', () => {
    render(
      <TemplateSelectionFilter
        filters={baseFilters}
        onFilterChange={jest.fn()}
      />
    );
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    expect(screen.getByTestId('categories-filters-drawer')).toBeInTheDocument();
  });

  it('calls onFilterChange for search', () => {
    const onFilterChange = jest.fn();
    render(
      <TemplateSelectionFilter
        filters={baseFilters}
        onFilterChange={onFilterChange}
      />
    );
    fireEvent.change(screen.getByTestId('search-input'), {target: {value: 'abc'}});
    expect(onFilterChange).toHaveBeenCalledWith('search', 'abc');
  });

  it('calls onFilterChange for RA and Hazard categories', () => {
    const onFilterChange = jest.fn();
    render(
      <TemplateSelectionFilter
        filters={baseFilters}
        onFilterChange={onFilterChange}
      />
    );
    fireEvent.click(screen.getByText('Set RA Categories'));
    expect(onFilterChange).toHaveBeenCalledWith('ra_categories', [1, 2]);
    fireEvent.click(screen.getByText('Set Hazard Categories'));
    expect(onFilterChange).toHaveBeenCalledWith('hazard_categories', [3, 4]);
  });

  it('shows toast on data load error', async () => {
    const {toast} = require('react-toastify');
    const {getRiskCategoryList, getHazardsList} = require('../../../../src/services/services');
    getRiskCategoryList.mockImplementationOnce(() => Promise.reject('fail'));
    getHazardsList.mockImplementationOnce(() => Promise.reject('fail'));
    render(
      <TemplateSelectionFilter
        filters={baseFilters}
        onFilterChange={jest.fn()}
      />
    );
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to load data. Please try again later.');
    });
  });
});
