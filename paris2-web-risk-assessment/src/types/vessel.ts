interface SMAEntity {
  id: number | null;
  type: string | null;
  name: string | null;
  ship_party_id: number | null;
}

interface FleetStaffMember {
  id: string;
  full_name: string;
  email: string;
}

interface FleetStaff {
  qhse_deputy_general_manager: FleetStaffMember;
}

interface Vessel {
  id: number;
}

export interface VesselData {
  id: number;
  vessel_account_code_new: string;
  name: string;
  images: any[];
  vessel: Vessel;
  sma_entity: SMAEntity;
  status: string;
  fleet_staff: FleetStaff;
}
