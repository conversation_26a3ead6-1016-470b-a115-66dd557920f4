import React, {useEffect} from 'react';
import {toast} from 'react-toastify';
import {FilterValue} from '../../RAListing/components/RAFilters';
import {getHazardsList, getRiskCategoryList} from '../../../services/services';
import {useDataStoreContext} from '../../../context';
import SearchInput from '../../../components/SearchInput';
import CategoriesFiltersDrawer from '../../../components/CategoriesFiltersDrawer';

import '../../../styles/components/template-selection-filters.scss';

export interface TemplateSelectionFilterValues {
  search: string;
  ra_categories: number[] | null;
  hazard_categories?: number[] | null;
}

export interface TemplateSelectionFilterProps {
  filters: TemplateSelectionFilterValues;
  onFilterChange: (
    key: keyof TemplateSelectionFilterValues,
    value: FilterValue,
  ) => void;
}

const TemplateSelectionFilter: React.FC<TemplateSelectionFilterProps> = ({
  filters,
  onFilterChange,
}) => {
  const {setDataStore} = useDataStoreContext();

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        const [categoryListData, hazardsListData] = await Promise.all([
          getRiskCategoryList(),
          getHazardsList(),
        ]);

        setDataStore(prev => ({
          ...prev,
          riskCategoryList: categoryListData,
          hazardsList: hazardsListData,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      }
    };
    loadBasicDetails();
  }, []);

  return (
    <div className="template-selection-filter">
      <div className="search-filter">
        <SearchInput
          value={filters.search}
          onSearch={value => onFilterChange('search', value)}
          placeholder="Search by Task Required or Keywords"
        />
      </div>
      <CategoriesFiltersDrawer
        filters={filters}
        onFilterChange={onFilterChange}
      />
    </div>
  );
};

export default TemplateSelectionFilter;
