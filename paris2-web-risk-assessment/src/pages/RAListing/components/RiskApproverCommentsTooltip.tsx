import React from 'react';
import {OverlayTrigger, Tooltip} from 'react-bootstrap';
import {RAItemFull} from '../../../types';
import {getApproverStatusText} from '../../CreateRA/AddApproverCard';
import ColoredTile from '../../../components/ColoredTile';
import {CommentIcon} from '../../../utils/svgIcons';

type Props = {
  riskApprovers: RAItemFull['risk_approver'];
};

const reviewerTitles = ['First Approver', 'Second Approver', 'Final Approver'];

const RiskApproverCommentsTooltip: React.FC<Props> = ({riskApprovers}) => {
  // Sort by approval_order if present, fallback to original order
  const sortedApprovers = [
    ...riskApprovers.filter(approver => !!approver.status),
  ].sort((a, b) => {
    if (a.approval_order == null) return 1;
    if (b.approval_order == null) return -1;
    return a.approval_order - b.approval_order;
  });
  const count = sortedApprovers?.length ?? 0;

  // Show tooltip only if there is at least one approver
  if (!count) {
    return (
      <span
        style={{
          cursor: 'default',
          display: 'inline-flex',
          alignItems: 'center',
        }}
      >
        <CommentIcon />
        <span style={{marginLeft: 4, fontWeight: 600}}>-</span>
      </span>
    );
  }

  return (
    <OverlayTrigger
      placement="left"
      overlay={
        <Tooltip id="approver-comments-tooltip" className="approver-tooltip">
          <div style={{width: '100%'}}>
            {sortedApprovers.map((approver, idx) => {
              const [statusText, statusColor] = getApproverStatusText(approver);
              const name = approver.user_name || '-';
              const job = approver.job_title || '';
              const email = approver.user_email
                ? atob(approver.user_email)
                : '';
              const showMeta = job || email;
              // If only one approver, show "Approver" instead of "First Approver"
              const title =
                sortedApprovers.length === 1
                  ? 'Approver'
                  : reviewerTitles[idx] || `Approver ${idx + 1}`;
              return (
                <div
                  key={approver.id}
                  style={{
                    marginBottom: idx !== sortedApprovers.length - 1 ? 16 : 0,
                  }}
                >
                  {/* Reviewer Title */}
                  <div className="approver-title">{title}</div>
                  {/* Name and Status */}
                  <div className="approver-name">
                    <span>{name}</span>
                    <span className="colored-tile">
                      <ColoredTile text={statusText} theme={statusColor} />
                    </span>
                  </div>
                  {/* Meta */}
                  {showMeta && (
                    <div className="approver-meta">{job + ' • ' + email}</div>
                  )}
                  {/* Condition/Reason */}
                  {approver.message && (
                    <div className="approver-message">
                      <div className="approver-message">
                        {approver?.approval_status === 2
                          ? 'Reason for Rejection'
                          : 'Condition for Approval'}
                      </div>
                      <div className="approver-message fw-400">
                        {approver.message.trim()}
                      </div>
                    </div>
                  )}
                  {/* Divider */}
                  {idx !== sortedApprovers.length - 1 && <hr />}
                </div>
              );
            })}
          </div>
        </Tooltip>
      }
      popperConfig={{
        modifiers: [
          {
            name: 'offset',
            options: {
              offset: [0, 8], // [skid, distance] - 8px gap on the left
            },
          },
        ],
      }}
    >
      <span
        style={{
          cursor: 'pointer',
          display: 'inline-flex',
          alignItems: 'center',
        }}
      >
        <CommentIcon />
        <span className="fs-14 fw-400">{count}</span>
      </span>
    </OverlayTrigger>
  );
};

export default RiskApproverCommentsTooltip;
