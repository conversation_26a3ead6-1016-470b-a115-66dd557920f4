import React, {Dropdown} from 'react-bootstrap';
import classNames from 'classnames';
import {ThreeDotsMenuIcon} from '../../../components/icons';
import ArchiveTemplateModal from './ArchiveTemplateModal';
import {Template} from '../../../types/template';
import {BasicUserDetails} from '../../../types/user';
import {useDataStoreContext} from '../../../context';

import '../../../styles/components/template-listing.scss';
import {PreviewTemplateModal} from '../../../components/PreviewTemplateModal';
import {useState} from 'react';
import {useNavigate} from 'react-router-dom';

export interface ActionDropdownMenuProps {
  data: Pick<
    Template,
    | 'id'
    | 'task_requiring_ra'
    | 'template_category'
    | 'template_hazards'
    | 'template_keywords'
    | 'createdAt'
    | 'created_at'
  >;
  userDetails: Partial<Pick<BasicUserDetails, 'full_name' | 'email'>>;
  onSuccess?: () => void;
  menuAlign?: 'horizontal' | 'vertical';
  showModal?: boolean;
}

export const ActionDropdownMenu = ({
  data,
  userDetails,
  onSuccess,
  menuAlign = 'horizontal',
  showModal = false,
}: ActionDropdownMenuProps) => {
  const {roleConfig} = useDataStoreContext();
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const navigate = useNavigate();

  const showThreeDotsMenu =
    roleConfig.riskAssessment.canViewTemplate ||
    roleConfig.riskAssessment.canArchiveTemplate;

  return (
    <>
      <Dropdown className="ra-three-dots-dropdown d-flex align-items-center justify-content-center">
        <Dropdown.Toggle
          as="div"
          className={classNames(
            'dropdown-toggle-no-caret',
            menuAlign === 'vertical' ? 'ra-menu-vertical' : null,
          )}
          hidden={!showThreeDotsMenu}
        >
          <ThreeDotsMenuIcon />
        </Dropdown.Toggle>
        <Dropdown.Menu
          className="dropdown-menu-right text-style"
          popperConfig={{
            modifiers: [
              {name: 'preventOverflow', options: {boundary: 'viewport'}},
            ],
          }}
        >
          {roleConfig.riskAssessment.canViewTemplate && (
            <Dropdown.Item
              onClick={() => {
                showModal
                  ? setShowPreviewModal(true)
                  : navigate(
                      `/risk-assessment/template-listing/view/${data?.id}`,
                    );
              }}
            >
              View Template
            </Dropdown.Item>
          )}
          {roleConfig.riskAssessment.canArchiveTemplate && (
            <ArchiveTemplateModal
              templateId={data.id}
              trigger={<Dropdown.Item>Archive</Dropdown.Item>}
              templateName={data.task_requiring_ra}
              riskCategories={data.template_category}
              hazardCategories={data.template_hazards}
              keywords={data.template_keywords}
              createdOn={data.created_at || data.createdAt}
              userName={userDetails.full_name || userDetails.email || 'N/A'}
              onSuccess={() => onSuccess?.()}
            />
          )}
        </Dropdown.Menu>
      </Dropdown>
      {showPreviewModal && (
        <PreviewTemplateModal
          onClose={() => setShowPreviewModal(false)}
          id={data?.id}
          canUseTemplate={false}
        />
      )}
    </>
  );
};
