import React, {forwardRef, useImperativeHandle} from 'react';
import {Col, Form, Alert, Row} from 'react-bootstrap';
import {InputComponent} from '../../components/InputComponent';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {useDataStoreContext} from '../../context';
import moment from 'moment';
import FormCheckRadio from '../../components/FormCheckRadio';
import {format} from 'date-fns';
import {RISK_RATING_NOTE} from '../../constants/template';
import {
  calculateRiskRating,
  getAssessmentArray,
  getRiskRatingBackgroundColor,
  getRiskRatingTextColor,
  validateRequiredField,
  validateTaskReliabilityAnswers,
} from '../../utils/helper';
import LevelOfRATag from '../../components/LevelOfRATag';

// Extracted header component
const RiskRatingHeader = ({
  form,
  riskRating,
}: {
  form: TemplateForm | RiskForm;
  riskRating: string;
}) => (
  <>
    <div className="d-flex justify-content-between align-items-center mb-3">
      <div>
        <div style={{color: '#1F4A70', fontSize: '20px', fontWeight: 600}}>
          {form?.task_requiring_ra || ''}
        </div>
        <div className="d-flex align-items-center">
          {'date_risk_assessment' in form && form.date_risk_assessment ? (
            <div className="text-muted fs-14 d-flex align-items-center">
              Date of Risk Assessment:{' '}
              {format(new Date(form.date_risk_assessment), 'dd MMM yyyy')}
            </div>
          ) : (
            <div className="text-muted fs-14 d-flex align-items-center">
              Date of Risk Assessment: {moment().format('DD MMM YYYY')}
            </div>
          )}
          {(form as RiskForm)?.ra_level === 4 && <LevelOfRATag />}
        </div>
      </div>
      <div
        className="d-flex align-items-center gap-2"
        style={{flexDirection: 'column'}}
      >
        <div className="d-flex align-items-center gap-2">
          <span className="me-2" style={{paddingRight: '8px'}}>
            Overall Risk Rating
          </span>
        </div>
        <div
          style={{
            backgroundColor: getRiskRatingBackgroundColor(riskRating),
            color: getRiskRatingTextColor(riskRating),
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 500,
            padding: '2px 8px',
            alignSelf: 'end',
            marginTop: '0.5rem',
          }}
        >
          {riskRating}
        </div>
      </div>
    </div>
    <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />
    <Col className="ra-negate-padding">
      <Alert
        variant="warning"
        className="py-2 fs-14"
        style={{
          borderColor: getRiskRatingBackgroundColor(riskRating),
          backgroundColor: getRiskRatingBackgroundColor(riskRating),
          color: getRiskRatingTextColor(riskRating),
        }}
      >
        {
          RISK_RATING_NOTE[
            riskRating.toUpperCase() as keyof typeof RISK_RATING_NOTE
          ]
        }
      </Alert>
    </Col>
  </>
);

// Extracted form fields component
const ScenarioInput = ({
  form,
  setForm,
  handleBlur,
  disableHeader,
  previewOnly = false,
}: {
  form: TemplateForm | RiskForm;
  setForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  handleBlur: (e: React.FocusEvent<any>) => void;
  disableHeader: boolean;
  previewOnly?: boolean;
}) => (
  <Col md={disableHeader ? 12 : 9} className="ra-negate-padding">
    <InputComponent
      label="Worst Case Scenario"
      name="worst_case_scenario"
      value={form.worst_case_scenario}
      onChange={(
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      ) =>
        setForm(prev => ({
          ...prev,
          worst_case_scenario: e.target.value,
        }))
      }
      onBlur={handleBlur}
      placeholder="Mention all the possible Worst Case Scenarios"
      type="textarea"
      formControlId="worst_case_scenario"
      form={form}
      maxLength={4000}
      showMaxLength={!previewOnly}
      rows={4}
      classes={{label: 'fs-16 fw-600'}}
      disabled={previewOnly}
    />
  </Col>
);

const RecoveryMeasuresInput = ({
  form,
  setForm,
  handleBlur,
  disableHeader,
  previewOnly = false,
}: {
  form: TemplateForm | RiskForm;
  setForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  handleBlur: (e: React.FocusEvent<any>) => void;
  disableHeader: boolean;
  previewOnly?: boolean;
}) => (
  <Col md={disableHeader ? 12 : 9} className="ra-negate-padding">
    <InputComponent
      label="Recovery Measures"
      name="recovery_measures"
      value={form.recovery_measures}
      onChange={(
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      ) =>
        setForm(prev => ({
          ...prev,
          recovery_measures: e.target.value,
        }))
      }
      onBlur={handleBlur}
      placeholder="Mention the Recovery Measures to be Followed"
      type="textarea"
      formControlId="recovery_measures"
      form={form}
      maxLength={4000}
      showMaxLength={!previewOnly}
      rows={4}
      classes={{label: 'fs-16 fw-600'}}
      disabled={previewOnly}
    />
  </Col>
);

type RiskAssessmentDetailsProps = {
  form: TemplateForm | RiskForm;
  setForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  previewOnly?: boolean;
};

const RiskAssessmentDetails: React.FC<RiskAssessmentDetailsProps> = ({
  form,
  setForm,
  previewOnly = false,
}) => {
  const {
    dataStore: {taskReliabilityAssessList},
  } = useDataStoreContext();

  // Initialize from form values
  const [answers, setAnswers] = React.useState<{[id: number]: string}>({});
  const [yesMessages, setYesMessages] = React.useState<{[id: number]: string}>(
    {},
  );

  // Sync local state with form values
  React.useEffect(() => {
    const initialAnswers: {[id: number]: string} = {};
    const initialYesMessages: {[id: number]: string} = {};
    const assessments = getAssessmentArray(form);
    assessments.forEach((item: any) => {
      initialAnswers[item.task_reliability_assessment_id] =
        item.task_reliability_assessment_answer;
      if (item.task_reliability_assessment_answer === 'Yes') {
        initialYesMessages[item.task_reliability_assessment_id] =
          item.condition || '';
      }
    });
    setAnswers(initialAnswers);
    setYesMessages(initialYesMessages);
  }, [
    'template_task_reliability_assessment' in form
      ? form.template_task_reliability_assessment
      : form.risk_task_reliability_assessment,
  ]);

  const handleRadioChange = (id: number, value: string) => {
    setAnswers(prev => ({...prev, [id]: value}));
    // Clear message if not 'Yes'
    if (value !== 'Yes') {
      setYesMessages(prev => ({...prev, [id]: ''}));
    }
    updateFormAssessments(id, value);
  };

  const handleYesMessageChange = (id: number, value: string) => {
    setYesMessages(prev => ({...prev, [id]: value}));
    updateFormAssessments(id, 'Yes', value);
  };

  // Extract update logic to reduce complexity
  const updateFormAssessments = (
    id: number,
    value: string,
    condition?: string,
  ) => {
    setForm(prev => {
      const prevAssessments: any[] = getAssessmentArray(prev);
      const idx = prevAssessments.findIndex(
        a => a.task_reliability_assessment_id === id,
      );
      const updated = [...prevAssessments];

      const defaultMessage = value === 'Yes' ? yesMessages[id] ?? '' : '';
      const messageValue = condition ?? defaultMessage;

      if (idx !== -1) {
        updated[idx] = {
          ...updated[idx],
          task_reliability_assessment_answer: value,
          condition: messageValue,
        };
      } else {
        updated.push({
          task_reliability_assessment_id: id,
          task_reliability_assessment_answer: value,
          condition: messageValue,
        });
      }

      if ('template_task_reliability_assessment' in prev) {
        return {
          ...prev,
          template_task_reliability_assessment: updated,
        };
      } else {
        return {
          ...prev,
          risk_task_reliability_assessment: updated,
        };
      }
    });
  };

  return (
    <div>
      <div className="mb-3 mt-4" style={{fontSize: '16px', fontWeight: 500}}>
        Task Reliability Assessment
      </div>
      {taskReliabilityAssessList.map((q: any, idx: number) => {
        // Always include Yes and No, plus any other options from q.options (excluding Yes/No to avoid duplicates)
        const radioOptions = [
          'Yes',
          'No',
          ...q.options.filter((opt: string) => opt !== 'Yes' && opt !== 'No'),
        ];
        const selected = answers[q?.id];

        return (
          <Form.Group as={Row} className="mb-3" key={q.id}>
            <Form.Label
              column
              sm={12}
              className="fw-normal"
              style={{fontSize: '14px'}}
            >
              ({idx + 1}) {q.name}
            </Form.Label>
            <Col>
              <div className="d-flex flex-wrap align-items-center">
                {radioOptions.map((opt: string) => (
                  <FormCheckRadio
                    checked={selected === opt}
                    onChange={() => handleRadioChange(q.id, opt)}
                    name={`question-${q.id}`}
                    value={opt}
                    label={opt}
                    key={opt}
                    className="me-3 mr-4 fs-14 fw-400"
                    id={`question-${q.id}-${opt}`}
                    disabled={q.disabled || previewOnly}
                  />
                ))}
              </div>
              {/* Show textbox if 'Yes' is selected */}
              {selected === 'Yes' && q?.id === 1 && (
                <div className="mt-2">
                  <Col md={4} className="ra-negate-padding">
                    <Form.Control
                      name={`yes_message_${q.id}`}
                      value={yesMessages[q.id] || ''}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        handleYesMessageChange(q.id, e.target.value);
                      }}
                      type="text"
                      maxLength={255}
                      id={`yes_message_${q.id}`}
                      placeholder='State the "Stand by Units" or "Redundancy"'
                      disabled={previewOnly}
                      autoComplete="off"
                    />
                  </Col>
                </div>
              )}
            </Col>
          </Form.Group>
        );
      })}
    </div>
  );
};

export default RiskAssessmentDetails;

type RiskRatingStepProps = {
  form: TemplateForm | RiskForm;
  setForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  onValidate?: (valid: boolean) => void;
  disableHeader?: boolean;
  previewOnly?: boolean;
};

export const RiskRatingStep = forwardRef<any, RiskRatingStepProps>(
  (
    {form, setForm, onValidate, disableHeader = false, previewOnly = false},
    ref,
  ) => {
    const riskRating = calculateRiskRating(form);

    // Validation logic - refactored to reduce complexity
    const validate = () => {
      const errors: {[key: string]: string} = {};
      const errorMessage = 'This is a mandatory field. Please fill to process.';

      // Validate required text fields
      if (validateRequiredField(form.worst_case_scenario)) {
        errors.worst_case_scenario = errorMessage;
      }

      if (validateRequiredField(form.recovery_measures)) {
        errors.recovery_measures = errorMessage;
      }

      // Validate task reliability assessment answers
      const assessments = getAssessmentArray(form);

      if (validateTaskReliabilityAnswers(assessments)) {
        errors.template_task_reliability_assessment =
          'Please answer all Task Reliability Assessment questions.';
      }

      // Additional validation for "Yes" answer with empty message for question 1
      assessments.forEach((assessment: any) => {
        if (
          assessment.task_reliability_assessment_id === 1 &&
          assessment.task_reliability_assessment_answer === 'Yes' &&
          (!assessment.condition || assessment.condition.trim() === '')
        ) {
          errors[`assessment_${assessment.task_reliability_assessment_id}`] =
            'Please provide details when selecting "Yes"';
        }
      });

      const isValid = Object.keys(errors).length === 0;
      if (onValidate) onValidate(isValid);
      return isValid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    React.useEffect(() => {
      validate();
      // eslint-disable-next-line
    }, [
      form.worst_case_scenario,
      form.recovery_measures,
      'template_task_reliability_assessment' in form
        ? form.template_task_reliability_assessment
        : form.risk_task_reliability_assessment,
    ]);

    const handleBlur = (_e: React.FocusEvent<any>) => {
      validate();
    };

    return (
      <div>
        {!disableHeader && (
          <RiskRatingHeader form={form} riskRating={riskRating} />
        )}

        <ScenarioInput
          form={form}
          setForm={setForm}
          handleBlur={handleBlur}
          disableHeader={disableHeader}
          previewOnly={previewOnly}
        />

        <RiskAssessmentDetails
          form={form}
          setForm={setForm}
          previewOnly={previewOnly}
        />

        <RecoveryMeasuresInput
          form={form}
          setForm={setForm}
          handleBlur={handleBlur}
          disableHeader={disableHeader}
          previewOnly={previewOnly}
        />
      </div>
    );
  },
);

RiskRatingStep.displayName = 'RiskRatingStep';
