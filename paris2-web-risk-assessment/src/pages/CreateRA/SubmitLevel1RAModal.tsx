import React, {useState} from 'react';
import {Modal, Button, Form} from 'react-bootstrap';
import CustomDatePicker from '../../components/CustomDatePicker';

interface SubmitLevel1RAModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  setForm: (f: any) => void;
}

const SubmitLevel1RAModal: React.FC<SubmitLevel1RAModalProps> = ({
  show,
  onClose,
  onConfirm,
  setForm,
}) => {
  const [approvalDate, setApprovalDate] = useState<Date | undefined>(undefined);
  const handleSubmit = () => {
    return onConfirm();
  };

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title className="fs-20">Submitting Level 1 RA</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div
          style={{
            background: '#FDEAEA',
            color: '#D32F2F',
            borderRadius: 4,
            padding: 16,
            marginBottom: 20,
            fontWeight: 500,
            fontSize: 16,
          }}
        >
          <div>
            <strong>Do you want to submit this Level 1 Risk Assessment?</strong>
          </div>
          <div style={{fontWeight: 400, fontSize: 14}}>
            Submitting this would mean auto approval and vessel will be Notified
            along with an Automated Email.
          </div>
        </div>
        <Form.Group>
          <CustomDatePicker
            isRequired={true}
            minDate={undefined}
            label="Date of Approval"
            value={approvalDate}
            onChange={date => {
              setApprovalDate(date);
              setForm((prev: any) => ({...prev, approval_date: date}));
            }}
            placeholder="Select Date"
            controlId="approval_date"
            errorMsg=""
          />
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={() => handleSubmit()}
          disabled={!approvalDate}
        >
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default SubmitLevel1RAModal;
