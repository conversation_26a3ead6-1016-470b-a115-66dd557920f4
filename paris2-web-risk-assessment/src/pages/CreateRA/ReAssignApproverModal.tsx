import React, {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>} from 'react-bootstrap';
import {
  AsyncSearchCrewMember,
  AsyncSearchCrewMemberProps,
} from '../../components/SearchCrewMember';
import {OfficeApprover} from '../../types';

import '../../styles/components/re-assign-approver-modal.scss';

type Props = {
  onConfirm: (selectedUser: OfficeApprover, order: number) => Promise<void>;
  trigger: React.ReactElement;
  approverOrder: number;
  fetchQuery: AsyncSearchCrewMemberProps<OfficeApprover>['fetchQuery'];
};

export const ReAssignApproverModal: React.FC<Props> = ({
  onConfirm,
  trigger,
  approverOrder,
  fetchQuery,
}) => {
  const [show, setShow] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<OfficeApprover | null>(null);

  const handleTriggerClick = () => {
    setShow(true);
  };

  const handleClose = () => {
    setShow(false);
    setSelectedUser(null);
  };

  const handleConfirm = async () => {
    if (selectedUser) {
      try {
        setIsLoading(true);
        await onConfirm(selectedUser, approverOrder);
      } catch (error) {
        console.error('Error re-assigning reviewer:', error);
      } finally {
        setIsLoading(false);
      }

      handleClose();
    }
  };

  return (
    <>
      {trigger &&
        React.cloneElement(trigger, {
          onClick: handleTriggerClick,
        })}
      <Modal
        show={show}
        onHide={handleClose}
        size="lg"
        backdrop="static"
        className="reassign-approver-modal"
      >
        <Modal.Header>
          <Modal.Title className="fs-20">Re-Assigning Reviewer</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-warning yellow-text-warning">
            <span style={{fontWeight: 600}}>
              Do you want to re-assign the reviewer?
            </span>
            Add a new reviewer to re-assign. We will notify them once added.
          </div>
          <AsyncSearchCrewMember<OfficeApprover>
            value={selectedUser?.email ? [selectedUser.email] : []}
            placeholder="Search Name, Rank or Email ID"
            onChange={(selectedUserIds, originalData) => {
              const selectedUserId = selectedUserIds[0];

              if (selectedUserId && originalData?.length) {
                const selectedUser = originalData.find(
                  user => user.user_id === selectedUserId,
                );
                if (selectedUser) {
                  setSelectedUser(selectedUser);
                }
              } else {
                setSelectedUser(null);
              }
            }}
            fetchQuery={fetchQuery}
            uniqueQueryKey={`getOfficeApprovers-${approverOrder}`}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="primary"
            className="me-2 fs-14"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="secondary"
            className="me-2 fs-14"
            onClick={handleConfirm}
            disabled={!selectedUser || isLoading}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default ReAssignApproverModal;
