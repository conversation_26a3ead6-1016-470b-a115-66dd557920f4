import classNames from 'classnames';
import React from 'react';
import {Button} from 'react-bootstrap';
import {Link} from 'react-router-dom';
import {useDataStoreContext} from '../../context';

interface RADraftHeaderProps {
  activeTab: number;
  setActiveTab: (value: number) => void;
}

export const RADraftHeader: React.FC<RADraftHeaderProps> = props => {
  const {
    roleConfig: {
      riskAssessment: {canViewDraftRA = false, canViewDraftTemplate = false},
    },
  } = useDataStoreContext();
  return (
    <div className={'d-flex justify-content-between align-items-center mb-4'}>
      <div className="fs-24 secondary-color">
        <Link className="underline fs-24 secondary-color" to="/risk-assessment">
          Risk Assessment
        </Link>
        {' / '}
        <span className="fs-24 secondary-color">Drafts</span>
      </div>

      <div className="d-flex border rounded overflow-hidden">
        <Button
          variant="light"
          className={classNames(
            'draft-listing-tab',
            props.activeTab === 1 ? 'active-tab' : 'inactive-tab',
          )}
          disabled={!canViewDraftRA}
          onClick={() => props.setActiveTab(1)}
        >
          Risk Assessment
        </Button>
        <Button
          variant="light"
          className={classNames(
            'draft-listing-tab',
            props.activeTab === 2 ? 'active-tab' : 'inactive-tab',
          )}
          disabled={!canViewDraftTemplate}
          onClick={() => props.setActiveTab(2)}
        >
          RA Template
        </Button>
      </div>
    </div>
  );
};
