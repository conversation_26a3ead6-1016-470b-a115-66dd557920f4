import React from 'react';
import {Link} from 'react-router-dom';

type BreadCrumbItem = {
  title: string;
  link?: string;
  state?: any;
  onClick?: (event: React.MouseEvent, link: string) => void;
};

export type ProjectBreadCrumbProps = {
  items: BreadCrumbItem[];
  options?: React.ReactElement;
};

const ProjectBreadCrumb: React.FC<ProjectBreadCrumbProps> = ({
  items,
  options,
}) => {
  return (
    <div className="d-flex justify-content-between align-items-center w-100">
      <div className="fs-24" style={{color: '#1F4A70'}}>
        {items.map((item, idx) => (
          <span key={`link-${item.title}`}>
            {item.link ? (
              <Link
                className="underline fs-24"
                style={{
                  color: '#1F4A70',
                }}
                to={item.link}
                state={item.state}
                onClick={event => item.onClick?.(event, item.link || '')}
              >
                {item.title}
              </Link>
            ) : (
              <span className="fs-24">{item.title}</span>
            )}
            {idx < items.length - 1 && ' / '}
          </span>
        ))}
      </div>
      {options || null}
    </div>
  );
};

export default ProjectBreadCrumb;
