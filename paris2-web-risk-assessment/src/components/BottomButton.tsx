import React from 'react';
import {Button} from 'react-bootstrap';
import '../styles/styles.scss';

export type ButtonConfig = {
  title: string;
  testID?: string;
  variant?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  customClass?: string;
};

export type BottomButtonProps = {
  buttons: ButtonConfig[];
  customContainerClass?: string;
};

const BottomButton = ({buttons, customContainerClass}: BottomButtonProps) => {
  return (
    <div
      className={`fixed-bottom bottom-component ${customContainerClass || ''}`}
    >
      {buttons.map(btn => (
        <Button
          key={`button-${btn.title}`}
          variant={btn.variant || 'primary'}
          className={`bottom-component__button ${btn.customClass || ''}`}
          data-testid={btn.testID}
          onClick={btn.onClick}
          type={btn.type || 'button'}
          disabled={btn.disabled}
        >
          {btn.title}
        </Button>
      ))}
    </div>
  );
};

export default BottomButton;
