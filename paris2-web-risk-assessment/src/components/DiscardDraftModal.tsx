import React, {useState} from 'react';
import {<PERSON><PERSON>, Button} from 'react-bootstrap';
import {deleteRiskById, deleteTemplateById} from '../services/services';

type Props = {
  onClose: (isRefetch?: boolean) => void;
  id: number;
  activeTab: number;
};

export const DiscardDraftModal: React.FC<Props> = ({
  onClose,
  id,
  activeTab,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      if (activeTab === 2) {
        await deleteTemplateById(id);
      } else {
        await deleteRiskById(id);
      }
    } catch (error) {
      console.error('Error deleting Inspection:', error);
    } finally {
      setIsDeleting(false);
      onClose(true);
    }
  };

  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title className="fs-20">Discard Draft</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <div
          className="alert d-flex align-items-center fs-14 ra-alert-warning"
          role="alert"
          data-testid="error-alert"
        >
          <div>
            <strong>Are you sure you want to discard this draft?</strong>
            <span> All entered information will be lost permanently.</span>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={handleConfirm}
          disabled={isDeleting}
        >
          Discard
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={() => onClose()}
          disabled={isDeleting}
        >
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
