import React from 'react';
import {OverlayTrigger, Popover as BootstrapPopover} from 'react-bootstrap';
import '../styles/components/single-badge-popover.scss';

interface PopoverProps {
  text: string;
  children: React.ReactElement;
}

const Popover: React.FC<PopoverProps> = ({text, children}) => {
  const popover = (
    <BootstrapPopover id="basic-popover" className="ra-single-badge-popover">
      <BootstrapPopover.Body className="ra-single-badge-popover-body">
        {text}
      </BootstrapPopover.Body>
    </BootstrapPopover>
  );

  return (
    <OverlayTrigger
      trigger={['hover', 'focus']}
      placement="top"
      overlay={popover}
      delay={{show: 200, hide: 100}}
    >
      {children}
    </OverlayTrigger>
  );
};

export default Popover;
