import React from 'react';
import {OverlayTrigger, Popover} from 'react-bootstrap';
import '../styles/components/single-badge-popover.scss';

interface TruncateTextProps {
  text: string;
  maxLength: number;
}

const TruncateText: React.FC<TruncateTextProps> = ({text, maxLength}) => {
  const isTruncated = text.length > maxLength;
  const displayText = isTruncated ? text.slice(0, maxLength) + '...' : text;

  const popover = (
    <Popover id="truncate-text-popover" className="ra-single-badge-popover">
      <Popover.Body className="ra-single-badge-popover-body">
        {text}
      </Popover.Body>
    </Popover>
  );

  return (
    <OverlayTrigger
      trigger={['hover', 'focus']}
      placement="top"
      overlay={isTruncated ? popover : <></>}
    >
      <span style={{cursor: isTruncated ? 'pointer' : 'default'}}>
        {displayText}
      </span>
    </OverlayTrigger>
  );
};

export default TruncateText;
