import {Dropdown} from 'react-bootstrap';
import {ThreeDotsMenuIcon} from './icons';

type ActionsDropdownCellProps = {
  jobId: string;
  setEditStep: (step: number) => void;
  setEditModalTitle: (title: string) => void;
  setSelectedJobIdx: (id: string) => void;
  setIsEdit: (isEdit: boolean) => void;
  setShowDeleteJobModal: (show: boolean) => void;
};

export const ActionsDropdownCell: React.FC<ActionsDropdownCellProps> = ({
  jobId,
  setEditStep,
  setEditModalTitle,
  setSelectedJobIdx,
  setIsEdit,
  setShowDeleteJobModal,
}) => {
  const handleEdit = () => {
    setEditStep(5);
    setEditModalTitle('Edit Associated Job');
    setSelectedJobIdx(jobId);
    setIsEdit(true);
  };

  const handleDelete = () => {
    setSelectedJobIdx(jobId);
    setShowDeleteJobModal(true);
  };

  return (
    <Dropdown className="ra-three-dots-dropdown d-flex align-items-center justify-content-center">
      <Dropdown.Toggle as="div" className="dropdown-toggle-no-caret">
        <ThreeDotsMenuIcon />
      </Dropdown.Toggle>
      <Dropdown.Menu
        className="dropdown-menu-right text-style"
        popperConfig={{
          modifiers: [
            {name: 'preventOverflow', options: {boundary: 'viewport'}},
          ],
        }}
      >
        <Dropdown.Item onClick={handleEdit}>Edit</Dropdown.Item>
        <Dropdown.Item onClick={handleDelete}>Delete</Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};
