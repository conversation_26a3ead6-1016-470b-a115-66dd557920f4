import React from 'react';

interface TrashIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const TrashIcon: React.FC<TrashIconProps> = ({
  width = 12,
  height = 14,
  color = '#1F4A70',
  className = '',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 12 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10.5187 2.08001H8.63551V1.70321C8.63509 1.40375 8.51594 1.11668 8.30419 0.904928C8.09244 0.693179 7.80537 0.574032 7.50591 0.573608L4.49391 0.573608C4.19445 0.574032 3.90738 0.693179 3.69563 0.904928C3.48388 1.11668 3.36473 1.40375 3.36431 1.70321V2.08001H1.48111C1.21235 2.08002 0.952405 2.17586 0.747956 2.35031C0.543508 2.52475 0.407956 2.76638 0.365649 3.03179C0.323341 3.2972 0.37705 3.56899 0.517129 3.79836C0.657207 4.02773 0.874476 4.19964 1.12991 4.28321L1.80191 12.3912C1.82681 12.6731 1.956 12.9355 2.16419 13.1271C2.37237 13.3187 2.64457 13.4257 2.92751 13.4272H9.07151C9.35509 13.4256 9.62781 13.318 9.83609 13.1256C10.0444 12.9332 10.1732 12.6698 10.1971 12.3872L10.8683 4.27921C11.1237 4.19564 11.341 4.02373 11.4811 3.79436C11.6212 3.56499 11.6749 3.2932 11.6326 3.02779C11.5903 2.76238 11.4547 2.52075 11.2503 2.34631C11.0458 2.17186 10.7859 2.07602 10.5171 2.07601L10.5187 2.08001ZM4.11871 1.70321C4.11871 1.60327 4.15841 1.50743 4.22907 1.43677C4.29974 1.36611 4.39558 1.32641 4.49551 1.32641H7.50831C7.60824 1.32641 7.70409 1.36611 7.77475 1.43677C7.84541 1.50743 7.88511 1.60327 7.88511 1.70321V2.08001H4.11711L4.11871 1.70321ZM9.44751 12.328C9.43933 12.422 9.39632 12.5096 9.3269 12.5735C9.25749 12.6375 9.16669 12.6732 9.07231 12.6736H2.92751C2.83327 12.6732 2.74257 12.6376 2.67317 12.5738C2.60378 12.5101 2.56068 12.4227 2.55231 12.3288L1.89071 4.33921H10.1115L9.44751 12.328ZM10.5187 3.58641H1.47871C1.42782 3.58876 1.37698 3.58076 1.32927 3.5629C1.28156 3.54504 1.23797 3.5177 1.20112 3.48251C1.16428 3.44733 1.13496 3.40504 1.11492 3.3582C1.09488 3.31137 1.08455 3.26095 1.08455 3.21001C1.08455 3.15907 1.09488 3.10865 1.11492 3.06181C1.13496 3.01497 1.16428 2.97269 1.20112 2.9375C1.23797 2.90232 1.28156 2.87497 1.32927 2.85712C1.37698 2.83926 1.42782 2.83126 1.47871 2.83361H10.5187C10.5696 2.83126 10.6204 2.83926 10.6681 2.85712C10.7159 2.87497 10.7595 2.90232 10.7963 2.9375C10.8331 2.97269 10.8625 3.01497 10.8825 3.06181C10.9025 3.10865 10.9129 3.15907 10.9129 3.21001C10.9129 3.26095 10.9025 3.31137 10.8825 3.3582C10.8625 3.40504 10.8331 3.44733 10.7963 3.48251C10.7595 3.5177 10.7159 3.54504 10.6681 3.5629C10.6204 3.58076 10.5696 3.58876 10.5187 3.58641Z"
        fill={color}
      />
      <path
        d="M4.4924 11.5208L4.11559 5.44559C4.10743 5.34738 4.06113 5.25627 3.9866 5.19179C3.91207 5.12732 3.81525 5.09462 3.71688 5.10069C3.61852 5.10676 3.52646 5.15112 3.46041 5.22426C3.39437 5.29741 3.35961 5.39352 3.36359 5.49199L3.7404 11.5672C3.74242 11.6173 3.75443 11.6665 3.77572 11.7119C3.79701 11.7573 3.82715 11.798 3.86438 11.8316C3.90161 11.8653 3.94518 11.8911 3.99252 11.9076C4.03986 11.9242 4.09003 11.9311 4.14009 11.9281C4.19015 11.925 4.2391 11.9119 4.28405 11.8897C4.329 11.8674 4.36904 11.8364 4.40186 11.7985C4.43467 11.7605 4.45958 11.7165 4.47512 11.6688C4.49067 11.6211 4.49655 11.5708 4.4924 11.5208Z"
        fill={color}
      />
      <path
        d="M5.99984 5.09204C5.89991 5.09204 5.80407 5.13174 5.73341 5.2024C5.66275 5.27307 5.62305 5.3689 5.62305 5.46884V11.544C5.62751 11.6409 5.66912 11.7323 5.73922 11.7992C5.80932 11.8661 5.90252 11.9035 5.99945 11.9035C6.09638 11.9035 6.18958 11.8661 6.25968 11.7992C6.32978 11.7323 6.37138 11.6409 6.37584 11.544V5.46884C6.37584 5.36904 6.33626 5.27333 6.26577 5.20269C6.19528 5.13204 6.09964 5.09225 5.99984 5.09204Z"
        fill={color}
      />
      <path
        d="M8.28136 5.09207C8.23192 5.08892 8.18236 5.09556 8.13549 5.1116C8.08862 5.12763 8.04537 5.15276 8.00822 5.18553C7.97106 5.21829 7.94072 5.25806 7.91896 5.30257C7.8972 5.34707 7.88442 5.39542 7.88136 5.44486L7.50617 11.5201C7.50219 11.6185 7.53695 11.7146 7.60299 11.7878C7.66903 11.8609 7.7611 11.9053 7.85946 11.9114C7.95782 11.9174 8.05463 11.8847 8.12916 11.8203C8.2037 11.7558 8.25001 11.6647 8.25817 11.5665L8.63498 5.49126C8.63801 5.44182 8.63126 5.39228 8.61513 5.34545C8.59899 5.29862 8.57377 5.25543 8.54093 5.21835C8.50809 5.18127 8.46826 5.15104 8.42372 5.12937C8.37918 5.1077 8.3308 5.09502 8.28136 5.09207Z"
        fill={color}
      />
    </svg>
  );
};

export default TrashIcon;
