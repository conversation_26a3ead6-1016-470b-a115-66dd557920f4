import React from 'react';

interface ExternalLinkIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  color?: string;
}

const ExternalLinkIcon: React.FC<ExternalLinkIconProps> = ({
  size = 16,
  color = '#1F4A70',
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_2518_15048)">
        <path
          d="M13.3346 15.3327H2.66797C2.13754 15.3327 1.62883 15.122 1.25376 14.7469C0.878682 14.3718 0.667969 13.8631 0.667969 13.3327V2.66602C0.667969 2.13558 0.878682 1.62687 1.25376 1.2518C1.62883 0.876729 2.13754 0.666016 2.66797 0.666016H6.66797C6.84478 0.666016 7.01435 0.736253 7.13937 0.861278C7.2644 0.986302 7.33464 1.15587 7.33464 1.33268C7.33464 1.50949 7.2644 1.67906 7.13937 1.80409C7.01435 1.92911 6.84478 1.99935 6.66797 1.99935H2.66797C2.49116 1.99935 2.32159 2.06959 2.19656 2.19461C2.07154 2.31964 2.0013 2.4892 2.0013 2.66602V13.3327C2.0013 13.5095 2.07154 13.6791 2.19656 13.8041C2.32159 13.9291 2.49116 13.9993 2.66797 13.9993H13.3346C13.5114 13.9993 13.681 13.9291 13.806 13.8041C13.9311 13.6791 14.0013 13.5095 14.0013 13.3327V9.33268C14.0013 9.15587 14.0715 8.9863 14.1966 8.86128C14.3216 8.73625 14.4912 8.66602 14.668 8.66602C14.8448 8.66602 15.0143 8.73625 15.1394 8.86128C15.2644 8.9863 15.3346 9.15587 15.3346 9.33268V13.3327C15.3346 13.8631 15.1239 14.3718 14.7488 14.7469C14.3738 15.122 13.8651 15.3327 13.3346 15.3327ZM6.66797 9.99935C6.58023 9.99986 6.49326 9.98304 6.41203 9.94986C6.33081 9.91669 6.25693 9.8678 6.19464 9.80602C6.13215 9.74404 6.08255 9.67031 6.04871 9.58907C6.01486 9.50783 5.99744 9.42069 5.99744 9.33268C5.99744 9.24467 6.01486 9.15754 6.04871 9.0763C6.08255 8.99506 6.13215 8.92132 6.19464 8.85935L13.0613 1.99935H10.668C10.4912 1.99935 10.3216 1.92911 10.1966 1.80409C10.0715 1.67906 10.0013 1.50949 10.0013 1.33268C10.0013 1.15587 10.0715 0.986302 10.1966 0.861278C10.3216 0.736253 10.4912 0.666016 10.668 0.666016H14.668C14.8448 0.666016 15.0143 0.736253 15.1394 0.861278C15.2644 0.986302 15.3346 1.15587 15.3346 1.33268V5.33268C15.3346 5.50949 15.2644 5.67906 15.1394 5.80409C15.0143 5.92911 14.8448 5.99935 14.668 5.99935C14.4912 5.99935 14.3216 5.92911 14.1966 5.80409C14.0715 5.67906 14.0013 5.50949 14.0013 5.33268V2.93935L7.1413 9.80602C7.07901 9.8678 7.00513 9.91669 6.92391 9.94986C6.84268 9.98304 6.75571 9.99986 6.66797 9.99935Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_2518_15048">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ExternalLinkIcon;
