import React from 'react';

interface PlusIconProps {
  width?: number | string;
  height?: number | string;
  color?: string;
  className?: string;
  style?: React.CSSProperties;
}

const PlusIcon: React.FC<PlusIconProps> = ({
  width = 20,
  height = 20,
  color,
  className = '',
  style = {},
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
    >
      <path
        d="M15.3999 9.39998H10.6V4.59996C10.6 4.26883 10.3312 4 9.99995 4C9.66882 4 9.39998 4.26883 9.39998 4.59996V9.39998H4.59996C4.26883 9.39998 4 9.66882 4 9.99995C4 10.3312 4.26883 10.6 4.59996 10.6H9.39998V15.3999C9.39998 15.7312 9.66882 16 9.99995 16C10.3312 16 10.6 15.7312 10.6 15.3999V10.6H15.3999C15.7312 10.6 16 10.3312 16 9.99995C16 9.66882 15.7312 9.39998 15.3999 9.39998Z"
        fill={color || 'currentColor'}
      />
    </svg>
  );
};

export default PlusIcon;
