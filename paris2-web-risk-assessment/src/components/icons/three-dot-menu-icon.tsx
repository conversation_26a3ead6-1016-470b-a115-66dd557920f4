import React from 'react';

interface ThreeDotsMenuProps {
  width?: number | string;
  height?: number | string;
  color?: string;
  className?: string;
  onClick?: () => void;
}

const ThreeDotsMenu: React.FC<ThreeDotsMenuProps> = ({
  width = 20,
  height = 20,
  color = '#1C1F4A',
  className = '',
  onClick,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      onClick={onClick}
    >
      <path
        d="M3.6 11.6C4.48366 11.6 5.2 10.8837 5.2 10C5.2 9.11637 4.48366 8.40002 3.6 8.40002C2.71634 8.40002 2 9.11637 2 10C2 10.8837 2.71634 11.6 3.6 11.6Z"
        fill={color}
      />
      <path
        d="M10.0004 11.6C10.884 11.6 11.6004 10.8837 11.6004 10C11.6004 9.11637 10.884 8.40002 10.0004 8.40002C9.11673 8.40002 8.40039 9.11637 8.40039 10C8.40039 10.8837 9.11673 11.6 10.0004 11.6Z"
        fill={color}
      />
      <path
        d="M16.4008 11.6C17.2844 11.6 18.0008 10.8837 18.0008 10C18.0008 9.11637 17.2844 8.40002 16.4008 8.40002C15.5171 8.40002 14.8008 9.11637 14.8008 10C14.8008 10.8837 15.5171 11.6 16.4008 11.6Z"
        fill={color}
      />
    </svg>
  );
};

export default ThreeDotsMenu;
