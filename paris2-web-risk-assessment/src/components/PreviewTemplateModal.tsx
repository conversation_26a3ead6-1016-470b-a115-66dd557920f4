import React, {useEffect, useState} from 'react';
import {Mo<PERSON>, Button} from 'react-bootstrap';
import * as _ from 'lodash';
import {
  getHazardsList,
  getMainRiskParameterType,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
  getTemplateById,
} from '../services/services';
import {createFormFromData} from '../utils/helper';
import {TemplateForm} from '../types';
import PreviewFormDetails from '../pages/CreateRA/PreviewFormDetails';
import {useNavigate} from 'react-router-dom';
import Loader from './Loader';
import {useDataStoreContext} from '../context';

type Props = {
  onClose: () => void;
  id: number;
  canUseTemplate?: boolean;
};

export const PreviewTemplateModal: React.FC<Props> = ({
  onClose,
  id,
  canUseTemplate = true,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<TemplateForm>(createFormFromData());
  const navigate = useNavigate();
  const {setDataStore} = useDataStoreContext();
  const fetchTemplateData = async (templateId: string) => {
    setIsLoading(true);
    try {
      const response = await getTemplateById(templateId);
      const data = response.result;
      const formData = createFormFromData(data);
      setForm(formData);
    } catch (err) {
      console.error('Error fetching draft', err);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setIsLoading(true);
        const results = await Promise.allSettled([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
        ]);
        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
        ] = results.map(result =>
          result.status === 'fulfilled' ? result.value : [],
        );

        const groupedRiskParameterData = _.chain(riskParameterData)
          .groupBy(item => item?.parameter_type?.id)
          .map(items => ({
            id: items[0]?.parameter_type?.id,
            name: items[0]?.parameter_type?.name,
            parameters: items?.map(i => ({
              id: i?.id,
              name: i?.name,
            })),
          }))
          .value();

        setDataStore((prev: any) => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!id) return;
    fetchTemplateData(id.toString());
  }, [id]);

  return (
    <Modal
      show
      onHide={onClose}
      size="xl"
      backdrop="static"
      dialogClassName="top-modal preview-template-modal"
    >
      <Modal.Header>
        <Modal.Title className="fs-20">Template Preview</Modal.Title>
      </Modal.Header>
      <Modal.Body className="edit-modal-body">
        {isLoading && <Loader isOverlayLoader />}
        <PreviewFormDetails
          form={form}
          setForm={setForm}
          atRiskRef={{current: null}}
          handlePreviewPublush={() => {}}
          handleSaveToDraft={() => {}}
          type="template"
          previewOnly={true}
        />
      </Modal.Body>

      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        {canUseTemplate && (
          <Button
            variant="secondary"
            className="me-2 fs-14"
            disabled={isLoading}
            onClick={() =>
              navigate(`/risk-assessment/templates/${id}/risks/create`)
            }
          >
            Use Template
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};
