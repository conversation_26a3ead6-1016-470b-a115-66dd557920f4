import React from 'react';
import {CheckFilled, CheckUnFilled} from '../utils/svgIcons';
import classNames from 'classnames';

const CheckboxComponent = ({
  checked,
  label,
  onChange,
  id,
  className,
}: {
  checked: boolean;
  label?: string | React.ReactNode;
  onChange: () => void;
  id: string;
  className?: string;
}) => (
  // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
  <label
    htmlFor={id}
    className={classNames('ra-form-check-box fs-14', className)}
    style={{
      display: 'flex',
      alignItems: 'center',
      minHeight: 20,
      cursor: 'pointer',
      color: '#333333',
      fontWeight: 400,
      gap: 8,
      userSelect: 'none',
    }}
    // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
    role="checkbox"
    onClick={e => e.stopPropagation()} // Prevent click from bubbling to parent
    onKeyDown={e => {
      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault();
        onChange();
      }
    }}
    tabIndex={0}
    aria-checked={checked}
  >
    <span
      style={{
        width: 18,
        height: 18,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {checked ? <CheckFilled /> : <CheckUnFilled />}
    </span>
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={event => {
        event.stopPropagation();
        onChange();
      }}
      onClick={event => {
        event.stopPropagation();
      }}
      style={{display: 'none'}}
    />
    {label && <span style={{lineHeight: '20px'}}>{label}</span>}
  </label>
);
export default CheckboxComponent;
