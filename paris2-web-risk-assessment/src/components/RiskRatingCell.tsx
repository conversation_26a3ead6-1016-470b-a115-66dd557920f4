import React from 'react';
import {getRiskLabel} from '../pages/CreateRA/AddJobsStep';
import {getCellColor} from './InitialRiskRatingModal';

type RiskRating = {
  parameter_type_id: number;
  rating: string;
};

type CellRendererProps = {
  rating: RiskRating[];
};

export const RiskRatingCell: React.FC<CellRendererProps> = ({rating}) => {
  const ratingParam: Record<number, string> = {
    1: 'P',
    2: 'E',
    3: 'A',
    4: 'R',
  };

  // Convert rating array to a map for quick lookup
  const ratingMap = new Map(rating.map(r => [r.parameter_type_id, r]));

  return (
    <div className="d-flex flex-column">
      {[1, 2, 3, 4].map((paramId, index) => {
        const risk = ratingMap.get(paramId);
        const riskLabel = risk ? getRiskLabel(risk.rating) : 'Not Selected';
        const bgColor = risk?.rating
          ? getCellColor(risk.rating.split('-')[0]?.trim())
          : '#F6F8FA';
        const textColor = risk ? '#222' : '#333333';
        const borderStyle = risk ? 'none' : '1px solid #e0e0e0';

        return (
          <div key={paramId}>
            <div className="d-flex align-items-center gap-2 mb-2">
              <span style={{width: 20}}>{ratingParam[paramId]}</span>
              <button
                type="button"
                className="risk-rating-pill"
                disabled
                style={{
                  background: bgColor,
                  color: textColor,
                  border: borderStyle,
                }}
              >
                {riskLabel}
              </button>
            </div>
            {index < 3 && <hr className="hr-rem" />}
          </div>
        );
      })}
    </div>
  );
};
