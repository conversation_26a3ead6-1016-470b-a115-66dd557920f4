/**
 * Generates initials from a person's name.
 * Returns the first letter of each word, up to 2 characters.
 * For single names, returns the first 2 characters.
 *
 * @param {string} [name] - The full name to extract initials from
 * @returns {string} The initials in uppercase, or empty string if no name provided
 *
 * @example
 * getInitials('<PERSON>') // Returns: 'JD'
 * getInitials('Madonna') // Returns: 'MA'
 * getInitials('<PERSON><PERSON><PERSON>') // Returns: 'JC'
 * getInitials('') // Returns: ''
 * getInitials(undefined) // Returns: ''
 */
export const getInitials = (name?: string): string => {
  if (!name) return '';
  const names = name.split(' ');
  if (names.length === 1) return names[0].substring(0, 2).toUpperCase();
  return names
    .map(n => n[0])
    .join('')
    .substring(0, 2)
    .toUpperCase();
};
