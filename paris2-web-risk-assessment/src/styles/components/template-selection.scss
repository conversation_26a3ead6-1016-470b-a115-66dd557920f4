.ra-template-selection {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: calc(100vh - 150px);

  .breadcrumb-text {
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    color: #1f4a70;
    margin-bottom: 16px;
  }

  .template-selection-main {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
    gap: 16px;
    overflow: hidden;

    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 6px;

    .main-title {
      font-weight: 600;
      font-size: 20px;
      line-height: 28px;
      color: #1f4a70;
      margin: 0px;
    }

    .all-template-container {
      width: 100%;
      overflow-y: auto;
    }

    .all-template-text {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
      margin-bottom: 12px;
    }
  }

  .template-selection-content {
    overflow-y: auto;
  }

  .template-selection-bottom-btn {
    height: 64px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0px !important;

    button {
      height: 32px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding-top: 0px !important;
      padding-bottom: 0px !important;
    }
  }
}
