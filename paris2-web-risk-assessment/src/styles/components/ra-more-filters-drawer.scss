.ra-more-filters-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;

  .filter-container {
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px 20px;
    overflow: hidden;

    .filter-label {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-bottom: 4px;
    }

    .filter-input {
      margin-bottom: 8px;
    }

    .filters-divider {
      width: full;
      border-bottom: 1px solid #ced4da;
      margin-bottom: 4px;
    }
  }

  .filters-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 20px 20px 16px;
    width: 100%;
    height: 68px;
    gap: 12px;

    border-top: 1px solid #dee2e6;

    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;

    .footer-btn-primary {
      cursor: pointer;
      padding: 6px 12px;
      width: 62px;
      height: 32px;

      background: #1f4a70;
      border: 1px solid #1f4a70;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #ffffff;
    }

    .footer-btn-secondary {
      cursor: pointer;
      padding: 4px 8px 4px 6px;
      width: 49px;
      height: 28px;

      border-radius: 4px;

      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-decoration-line: underline;

      color: #1f4a70;
    }
  }
}

.add-more-filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0px 20px 16px 20px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #333333;
}

.add-more-filter-dropdown-container {
  margin-left: auto;
  z-index: 10;
  padding: 1px;

  &:active,
  &:focus-within,
  &:hover {
    background: #f3f6f8;
    border: 1px solid #89a0b1;
    border-radius: 4px;
    padding: 0px !important;
  }

  font-weight: 400;
  font-size: 14px;

  color: #1f4a70;

  .add-more-filter-dropdown-toggle {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 4px 8px 4px 6px;
    gap: 4px;

    cursor: pointer;
    &::after {
      display: none; // Remove the default caret
    }

    .dropdown-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .dropdown-label {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-decoration-line: underline;
    }
  }
}
