.ra-approval-page {
  .ra-approval-status-dropdown {
    padding: 0px;
    width: 218px;
    height: 32px;
    margin-right: 12px;
  }

  .ra-approval-save-btn {
    all: unset;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 6px 12px !important;
    width: 90px !important;
    height: 32px !important;
    background: #1f4a70 !important;
    border: 1px solid #1f4a70 !important;
    border-radius: 4px !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    color: #ffffff !important;
    cursor: pointer !important;

    &:hover,
    &:focus,
    &:active {
      background-color: darken(#1f4a70, 10%) !important;
    }

    &:disabled {
      background: #efefef !important;
      border: 1px solid #efefef !important;
      border-radius: 4px !important;
      font-size: 14px !important;
      color: #aaaaaa !important;
      cursor: not-allowed !important;
    }
  }
}
