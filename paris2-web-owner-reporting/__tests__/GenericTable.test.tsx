import {act, render, screen} from '@testing-library/react';
import React from 'react';
import {GenericTable} from '../src/components/Table/GenericTable';

describe('GenericTable', () => {
  it('renders a table with header and rows', async () => {
    // Define mock data for table
    const data = [
      {
        col1: 'Hello',
        col2: 'World',
      },
      {
        col1: 'Foo',
        col2: 'Bar',
      },
    ];
    // Define mock columns for table
    const columns = [
      {
        Header: 'Column 1',
        accessor: 'col1',
      },
      {
        Header: 'Column 2',
        accessor: 'col2',
      },
    ];

    // Render the component with mock data and columns
    await act(async () => {
      render(<GenericTable tableOptions={{columns, data}} tablePlugins={[]} />);
    });
    const {getByRole, getByText} = screen;

    // Check that the table element is present
    const table = getByRole('table');
    expect(table).toBeInTheDocument();

    // Check that the header row and cells are present and have correct text
    const headerRow = getByRole('row', {name: 'Column 1 Column 2'});
    expect(headerRow).toBeInTheDocument();
    expect(getByText('Column 1')).toBeInTheDocument();
    expect(getByText('Column 2')).toBeInTheDocument();

    // Check that the rows and cells are present and have correct text
    const row1 = getByRole('row', {name: 'Hello World'});
    expect(row1).toBeInTheDocument();
    expect(getByText('Hello')).toBeInTheDocument();
    expect(getByText('World')).toBeInTheDocument();

    const row2 = getByRole('row', {name: 'Foo Bar'});
    expect(row2).toBeInTheDocument();
    expect(getByText('Foo')).toBeInTheDocument();
    expect(getByText('Bar')).toBeInTheDocument();
  });
});
