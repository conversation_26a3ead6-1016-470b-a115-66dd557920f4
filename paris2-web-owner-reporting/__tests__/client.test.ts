import axios from 'axios';
import userService from '../src/services/user.service';
import {createAxiosInstance} from '../src/utils/client'; // Assuming file is named axiosInstance
// Mock external dependencies
jest.mock('axios');
jest.mock('react-toastify', () => ({
  toast: {
    error: jest.fn(),
  },
}));
jest.mock('../src/services/user.service', () => ({
  getToken: jest.fn(),
}));
jest.mock('../src/utils/error', () => ({
  extractErrorMessage: jest.fn(),
}));
describe('createAxiosInstance', () => {
  let mockAxiosInstance: any;
  const originalLocation = window.location;
  beforeEach(() => {
    mockAxiosInstance = {
      interceptors: {
        request: {
          use: jest.fn(),
        },
        response: {
          use: jest.fn(),
        },
      },
    };
    axios.create.mockReturnValue(mockAxiosInstance);
    // Reset and mock location each time
    delete (window as any).location;
    (window as any).location = {
      pathname: '/',
    };
  });
  afterEach(() => {
    jest.clearAllMocks();
    window.location = originalLocation;
  });
  it('should create an Axios instance with the correct base URL', () => {
    const baseURL = 'https://api.example.com';
    const instance = createAxiosInstance(baseURL);
    expect(axios.create).toHaveBeenCalledWith({
      baseURL,
      signal: expect.any(AbortSignal),
    });
    expect(instance).toBe(mockAxiosInstance);
  });
  it('should add ownership and report ID headers if regex matches', async () => {
    const mockToken = 'mockToken';
    userService.getToken.mockResolvedValue(mockToken);
    // Mock pathname
    Object.defineProperty(window, 'location', {
      value: {pathname: '/owner-reporting/123/reports/456'},
    });
    const instance = createAxiosInstance('https://api.example.com');
    const requestInterceptor =
      mockAxiosInstance.interceptors.request.use.mock.calls[0][0];
    const config = {headers: {}};
    await requestInterceptor(config);
    expect(config.headers).toEqual({
      'x-ownership-id': '123',
      'x-report-id': '456',
      Authorization: `Bearer ${mockToken}`,
    });
  });
  it('should not add ownership and report ID headers if regex does not match', async () => {
    Object.defineProperty(window, 'location', {
      value: {pathname: '/some-other-path'},
    });
    userService.getToken.mockResolvedValue('mockToken');
    const instance = createAxiosInstance('https://api.example.com');
    const requestInterceptor =
      mockAxiosInstance.interceptors.request.use.mock.calls[0][0];
    const config = {headers: {}};
    await requestInterceptor(config);
    expect(config.headers).toEqual({
      Authorization: 'Bearer mockToken',
    });
    expect(config.headers).not.toHaveProperty('x-ownership-id');
    expect(config.headers).not.toHaveProperty('x-report-id');
  });
  it('should create an Axios instance with ownership and report ID headers disabled', () => {
    const instance = createAxiosInstance('https://api.example.com', {
      addOwnershipAndReportId: false,
    });
    expect(axios.create).toHaveBeenCalledWith({
      baseURL: 'https://api.example.com',
      signal: expect.any(AbortSignal),
    });
    const requestInterceptor =
      mockAxiosInstance.interceptors.request.use.mock.calls[0][0];
    const config = {headers: {}};
    requestInterceptor(config);
    expect(config.headers).not.toHaveProperty('x-ownership-id');
    expect(config.headers).not.toHaveProperty('x-report-id');
  });
});
