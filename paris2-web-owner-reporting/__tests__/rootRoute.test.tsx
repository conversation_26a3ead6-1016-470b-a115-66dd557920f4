import React from 'react';
import {render, screen, waitFor} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import AppRoutes from '../src/routes/rootRoutes';
import {useDataStoreContext} from '../src/context';
import routesConfig from '../src/routes/route.config';
// Mock necessary dependencies
jest.mock('../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));
jest.mock('../src/routes/route.config', () => jest.fn());
const mockUseDataStoreContext = useDataStoreContext as jest.Mock;
const mockRoutesConfig = routesConfig as jest.Mock;
// Sample component mocks
const MockComponent = () => <div>Mock Component</div>;
const MockChildComponent = () => <div>Mock Child Component</div>;
describe('AppRoutes Component', () => {
  beforeEach(() => {
    // Reset mock implementations before each test
    mockUseDataStoreContext.mockReturnValue({
      roleConfig: {},
    });
    // Provide a sample route configuration
    mockRoutesConfig.mockReturnValue([
      {
        path: '/test',
        component: MockComponent,
        childRoutes: [
          {
            path: 'child',
            component: MockChildComponent,
          },
        ],
      },
    ]);
  });
  it('should render routes based on the route config', async () => {
    render(
      <MemoryRouter initialEntries={['/test']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Check that the mock component is rendered based on the route
    await waitFor(() => {
      expect(screen.getByText('Mock Component')).toBeInTheDocument();
    });
  });
  it('should handle child routes correctly', async () => {
    render(
      <MemoryRouter initialEntries={['/test/child']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Check that the component is rendered based on the child route
    await waitFor(() => {
      expect(screen.getByText('Mock Component')).toBeInTheDocument();
    });
  });
  it('should handle redirects correctly', async () => {
    // Set up a route with a redirect
    mockRoutesConfig.mockReturnValueOnce([
      {
        path: '/redirect',
        redirect: '/test',
      },
    ]);
    render(
      <MemoryRouter initialEntries={['/redirect']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Wait for the redirect to take effect and mock component to be rendered
    await waitFor(() => {
      expect(screen.getByText('Not Found!')).toBeInTheDocument();
    });
  });
  it('should handle "Not Found" route for unmatched paths', async () => {
    render(
      <MemoryRouter initialEntries={['/unknown-path']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Check for "Not Found!" message
    await waitFor(() => {
      expect(screen.getByText('Not Found!')).toBeInTheDocument();
    });
  });
  it('should handle permission-denied routes', async () => {
    // Set up a route with isPermission set to false
    mockRoutesConfig.mockReturnValueOnce([
      {
        path: '/restricted',
        component: MockComponent,
        isPermission: false,
      },
    ]);
    render(
      <MemoryRouter initialEntries={['/restricted']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Check if user is redirected to /vessel when permission is denied
    await waitFor(() => {
      expect(screen.queryByText('Mock Component')).not.toBeInTheDocument();
      expect(screen.getByText('Not Found!')).toBeInTheDocument(); // because /vessel is not defined in this mock
    });
  });
  it('should show loading fallback while Suspense is waiting', async () => {
    render(
      <MemoryRouter initialEntries={['/test']}>
        <AppRoutes />
      </MemoryRouter>,
    );
    // Check if fallback text " Mock Component" is rendered during Suspense
    expect(screen.getByText('Mock Component')).toBeInTheDocument();
  });
});
