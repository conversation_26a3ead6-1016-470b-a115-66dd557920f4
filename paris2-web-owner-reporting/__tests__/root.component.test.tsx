import React from 'react';
import {render, screen, waitFor} from '@testing-library/react';
import Root from '../src/root.component';
import userService from '../src/services/user.service';
import UserRoleController from '../src/controller/user-role-controller';

jest.mock('../src/services/user.service', () => ({
  init: jest.fn(),
}));

jest.mock('../src/controller/user-role-controller', () => {
  return jest.fn().mockImplementation(() => ({
    getConfig: jest.fn(),
  }));
});

const mockUserService = userService as jest.Mocked<typeof userService>;

describe('Root Component', () => {
  const kcMock = {};
  const ga4reactMock = {
    event: jest.fn(),
  };

  beforeEach(() => {
    // Mock userService.init
    mockUserService.init.mockResolvedValue(undefined);

    // Mock UserRoleController.prototype.getConfig
    (UserRoleController as jest.Mock).mockImplementation(() => ({
      getConfig: jest.fn().mockReturnValue({
        roles: ['admin'],
      }),
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading spinner while initializing user service', async () => {
    render(<Root kc={kcMock} ga4react={ga4reactMock} />);

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    await waitFor(() => expect(mockUserService.init).toHaveBeenCalled());
  });
});
