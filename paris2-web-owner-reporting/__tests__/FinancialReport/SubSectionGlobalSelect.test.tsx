import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import _ from 'lodash';
import SubSectionGlobalSelect from '../../src/components/FinancialReport/SubSectionGlobalSelect';
import {useDataStoreContext, useRowSelectContext} from '../../src/context';
import {ReportType} from '../../src/enums';
import {ONGOING_REOPENED_UNDER_REVIEW_STATUSES} from '../../src/constants/fiancialReport';
import {Format} from '../../src/types';

// Mocking context hooks
jest.mock('../../src/context', () => ({
  useDataStoreContext: jest.fn(),
  useRowSelectContext: jest.fn(),
}));

describe('SubSectionGlobalSelect Component', () => {
  const mockSetRowSelected = jest.fn();
  const mockUseRowSelectContext = {
    rowSelected: [],
    setRowSelected: mockSetRowSelected,
  };

  const mockUseDataStoreContext = {
    roleConfig: {
      ownerReporting: {canUploadDoc: true},
      isOwner: false,
    },
    dataStore: {
      reportDetails: {
        isViewOnly: false,
        status: ONGOING_REOPENED_UNDER_REVIEW_STATUSES[0],
      },
      filters: [],
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRowSelectContext as jest.Mock).mockReturnValue(mockUseRowSelectContext);
    (useDataStoreContext as jest.Mock).mockReturnValue(mockUseDataStoreContext);
  });

  const rows = [
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2025-03-24T00:00:00.000Z',
      modifiedOn: '2025-03-24T14:40:01.874Z',
      createdBy: '000',
      modifiedBy: null,
      id: 7642152,
      serial: 42,
      fmlId: '1000Dec-test1',
      accountCode: 'test1',
      accountDescription: 'test1',
      section: 'test1',
      amount: 76009,
      transactionDate: '2024-12-31',
      reportFormatVersion: 2,
      reportId: 123,
    },
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2025-03-24T00:00:00.000Z',
      modifiedOn: '2025-03-24T14:40:01.874Z',
      createdBy: '000',
      modifiedBy: null,
      id: 7642153,
      serial: 44,
      fmlId: '1000Dec-test2',
      accountCode: 'test2',
      accountDescription: null,
      section: 'test2',
      amount: 8043.76,
      transactionDate: '2024-12-31',
      reportFormatVersion: 2,
      reportId: 123,
    },
    {
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '2025-03-24T00:00:00.000Z',
      modifiedOn: '2025-03-24T14:40:01.874Z',
      createdBy: '000',
      modifiedBy: null,
      id: 7642154,
      serial: 45,
      fmlId: '1000Dec-test3',
      accountCode: 'test3',
      accountDescription: null,
      section: 'test3',
      amount: 192865.75,
      transactionDate: '2024-12-31',
      reportFormatVersion: 2,
      reportId: 123,
    },
  ];

  it('renders the component with title', () => {
    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders the checkbox when conditions are met', () => {
    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();
  });

  it('does not render the checkbox when conditions are not met', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      ...mockUseDataStoreContext,
      roleConfig: {ownerReporting: {canUploadDoc: false}, isOwner: true},
    });

    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    expect(screen.queryByTestId('selectAllCheckbox')).not.toBeInTheDocument();
  });

  it('handles checkbox selection correctly', () => {
    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    fireEvent.click(checkbox);

    expect(mockSetRowSelected).toHaveBeenCalled();
  });

  it('handles checkbox deselection correctly', () => {
    (useRowSelectContext as jest.Mock).mockReturnValue({
      rowSelected: [
        {id: 7642153, fmlId: '1000Dec-test2', format: Format.BS},
        {id: 2, fmlId: 'fml2', format: Format.BS},
      ],
      setRowSelected: mockSetRowSelected,
    });

    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    fireEvent.click(checkbox);

    expect(mockSetRowSelected).toHaveBeenCalled();
  });

  it('displays the correct checkbox state based on rowSelected', () => {
    (useRowSelectContext as jest.Mock).mockReturnValue({
      rowSelected: [{id: 1, fmlId: 'fml1', format: Format.BS}],
      setRowSelected: mockSetRowSelected,
    });

    render(
      <SubSectionGlobalSelect
        rows={rows}
        reportType={ReportType.BS}
        title="Test Title"
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    expect(checkbox).not.toBeChecked();
  });
});
