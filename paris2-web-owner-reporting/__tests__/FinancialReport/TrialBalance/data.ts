export const mockTrialBalanceData = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2025-03-06T00:00:00.000Z',
    modifiedOn: '2025-03-06T06:59:43.115Z',
    createdBy: '000',
    modifiedBy: null,
    id: 1,
    accountCode: 'CBASBK001',
    serial: 1,
    accountName: 'test',
    fmlId: '1001-test',
    openingDebit: 822282.**********,
    openingCredit: null,
    periodDebit: 171497.85,
    periodCredit: 260861.29,
    closingDebit: 732919.13,
    closingCredit: null,
    transactionDate: '2023-07-31',
    asAt: '2023-07-31T00:00:00.000Z',
    currency: 'USD',
    ledgerCurrency: 'USD',
    vesselCode: '1001',
    vesselName: 'test vesselName',
    vesselOwner: 'vesselOwner',
    reportId: 120209,
    openingBalance: 822282.**********,
    duringThePeriod: -89363.44,
    closingBalance: 732919.13,
    remarks: null,
    comments: {
      resolved: 1,
      total: 31,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2025-03-06T00:00:00.000Z',
    modifiedOn: '2025-03-06T06:59:43.115Z',
    createdBy: '000',
    modifiedBy: null,
    id: 2,
    accountCode: 'CBASBK001',
    serial: 2,
    accountName: 'Vessel accountName',
    fmlId: '1001-test',
    openingDebit: 822282.**********,
    openingCredit: null,
    periodDebit: 171497.85,
    periodCredit: 260861.29,
    closingDebit: 732919.13,
    closingCredit: null,
    transactionDate: '2023-07-31',
    asAt: '2023-07-31T00:00:00.000Z',
    currency: 'USD',
    ledgerCurrency: 'USD',
    vesselCode: '1001',
    vesselName: 'test test',
    vesselOwner: 'test',
    reportId: 120209,
    openingBalance: 822282.**********,
    duringThePeriod: -89363.44,
    closingBalance: 732919.13,
    remarks: null,
    comments: {
      resolved: 1,
      total: 31,
    },
  },
];
