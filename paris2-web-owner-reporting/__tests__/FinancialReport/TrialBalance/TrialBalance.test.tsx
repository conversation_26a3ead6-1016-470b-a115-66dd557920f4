import {fireEvent, render, screen} from '@testing-library/react';
import axios from 'axios';
import React from 'react';
import {act} from 'react-dom/test-utils';
import {MemoryRouter} from 'react-router-dom';
import {TrialBalance} from '../../../src/components';
import {
  DataStoreContext,
  TableActionContextProvider,
} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {IReportDetails} from '../../../src/types';
import {
  mockReportDetailList,
  reportDetailsOwnerWithStatusOngoing,
  reportDetailsWithStatusFrozen,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {axiosGetMockFunc, setupAxios} from '../../utils/setupAxios';
import {mockTrialBalanceData} from './data';
import {ReportType} from '../../../src/enums';
import CommentContextProvider from '../../../src/context/CommentContextProvider';

interface IArgs {
  reportDetails?: IReportDetails;
}

const wrappedComponent2 = (args?: IArgs) => {
  const {reportDetails = reportDetailsWithStatusOngoing} = args ?? {};

  return (
    <MemoryRouter>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: {
              ...reportDetails,
              reportFormatVersion: 1,
            },
            filters: '',
            reportDataCache: {BS: ['test']},
            ownerPreferences: null,
            reportVersion: 1,
          },
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
            isOwner: false,
          },
        }}
      >
        <AlertContextProvider>
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockTrialBalanceData}
                reportType={ReportType.TB_V2}
              >
                <TrialBalance />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </AlertContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  );
};
const wrappedComponent = (args?: IArgs) => {
  const {reportDetails = reportDetailsWithStatusOngoing} = args ?? {};

  return (
    <MemoryRouter>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: {
              ...reportDetails,
            },
            filters: '',
            reportDataCache: null,
            ownerPreferences: null,
            reportVersion: 2,
          },
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
            isOwner: false,
          },
        }}
      >
        <AlertContextProvider>
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockTrialBalanceData}
                reportType={ReportType.TB}
              >
                <TrialBalance />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </AlertContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  );
};

describe('TrialBalance', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  it('Testing url mapping', async () => {
    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/trial-balance/'))
        return {
          data: mockTrialBalanceData,
        };

      return axiosGetMockFunc(url);
    });
    await act(async () => {
      render(wrappedComponent());
    });
  });
  it('Render Component for new format', async () => {
    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/trial-balance/'))
        return {
          data: mockTrialBalanceData,
        };

      return axiosGetMockFunc(url);
    });
    await act(async () => {
      render(wrappedComponent2());
    });
  });

  test('testing date selection mutate', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const [_, secondInputElement] = screen.getAllByRole('textbox');

    const selectedEndDate = new Date(reportDetailsWithStatusOngoing.endDate);

    const selectedDay = selectedEndDate.getDate() - 1;

    await act(async () => {
      await fireEvent.focus(secondInputElement);
      await fireEvent.click(screen.getByText(selectedDay));
    });
  });

  it('Testing Other Documents button', async () => {
    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/trial-balance/'))
        return {
          data: mockTrialBalanceData,
        };

      return axiosGetMockFunc(url);
    });

    await act(async () => {
      render(wrappedComponent());
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(1);

    await act(async () => {
      fireEvent.click(screen.getByText('Other Documents'));
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(2);
    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListCloseBtn'));
    });
  });

  //this tests has to be run at the end because their mutating the default mock implementation of axios
  //testing useFetchDataWithAlertOnFailure
  test('Testing useFetchDataWithAlertOnFailure error case', async () => {
    (axios.get as jest.Mock).mockReturnValue(Promise.reject({response: {}}));

    await act(async () => {
      render(wrappedComponent());
    });
  });
});

describe('use Report Status', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  it('renders without error', async () => {
    await act(async () => {
      render(wrappedComponent({reportDetails: reportDetailsWithStatusFrozen}));
    });

    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('trial-balance'))
        return {
          data: mockTrialBalanceData,
        };
      return axiosGetMockFunc(url);
    });
  });
});
