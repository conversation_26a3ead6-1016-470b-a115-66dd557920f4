import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import axios from 'axios';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {MentionCard} from '../../src/components/FinancialReport/TransactionDetails/Comment/MentionCard';
import {UNAUTHORIZED_USER_REPORT_DETAIL_MESSAGE} from '../../src/constants';
import {
  NO_COMMENTS_YET,
  UNAUTHORIZED_STATUS_CODE,
} from '../../src/constants/keys.constants';
import {DataStoreContext, TableActionContextProvider} from '../../src/context';
import AlertContextProvider from '../../src/context/AlertContextProvider';
import RowSelectContextProvider from '../../src/context/RowSelectProvider';
import {ChannelType} from '../../src/enums';
import {ReportCount, ReportType} from '../../src/enums/reportType.enum';
import {FinancialReport} from '../../src/pages/FinancialReport/FinancialReport.page';
import {IOwnerPreferences, IReportDetails} from '../../src/types';
import {waitAsync} from '../../src/utils/helper';
import {
  mockDisabledReportRowsWithCounts,
  mockDocumentsCountList,
  mockDocumentsList,
  mockEnabledReportRowsWithCounts,
  mockReportDetailList,
  mockViewCommentList,
  reportDetailsOwnerWithStatusOngoing,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
} from '../data';
import {ownerReportingAllTrue} from '../data/owner-reporting-role';
import {
  axiosGetMockFunc,
  axiosPostMockFunc,
  setupAxios,
} from '../utils/setupAxios';
import {
  mockBalanceSheetData,
  mockOwnerPrefrenceDataBS,
  mockOwnerPrefrenceDataOC,
} from './BalanceSheet/data';
import {mockBudgetVarianceReportData} from './BudgetVarianceReport/data';
import {mockCashFlowData} from './CashFlowStatement/data';
import CommentContextProvider from '../../src/context/CommentContextProvider';
import {mockOperatingCostData} from './OperatingCost/data';

const wrappedComponent = (
  reportDetails: IReportDetails | null = reportDetailsWithStatusOngoing,
  reportRowsWithCounts: ReportCount | null = mockEnabledReportRowsWithCounts,
) => (
  <MemoryRouter
    initialEntries={[
      '/owner-reporting/783/reports/3194/?activeTab=1&openViewComments=true',
    ]}
  >
    <AlertContextProvider>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails,
            reportDetailsList: mockReportDetailList,
            ownerPreferences: null,
            reportRowsWithCounts: reportRowsWithCounts,
          },
          setDataStore: jest.fn(),
          ga4EventTrigger: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
          },
        }}
      >
        <TableActionContextProvider>
          <RowSelectContextProvider>
            <CommentContextProvider
              reportData={mockOperatingCostData}
              reportType={ReportType.OC}
            >
              <FinancialReport />
            </CommentContextProvider>
          </RowSelectContextProvider>
        </TableActionContextProvider>
      </DataStoreContext.Provider>
    </AlertContextProvider>
  </MemoryRouter>
);

const exportReportComponent = (
  reportDetails: IReportDetails = reportDetailsWithStatusOngoing,
  reportRowsWithCounts: ReportCount = mockEnabledReportRowsWithCounts,
  owner = true,
  ownerPreferences: IOwnerPreferences[] = mockOwnerPrefrenceDataBS,
) => (
  <MemoryRouter
    initialEntries={[
      '/owner-reporting/783/reports/3194/?activeTab=1&openViewComments=true',
    ]}
  >
    <AlertContextProvider>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails,
            reportDetailsList: mockReportDetailList,
            ownerPreferences: ownerPreferences,
            reportRowsWithCounts: reportRowsWithCounts,
          },
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
            isOwner: owner,
          },
        }}
      >
        <TableActionContextProvider>
          <RowSelectContextProvider>
            <FinancialReport />
          </RowSelectContextProvider>
        </TableActionContextProvider>
      </DataStoreContext.Provider>
    </AlertContextProvider>
  </MemoryRouter>
);

const signedUrl = 'http://123.com?key=12412-124-124ef';

const axiosMockImplementation = async (url: string) => {
  if (url.includes('/documents/upload-with-meta/generate-url?count='))
    return {
      data: {
        signedUrls: [{url: signedUrl}],
        uuid: '23432-23ag-23vds',
      },
    };

  return await axiosGetMockFunc(url);
};

const axiosPostMockImplementation = async (url: string) => {
  if (url.includes('/documents/upload/save/')) return {data: []};

  return await axiosPostMockFunc(url);
};

const openUploadDocModal = async () => {
  const expandCollapseAll = screen.getByText('Expand All');

  await act(async () => {
    fireEvent.click(expandCollapseAll);
  });

  const allCheckboxes = screen.getAllByTestId('cashFlowStatementCheckbox');

  await act(async () => {
    fireEvent.click(allCheckboxes[0]);
  });

  await act(async () => {
    fireEvent.click(screen.getByText('Upload Document'));
  });
};

const waitTillTheComponentRender = async () => {
  const node = await waitFor(() => {
    const possibleTexts = ['Table Columns', 'Expand All', 'Approved Budgets'];

    const node = possibleTexts.reduce<Element | null>((acc, text) => {
      if (acc) return acc;

      return screen.queryByText(text);
    }, null);

    if (!node) throw Error();
    return node;
  });

  expect(node).toBeInTheDocument();
};

describe('Financial Report Page', () => {
  beforeEach(async () => {
    await setupAxios();

    (axios.get as jest.Mock).mockClear();
    (axios.post as jest.Mock).mockClear();
    (axios.get as jest.Mock).mockImplementation(axiosMockImplementation);
    (axios.post as jest.Mock).mockImplementation(axiosPostMockImplementation);
    await waitAsync(110);
  });

  test('Rendering the component', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    await waitTillTheComponentRender();
  });

  test('Testing switching between tabs', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockCashFlowData}),
    );
    await act(async () => {
      render(wrappedComponent());
      await waitTillTheComponentRender();
    });

    const cashFlowStatement = screen.getByText('Cash Flow Statement');
    const balanceSheet = screen.getByText('Balance Sheet');

    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockBalanceSheetData}),
    );

    await act(async () => {
      fireEvent.click(balanceSheet);
    });
    await waitTillTheComponentRender();

    expect(screen.getByText('Assets')).toBeInTheDocument();

    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockCashFlowData}),
    );

    await act(async () => {
      fireEvent.click(cashFlowStatement);
    });

    await waitFor(() => {
      expect(screen.getByText('From')).toBeInTheDocument();
    });
  });

  test('Testing cash flow statement  report tab', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await waitTillTheComponentRender();

    screen.getByText('Cash Flow Statement');

    expect(screen.getByText('From')).toBeInTheDocument();
  });

  test('UploadDocumentButton: Testing Upload Document button', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    await waitTillTheComponentRender();
    await openUploadDocModal();

    expect(screen.getAllByText('Upload Document')).toHaveLength(2);
  });

  test('UploadDocumentButton: Testing File Upload Modal', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    await waitTillTheComponentRender();
    Object.defineProperty(HTMLElement.prototype, 'onTransitionEnd', {
      get() {
        return jest.fn();
      },
      configurable: true,
    });

    await act(async () => {
      await openUploadDocModal();
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Browse Files'));
    });

    const pdfSignature = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
    const pdfSignatureBlob = new Blob([pdfSignature], {
      type: 'application/pdf',
    });

    const file = new File([pdfSignatureBlob, 'sample content'], 'sample.pdf', {
      type: 'application/pdf',
    });

    await act(async () => {
      fireEvent.change(screen.getByLabelText('Browse Files'), {
        target: {files: [file]},
      });
    });

    await new Promise(resolve => setTimeout(resolve, 5));

    await act(async () => {
      fireEvent.click(screen.getByText('Save'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('attachmentDrawerCloseBtn'));
    });

    expect(screen.getAllByText('Upload Document')).toHaveLength(1);
  });

  test('UploadDocumentButton: Testing File Upload Modal Close Button', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    await waitTillTheComponentRender();
    await openUploadDocModal();

    await act(async () => {
      fireEvent.click(screen.getByText('Cancel'));
    });
  });

  test('Testing balance sheet report tab', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    await waitTillTheComponentRender();

    const balanceSheet = screen.getByText('Balance Sheet');

    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockBalanceSheetData}),
    );

    await act(async () => {
      fireEvent.click(balanceSheet);
    });

    await waitTillTheComponentRender();

    expect(screen.getByText('Assets')).toBeInTheDocument();
  });

  test('Testing the loading state', async () => {
    await act(async () => {
      render(wrappedComponent(null));
    });

    expect(screen.getByTestId('defaultLoader')).toBeInTheDocument();
  });

  //testing view comments

  test('Testing View Comments rendering', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await waitTillTheComponentRender();

    const viewCommentNode = screen.getByText('View Comments');

    await act(async () => {
      fireEvent.click(viewCommentNode);
    });

    expect(screen.queryByText('View Comments')).toBeInTheDocument();
  });

  test('Testing View Comments filtering', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await waitTillTheComponentRender();

    const viewCommentNode = screen.getByText('View Comments');

    await act(async () => {
      fireEvent.click(viewCommentNode);
    });

    const commentFilterNode = screen.getByText('all');
    const internalComments = mockViewCommentList.filter(
      commentNode => commentNode.channelType === ChannelType.INTERNAL,
    );
    const ownerComments = mockViewCommentList.filter(
      commentNode => commentNode.channelType === ChannelType.OWNER,
    );

    expect(commentFilterNode).toBeInTheDocument();

    expect(screen.queryAllByTestId('commentCard')).toHaveLength(
      internalComments.length + ownerComments.length,
    );

    //clicking on filter option
    await act(async () => {
      fireEvent.click(commentFilterNode);
    });

    jest.spyOn(axios, 'get').mockImplementationOnce(async (url: string) => {
      if (url.includes('/comments')) return {data: internalComments};

      return axiosMockImplementation(url);
    });

    //selecting filter option
    await act(async () => {
      fireEvent.click(screen.getByText('Internal'));
    });

    expect(screen.queryAllByTestId('commentCard-internal')).toHaveLength(
      internalComments.length,
    );

    expect(screen.queryAllByTestId('commentCard-owner')).toHaveLength(0);

    //clicking on filter option
    await act(async () => {
      fireEvent.click(commentFilterNode);
    });

    jest.spyOn(axios, 'get').mockImplementationOnce(async (url: string) => {
      if (url.includes('/comments')) return {data: ownerComments};

      return axiosMockImplementation(url);
    });

    //selecting filter option
    await act(async () => {
      fireEvent.click(screen.getByText('Owner'));
    });

    expect(screen.queryAllByTestId('commentCard-internal')).toHaveLength(0);

    expect(screen.queryAllByTestId('commentCard-owner')).toHaveLength(
      ownerComments.length,
    );

    //clicking on filter option
    await act(async () => {
      fireEvent.click(commentFilterNode);
    });

    //selecting filter option
    await act(async () => {
      fireEvent.click(screen.getByText('All'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('viewCommentClose'));
    });
  });

  test('Testing View Comments Card Click', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await waitTillTheComponentRender();

    const viewCommentNode = screen.getByText('View Comments');

    await act(async () => {
      fireEvent.click(viewCommentNode);
    });

    Element.prototype.scrollIntoView = jest.fn();

    await act(async () => {
      fireEvent.click(screen.queryAllByTestId('commentCard')[0]);
    });
  });

  // This test must be placed at the end due to overriding mock implementations
  // so that the correct behavior is tested after all mocks have been set up.

  test('Testing budget variance report tab', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await waitTillTheComponentRender();

    const budgetVarianceReport = screen.getByText('Budget Variance Report');

    jest.spyOn(axios, 'get').mockImplementation(async (url: string) => {
      if (url.includes('documents/count'))
        return {data: mockDocumentsCountList};
      if (url.includes('documents')) return {data: mockDocumentsList};

      return await axiosMockImplementation(url);
    });
    await act(async () => {
      fireEvent.click(budgetVarianceReport);
    });

    await waitTillTheComponentRender();

    expect(
      screen.getByText(mockBudgetVarianceReportData[0].section),
    ).toBeInTheDocument();
  });

  test('Testing User as Owner', async () => {
    jest.spyOn(axios, 'get').mockImplementation(async (url: string) => {
      if (url.includes('documents/count'))
        return {data: mockDocumentsCountList};
      if (url.includes('documents')) return {data: mockDocumentsList};

      return await axiosMockImplementation(url);
    });

    await act(async () => {
      render(wrappedComponent(reportDetailsOwnerWithStatusOngoing));
    });

    await waitTillTheComponentRender();
  });

  test('Testing View Comments rendering for owner', async () => {
    const ownerComments = mockViewCommentList.filter(
      commentNode => commentNode.channelType === ChannelType.OWNER,
    );
    jest.spyOn(axios, 'get').mockImplementation(async (url: string) => {
      if (url.includes('/comments')) return {data: ownerComments};

      return axiosMockImplementation(url);
    });

    await act(async () => {
      render(wrappedComponent(reportDetailsOwnerWithStatusOngoing));
    });

    await waitTillTheComponentRender();

    const viewCommentNode = screen.getByText('View Comments');

    await act(async () => {
      fireEvent.click(viewCommentNode);
    });

    expect(screen.queryAllByTestId('commentCard')).toHaveLength(
      ownerComments.length,
    );
  });

  test('Testing View Comments EmptyState rendering', async () => {
    jest.spyOn(axios, 'get').mockImplementation(async (url: string) => {
      if (url.includes('/comments')) return {data: []};

      return axiosMockImplementation(url);
    });

    await act(async () => {
      render(wrappedComponent(reportDetailsOwnerWithStatusOngoing));
    });

    await waitTillTheComponentRender();

    const viewCommentNode = screen.getByText('View Comments');

    await act(async () => {
      fireEvent.click(viewCommentNode);
    });

    expect(screen.queryByText(NO_COMMENTS_YET)).toBeInTheDocument();
  });

  test('Testing Report Details with an error', async () => {
    jest.spyOn(axios, 'get').mockImplementation(async (url: string) => {
      if (/^\/reports\/\d+$/.test(url))
        return {
          data: null,
          error: {
            data: {
              error: {
                statusCode: UNAUTHORIZED_STATUS_CODE,
                message: UNAUTHORIZED_USER_REPORT_DETAIL_MESSAGE,
              },
            },
          },
        };

      return axiosMockImplementation(url);
    });

    await act(async () => {
      render(wrappedComponent(reportDetailsOwnerWithStatusOngoing));
    });

    await waitTillTheComponentRender();
  });

  test('Testing Export Report Button exists', async () => {
    await act(async () => {
      render(wrappedComponent(reportDetailsOwnerWithStatusOngoing));
    });

    expect(screen.getByTestId('exportReportBtn')).toBeInTheDocument();
  });

  test('Testing Export Report Button is disabled and show tooltip', async () => {
    await act(async () => {
      render(
        wrappedComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockDisabledReportRowsWithCounts,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');

    expect(exportButton).toBeDisabled();

    fireEvent.mouseOver(exportButton);

    const tooltipContent = exportButton.getAttribute('title');
    expect(tooltipContent).toBe(
      'Export is disabled since there is no data within the report.',
    );
  });

  test('if CFS-FLEET option is disabled ', async () => {
    await act(async () => {
      render(
        wrappedComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });

    const CFSFleetBtnText = screen.getByTestId('CFS-FLEET');
    expect(CFSFleetBtnText).toBeInTheDocument();
    expect(CFSFleetBtnText).toBeDisabled();

    const CFSFleetLabel = screen.getByTestId(
      'CFS-FLEET-report-label',
    ).parentElement;

    if (CFSFleetLabel) {
      fireEvent.mouseOver(CFSFleetLabel);

      const tooltipContent = CFSFleetLabel.getAttribute('title');
      expect(tooltipContent).toBe('No data available for export');
    }
  });

  test('if & CFS-ACCRUAL option is enabled ', async () => {
    await act(async () => {
      render(
        wrappedComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });

    const CFSAccuralBtnText = screen.getByTestId('CFS-FLEET');

    expect(CFSAccuralBtnText).toBeInTheDocument();
    expect(CFSAccuralBtnText).not.toBeEnabled();
  });

  test('Download Financial Report ', async () => {
    await act(async () => {
      render(
        wrappedComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
        ),
      );
    });

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: {
          url: 'http://localhost',
          message: 'Report generated',
        },
        status: 200,
      });
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });

    const cashFlowStatement = screen.getByTestId('CFS-FLEET');
    expect(cashFlowStatement).not.toBeChecked();

    fireEvent.click(cashFlowStatement);
    expect(cashFlowStatement).toBeChecked();

    fireEvent.click(screen.getByTestId('download-financial-report'));

    const selectedTypeBtn = screen.getAllByTestId('selected-type-btn')[0];
    fireEvent.click(selectedTypeBtn);
    fireEvent.keyDown(selectedTypeBtn, {
      key: 'Enter',
    });
  });

  test('Download Financial Report gets failed', async () => {
    await act(async () => {
      render(
        wrappedComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
        ),
      );
    });

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.reject({});
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });

    const cashFlowStatement = screen.getByTestId('CFS-FLEET');
    expect(cashFlowStatement).not.toBeChecked();

    fireEvent.click(cashFlowStatement);
    expect(cashFlowStatement).toBeChecked();

    fireEvent.click(screen.getByTestId('download-financial-report'));
  });
});

describe('Mention Card', () => {
  const userData = {name: 'Zack', designation: 'Developer'};
  const wrappedComponent = () => <MentionCard userData={userData} />;

  test('if Mention Card Exists', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    expect(screen.getByTestId('mention-card-test')).toBeInTheDocument();
  });

  test('name and designation of user renders properly', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    expect(screen.getByTestId('user-name')).toHaveTextContent('Zack');
    expect(screen.getByTestId('user-designation')).toHaveTextContent(
      'Developer',
    );
  });
});

describe('Export Report', () => {
  test('Export for All is disabled with User', async () => {
    await act(async () => {
      render(
        exportReportComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
          false,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });
    const accountingReport = screen.getByTestId('Accounting-Report');
    expect(accountingReport).not.toBeChecked();

    fireEvent.click(accountingReport);

    waitFor(() => {
      const allReport = screen.getByTestId('All');
      expect(allReport).toBeInTheDocument();
    });
  });

  test('Export type is disabled with Owner when BS is false', async () => {
    await act(async () => {
      render(
        exportReportComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
          true,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });
    const accountingReport = screen.getByTestId('Accounting-Report');
    expect(accountingReport).not.toBeChecked();

    fireEvent.click(accountingReport);

    waitFor(() => {
      const allReport = screen.getByTestId('All');
      expect(allReport).toBeInTheDocument();

      const BSBtnText = screen.getByTestId('BS');
      expect(BSBtnText).toBeInTheDocument();
      expect(BSBtnText).toBeDisabled();
    });

    const click = screen.getByTestId('financialReportScrollTopBtn');
    fireEvent.click(click);
  });

  test('Export type is disabled with Owner when OC is false', async () => {
    await act(async () => {
      render(
        exportReportComponent(
          reportDetailsOwnerWithStatusOngoing,
          mockEnabledReportRowsWithCounts,
          true,
          mockOwnerPrefrenceDataOC,
        ),
      );
    });

    const exportButton = screen.getByTestId('exportReportBtn');
    // open export report modal
    await act(async () => {
      fireEvent.click(exportButton);
    });
    const accountingReport = screen.getByTestId('Accounting-Report');
    expect(accountingReport).not.toBeChecked();

    fireEvent.click(accountingReport);

    waitFor(() => {
      const allReport = screen.getByTestId('All');
      expect(allReport).toBeInTheDocument();

      const OCBtnText = screen.getByTestId('OC');
      expect(OCBtnText).toBeInTheDocument();
      expect(OCBtnText).toBeDisabled();
    });
  });
});
