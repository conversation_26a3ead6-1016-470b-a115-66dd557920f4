import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import GlobalCommentView from '../../src/components/FinancialReport/TransactionDetails/Comment/GlobalCommentView';
import {
  useAlertContext,
  useCommentContext,
  useDataStoreContext,
  useRowSelectContext,
} from '../../src/context';
import {APIDelete, APIPusher} from '../../src/services/standard.service';
import {TabsCommonFieldsType, ICommentPayload} from '../../src/types';
import {ReportType} from '../../src/enums';
// import {EMPTY_STATE_MESSAGE} from '../../src/constants/keys.constants';

// Mock dependencies
jest.mock('../../src/context', () => ({
  useAlertContext: jest.fn(),
  useCommentContext: jest.fn(),
  useDataStoreContext: jest.fn(),
  useRowSelectContext: jest.fn(),
}));

jest.mock('../../src/services/standard.service', () => ({
  APIDelete: jest.fn(),
  APIPusher: jest.fn(),
}));

jest.mock(
  '../../src/components/FinancialReport/TransactionDetails/Comment/CommentList',
  () => {
    const MockedCommentList = () => <div>Mocked CommentList</div>;
    MockedCommentList.displayName = 'MockedCommentList';
    return MockedCommentList;
  },
);
jest.mock(
  '../../src/components/FinancialReport/TransactionDetails/Comment/AddComment',
  () => {
    const MockedAddComment = ({onMessageSubmit}: any) => (
      <button
        onClick={() =>
          onMessageSubmit({
            message: 'Test comment',
            channelType: 'test',
            users: [],
          })
        }
      >
        Mocked AddComment
      </button>
    );
    MockedAddComment.displayName = 'MockedAddComment';
    return MockedAddComment;
  },
);

const mockCommentResult = {
  reportId: 120209,
  reportType: 'TB',
  fmlId: '1001-CBASBK001||1001-VBASCM001',
  message: 'hi',
  channelType: 'INTERNAL',
  users: [],
  path: '0',
};

describe('GlobalCommentView', () => {
  const mockAlert = jest.fn();
  const mockSetCountByMultiFmlId = jest.fn();
  const mockGa4EventTrigger = jest.fn();

  const mockSelectedRow: TabsCommonFieldsType = {fmlId: '123'};
  const mockReportType: ReportType = ReportType.TB;

  beforeEach(() => {
    (useAlertContext as jest.Mock).mockReturnValue({alert: mockAlert});
    (useCommentContext as jest.Mock).mockReturnValue({
      setCountByMultiFmlId: mockSetCountByMultiFmlId,
    });
    (useDataStoreContext as jest.Mock).mockReturnValue({
      dataStore: {
        reportDetails: {
          id: 1,
          vesselId: 'vessel-1',
          status: 'DRAFT',
          isViewOnly: false,
        },
      },
      ga4EventTrigger: mockGa4EventTrigger,
    });
    (useRowSelectContext as jest.Mock).mockReturnValue({rowSelected: []});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders empty state when there are no comments', () => {
    render(
      <GlobalCommentView
        selectedRow={mockSelectedRow}
        reportType={mockReportType}
      />,
    );
  });

  it('calls onAddComment when a new comment is added', async () => {
    (APIPusher as jest.Mock).mockResolvedValue([{id: 1}]);

    render(
      <GlobalCommentView
        selectedRow={mockSelectedRow}
        reportType={mockReportType}
      />,
    );
    fireEvent.click(screen.getByText('Mocked AddComment'));

    await waitFor(() => {
      expect(APIPusher).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({message: 'Test comment'}),
        expect.any(Object),
      );
      expect(mockAlert).toHaveBeenCalledWith(
        'success',
        'Comment added to all selected rows',
      );
    });
  });

  it('handles errors when adding a comment fails', async () => {
    (APIPusher as jest.Mock).mockRejectedValue({
      data: {error: {message: 'Error adding comment'}},
    });

    render(
      <GlobalCommentView
        selectedRow={mockSelectedRow}
        reportType={mockReportType}
      />,
    );
    fireEvent.click(screen.getByText('Mocked AddComment'));

    await waitFor(() => {
      expect(mockAlert).toHaveBeenCalledWith('danger', 'Error adding comment');
    });
  });
});
