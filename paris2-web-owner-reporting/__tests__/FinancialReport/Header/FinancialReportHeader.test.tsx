import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';

import {FinancialReportHeader} from '../../../src/components/FinancialReport';
import {
  DataStoreContext,
  RowSelectContext,
  TableActionContextProvider,
} from '../../../src/context';

import axios from 'axios';
import {reportDetailsStatus} from '../../../src/constants';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import {IReportDetails, IReportHistoryDetails} from '../../../src/types';
import {
  mockDisabledReportRowsWithCounts,
  mockEnabledReportRowsWithCounts,
  mockReportHistoryBothApproved,
  mockReportHistoryReportSubmitted,
  reportDetailsWithStatusApproved,
  reportDetailsWithStatusOngoing,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {setupAxios} from '../../utils/setupAxios';
import {ReportCount} from '../../../src/enums/reportType.enum';

const mockSearchParams = new Map();
const mockSetSearchParams = jest.fn(callback => callback(mockSearchParams));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useSearchParams: () => [new Map(), mockSetSearchParams],
}));

const wrappedComponent = (
  reportDetails: IReportDetails | null,
  reportRowsWithCounts: ReportCount = mockEnabledReportRowsWithCounts,
  owner = true,
  reportHistoryDetails: IReportHistoryDetails[] = [],
) => (
  <MemoryRouter>
    <RowSelectContext.Provider
      value={{rowSelected: [], setRowSelected: jest.fn()}}
    >
      <AlertContextProvider>
        <DataStoreContext.Provider
          value={{
            dataStore: {
              reportDetails,
              ownerPreferences: null,
              reportRowsWithCounts: reportRowsWithCounts,
              reportHistoryDetails,
            },
            ga4EventTrigger: jest.fn(),
            roleConfig: {
              ownerReporting: ownerReportingAllTrue,
              isOwner: owner,
            } as any,
            setDataStore: jest.fn().mockImplementation(callbackOrValue => {
              if (typeof callbackOrValue === 'function')
                callbackOrValue({reportDetails, ownerPreferences: null});
            }),
          }}
        >
          <TableActionContextProvider>
            <FinancialReportHeader />
          </TableActionContextProvider>
        </DataStoreContext.Provider>
      </AlertContextProvider>
    </RowSelectContext.Provider>
  </MemoryRouter>
);

describe('FinancialReportHeader', () => {
  beforeEach(async () => {
    await setupAxios();
    window.open = jest.fn();
  });

  test('renders the component', () => {
    render(wrappedComponent(reportDetailsWithStatusOngoing));

    // Assert that the component renders without errors
    expect(screen.getByText('Vessel')).toBeInTheDocument();
  });

  test('testing the breadcrumb rendering data', () => {
    render(wrappedComponent(reportDetailsWithStatusOngoing));

    expect(screen.getByText('Vessel')).toBeInTheDocument();
    expect(
      screen.getByText(reportDetailsWithStatusOngoing.vesselName),
    ).toBeInTheDocument();
    expect(screen.getByText('Financial Reports')).toBeInTheDocument();
  });

  test('testing of rendering the title of financial report', () => {
    render(wrappedComponent(reportDetailsWithStatusOngoing));

    expect(
      screen.getByText(
        reportDetailsWithStatusOngoing.name + ' Financial Report',
      ),
    ).toBeInTheDocument();
  });

  test('testing the rendering of financial report status', () => {
    render(wrappedComponent(reportDetailsWithStatusOngoing));

    expect(
      screen.getByText(
        reportDetailsStatus[reportDetailsWithStatusOngoing.status].name,
      ),
    ).toBeInTheDocument();
  });

  test('testing the submit report modal', async () => {
    render(wrappedComponent(reportDetailsWithStatusApproved));

    await act(async () => {
      fireEvent.click(screen.getByText('More'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Submit to Owner'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Cancel'));
    });
  });

  test('testing the submit report successful submission', async () => {
    const mockAxiosGetCall = jest.fn(() => ({}));

    render(
      wrappedComponent(
        reportDetailsWithStatusApproved,
        mockEnabledReportRowsWithCounts,
        false,
        mockReportHistoryBothApproved,
      ),
    );

    (axios.post as jest.Mock).mockImplementationOnce(mockAxiosGetCall);

    await act(async () => {
      fireEvent.click(screen.getByText('More'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Submit to Owner'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Confirm'));
    });

    expect(mockAxiosGetCall).toHaveBeenCalledTimes(1);
  });

  test('testing the submit report submission error', async () => {
    const mockAxiosPostCall = jest.fn();

    render(wrappedComponent(reportDetailsWithStatusApproved));

    (axios.post as jest.Mock).mockImplementationOnce(mockAxiosPostCall);

    await act(async () => {
      fireEvent.click(screen.getByText('More'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Submit to Owner'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Confirm'));
    });

    expect(mockAxiosPostCall).toHaveBeenCalledTimes(1);
  });

  test('testing the submit report submission error with status 409', async () => {
    const mockAxiosPostCallWith409Status = jest.fn(async () => {
      const error = new Error('Message');
      (error as any).response = {
        status: 409,
        data: {error: {message: ''}},
      };

      throw error;
    });

    (axios.post as jest.Mock).mockImplementationOnce(
      mockAxiosPostCallWith409Status,
    );

    await act(async () => {
      render(wrappedComponent(reportDetailsWithStatusApproved));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('More'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Submit to Owner'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Confirm'));
    });

    expect(mockAxiosPostCallWith409Status).toHaveBeenCalledTimes(1);
  });

  test('Clicking on Export Reports Button', async () => {
    await act(async () => {
      render(wrappedComponent(reportDetailsWithStatusApproved));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Export Reports'));
    });

    expect(screen.getByText('Export Financial Reports')).toBeInTheDocument();
  });

  test('if More Button is disabled', async () => {
    render(
      wrappedComponent(
        reportDetailsWithStatusOngoing,
        mockDisabledReportRowsWithCounts,
      ),
    );

    const MoreButton = screen.getByTestId('more-button');
    expect(MoreButton).toBeInTheDocument();
    expect(MoreButton).toBeDisabled();

    const tooltipContent = MoreButton.getAttribute('title');
    expect(tooltipContent).toBe(
      'Report Transition to next step is disabled since there is no data within the report.',
    );
  });

  test('open Unaddress Notification Modal & Sent Email for UnAddress Comments', async () => {
    render(
      wrappedComponent(
        reportDetailsWithStatusOngoing,
        mockEnabledReportRowsWithCounts,
        false,
      ),
    );

    fireEvent.click(screen.getByTestId('more-button'));

    const reminderEmailBtn = screen.getByTestId('reminder-btn');

    expect(reminderEmailBtn).toBeInTheDocument();
    fireEvent.click(reminderEmailBtn);

    // Reminder Email Modal Opens
    expect(screen.getByTestId('unaddressed-comment-modal')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Confirm'));
  });

  test('open Unaddress Notification Modal & throe error for UnAddress Comments Email', async () => {
    jest.resetAllMocks();

    render(
      wrappedComponent(
        reportDetailsWithStatusOngoing,
        mockEnabledReportRowsWithCounts,
        false,
      ),
    );

    (axios.post as jest.Mock).mockImplementationOnce((url: string) => {
      const err: any = new Error();
      err.response = {status: 400};
      throw err;
    });

    fireEvent.click(screen.getByTestId('more-button'));

    const reminderEmailBtn = screen.getByTestId('reminder-btn');

    expect(reminderEmailBtn).toBeInTheDocument();
    fireEvent.click(reminderEmailBtn);

    // Reminder Email Modal Opens
    expect(screen.getByTestId('unaddressed-comment-modal')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Confirm'));
  });
});

describe('Generate New Report Modal', () => {
  test('Open Modal & Generate Report Modal', async () => {
    render(
      wrappedComponent(
        reportDetailsWithStatusApproved,
        mockEnabledReportRowsWithCounts,
        false,
        mockReportHistoryBothApproved,
      ),
    );

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: {message: 'success'},
        status: 200,
      });
    });

    (axios.get as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: mockReportHistoryReportSubmitted,
        status: 200,
      });
    });

    fireEvent.click(screen.getByTestId('more-button'));

    expect(
      screen.getByTestId('dropdown-menu-item-SUBMITTED'),
    ).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('dropdown-menu-item-SUBMITTED'));

    fireEvent.click(screen.getByTestId('confirm-submit-report'));

    await waitFor(() => {
      fireEvent.click(screen.getByTestId('confirm-generate-new-report'));

      (axios.post as jest.Mock).mockImplementationOnce(() => {
        return Promise.resolve({
          data: {message: 'success'},
          status: 200,
        });
      });
    });
  });

  test('Open Modal & throw Error while Generating Report Modal', async () => {
    render(
      wrappedComponent(
        reportDetailsWithStatusApproved,
        mockEnabledReportRowsWithCounts,
        false,
        mockReportHistoryBothApproved,
      ),
    );

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: {message: 'success'},
        status: 200,
      });
    });
    (axios.get as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: mockReportHistoryReportSubmitted,
        status: 200,
      });
    });

    fireEvent.click(screen.getByTestId('more-button'));

    expect(
      screen.getByTestId('dropdown-menu-item-SUBMITTED'),
    ).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('dropdown-menu-item-SUBMITTED'));

    fireEvent.click(screen.getByTestId('confirm-submit-report'));

    await waitFor(() => {
      fireEvent.click(screen.getByTestId('confirm-generate-new-report'));
    });
  });
});
