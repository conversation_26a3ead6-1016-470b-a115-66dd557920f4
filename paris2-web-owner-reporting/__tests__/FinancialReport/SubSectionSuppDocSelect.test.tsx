import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import SubSectionSuppDocSelect, {
  FmlDetails,
} from '../../src/components/FinancialReport/SubSectionSuppDocSelect';
import {useAlertContext, useDataStoreContext} from '../../src/context';
import {ReportType} from '../../src/enums';
import {Loader} from '../../src/components/common';

jest.mock('../../src/context', () => ({
  useAlertContext: jest.fn(),
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../src/components/common', () => ({
  Loader: jest.fn(() => <div data-testid="loader">Loading...</div>),
}));

describe('SubSectionSuppDocSelect', () => {
  const mockAlert = jest.fn();
  const mockSetDataStore = jest.fn();

  const mockDataStoreContext = {
    roleConfig: {
      ownerReporting: {canActionalbleLineItem: true},
      isOwner: false,
    },
    dataStore: {
      reportDetails: {id: 1, vesselId: 123, isViewOnly: false, status: 'DRAFT'},
      actionableLineItem: {
        disableSuppDoc: {CFS: ['fml1', 'fml2']},
        hideFromOwner: {CFS: []},
      },
    },
    setDataStore: mockSetDataStore,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAlertContext as jest.Mock).mockReturnValue({alert: mockAlert});
    (useDataStoreContext as jest.Mock).mockReturnValue(mockDataStoreContext);
  });

  const rows = [
    {accountCode: '123', fmlId: 'fml1'},
    {accountCode: '456', fmlId: 'fml2'},
  ];

  it('renders the component with checkbox when conditions are met', () => {
    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    expect(screen.getByTestId('selectAllCheckbox')).toBeInTheDocument();
    expect(screen.getByTestId('selectAllCheckbox')).toBeChecked();
  });

  it('does not render the checkbox when conditions are not met', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      ...mockDataStoreContext,
      roleConfig: {
        ownerReporting: {canActionalbleLineItem: false},
        isOwner: true,
      },
    });

    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    expect(screen.queryByTestId('selectAllCheckbox')).not.toBeInTheDocument();
  });

  it('checks the checkbox when all rows are selected', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      ...mockDataStoreContext,
      dataStore: {
        ...mockDataStoreContext.dataStore,
        actionableLineItem: {
          disableSuppDoc: {CFS: ['fml1', 'fml2']},
          hideFromOwner: {CFS: ['fml1']},
        },
      },
    });

    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    expect(screen.getByTestId('selectAllCheckbox')).toBeChecked();
  });

  it('handles checkbox change and updates the state', async () => {
    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    fireEvent.click(checkbox);
  });

  it('shows the loader when loading', async () => {
    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    fireEvent.click(checkbox);

    expect(screen.getByTestId('loader')).toBeInTheDocument();
  });

  it('displays an alert when an error occurs', async () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      ...mockDataStoreContext,
      setDataStore: () => {
        throw new Error('Test Error');
      },
    });

    render(
      <SubSectionSuppDocSelect
        rows={rows}
        reportType={ReportType.CFS_ACCRUAL}
      />,
    );

    const checkbox = screen.getByTestId('selectAllCheckbox');
    fireEvent.click(checkbox);

    await waitFor(() => {
      expect(mockAlert).toHaveBeenCalledWith('danger', 'Something went wrong');
    });
  });
});
