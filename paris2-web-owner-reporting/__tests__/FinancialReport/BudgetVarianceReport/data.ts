export const mockBVRRemarkNode = {
  deleted: false,
  deletedOn: null,
  deletedBy: null,
  createdOn: '2023-10-12T12:21:13.994Z',
  modifiedOn: '2023-10-12T12:21:13.994Z',
  createdBy: 'c2f84469-c900-4fe7-a0ad-ab98420358ed',
  modifiedBy: 'c2f84469-c900-4fe7-a0ad-ab98420358ed',
  id: 71,
  reportId: 4415,
  reportType: 'VA',
  value: 'Hi',
  fmlId: '1',
};

export const mockBudgetVarianceReportData = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9729,
    serial: 0,
    rowType: null,
    section: 'Crew Wages',
    mappedAccountType: 'Crew (Wages)',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 70415.99,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 70415.99,
    yearToDateBudget: 0,
    variance: -70415.99,
    variancePercentage: 0,
    dailyAverage: 1154.*************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9730,
    serial: 1,
    rowType: null,
    section: 'Crew Expenses',
    mappedAccountType: 'Crew (Travel & Other Expenses)',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 10376.14,
    month02Actual: 36087.9,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 46464.04,
    yearToDateBudget: 0,
    variance: -46464.04,
    variancePercentage: 0,
    dailyAverage: 761.************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
    remarks: mockBVRRemarkNode,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9731,
    serial: 2,
    rowType: null,
    section: 'Crew Victualling',
    mappedAccountType: 'Crew (Victualling)',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 5048,
    month02Actual: 6008,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 11056,
    yearToDateBudget: 0,
    variance: -11056,
    variancePercentage: 0,
    dailyAverage: 181.************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9732,
    serial: 3,
    rowType: null,
    section: 'Stores',
    mappedAccountType: 'Store',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 1925.78,
    month02Actual: 10115.79,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 12041.57,
    yearToDateBudget: 0,
    variance: -12041.57,
    variancePercentage: 0,
    dailyAverage: 197.************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9733,
    serial: 4,
    rowType: null,
    section: 'Spares',
    mappedAccountType: 'Spare Parts',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 2306.*************,
    month02Actual: 17767.15,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 20073.37,
    yearToDateBudget: 0,
    variance: -20073.37,
    variancePercentage: 0,
    dailyAverage: 329.************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9734,
    serial: 5,
    rowType: null,
    section: 'Repairs',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 8908.37,
    month02Actual: 2181.12,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 11089.49,
    yearToDateBudget: 0,
    variance: -11089.49,
    variancePercentage: 0,
    dailyAverage: 181.794918032787,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9735,
    serial: 6,
    rowType: null,
    section: 'Lube Oil',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 10202.800000000001,
    month02Actual: 570.59,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 10773.39,
    yearToDateBudget: 0,
    variance: -10773.39,
    variancePercentage: 0,
    dailyAverage: 176.612950819672,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9736,
    serial: 7,
    rowType: null,
    section: 'Vsls. Comm.',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 1989.9,
    month02Actual: 2405.33,
    month03Actual: 2050.04,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 4395.2300000000005,
    yearToDateBudget: 0,
    variance: -4395.2300000000005,
    variancePercentage: 0,
    dailyAverage: 72.05295081967212,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.325Z',
    id: 9737,
    serial: 8,
    rowType: null,
    section: 'Supdt. Travel',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 2190.71,
    month02Actual: 418.77,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 2609.48,
    yearToDateBudget: 0,
    variance: -2609.48,
    variancePercentage: 0,
    dailyAverage: 42.7783606557377,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9738,
    serial: 9,
    rowType: null,
    section: 'Misc.',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 2113.26,
    month02Actual: 2806.08,
    month03Actual: -10.57,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 4919.34,
    yearToDateBudget: 0,
    variance: -4919.34,
    variancePercentage: 0,
    dailyAverage: 80.64491803278688,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9739,
    serial: 10,
    rowType: null,
    section: 'Insurance & Deductibles',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 32324,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 32324,
    yearToDateBudget: 0,
    variance: -32324,
    variancePercentage: 0,
    dailyAverage: 529.901639344262,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9740,
    serial: 11,
    rowType: null,
    section: 'Mgt Fee',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 7000,
    month02Actual: 7000,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 14000,
    yearToDateBudget: 0,
    variance: -14000,
    variancePercentage: 0,
    dailyAverage: 229.508196721311,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9741,
    serial: 12,
    rowType: null,
    section: 'Total OPEX',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 154801.17,
    month02Actual: 85360.73,
    month03Actual: 2039.47,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 240161.9,
    yearToDateBudget: 100,
    variance: -240161.9,
    variancePercentage: 0,
    dailyAverage: 3937.*************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9742,
    serial: 13,
    rowType: null,
    section: 'Charterers Expenses',
    mappedAccountType: "Charterer's Entertainment Expenses",
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 0,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 0,
    yearToDateBudget: 0,
    variance: 0,
    variancePercentage: 0,
    dailyAverage: 0,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9743,
    serial: 14,
    rowType: null,
    section: 'Non Budget Items',
    mappedAccountType: 'Non-Budget Expenses',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 8970,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 8970,
    yearToDateBudget: 0,
    variance: -8970,
    variancePercentage: 0,
    dailyAverage: 147.************,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9744,
    serial: 15,
    rowType: null,
    section: 'Total with Non Budget Items',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 163771.17,
    month02Actual: 85360.73,
    month03Actual: 2039.47,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 249131.9,
    yearToDateBudget: 0,
    variance: -249131.9,
    variancePercentage: 0,
    dailyAverage: 4084.129508196721,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9745,
    serial: 16,
    rowType: null,
    section: 'Pre-delivery',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 0,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 0,
    yearToDateBudget: 0,
    variance: 0,
    variancePercentage: 0,
    dailyAverage: 0,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9746,
    serial: 17,
    rowType: null,
    section: 'Dry-dock',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 0,
    month02Actual: 0,
    month03Actual: 0,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 0,
    yearToDateBudget: 0,
    variance: 0,
    variancePercentage: 0,
    dailyAverage: 0,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-08-06T00:00:00.000Z',
    modifiedOn: '2023-08-06T18:52:42.326Z',
    id: 9747,
    serial: 18,
    rowType: null,
    section: 'Grand Total',
    firstPeriodMonth: 'Apr-23',
    reportStartDate: '2023-04-01',
    reportEndDate: '2024-03-31',
    budgetMonthly: 0,
    budgetYearly: 0,
    month01Actual: 163771.17,
    month02Actual: 85360.73,
    month03Actual: 2039.47,
    month04Actual: 0,
    month05Actual: 0,
    month06Actual: 0,
    month07Actual: 0,
    month08Actual: 0,
    month09Actual: 0,
    month10Actual: 0,
    month11Actual: 0,
    month12Actual: 0,
    committedCost: 0,
    yearToDateTotal: 249131.9,
    yearToDateBudget: 0,
    variance: -249131.9,
    variancePercentage: 0,
    dailyAverage: 4084.129508196721,
    noOfDays: 366,
    createdBy: '000',
    modifiedBy: null,
    reportId: 558,
  },
].map((data, i) => ({...data, fmlId: String(i + 1)}));
