import {fireEvent, render, screen} from '@testing-library/react';
import axios from 'axios';
import React from 'react';
import {act} from 'react-dom/test-utils';
import {MemoryRouter} from 'react-router-dom';
import {BudgetVarianceReport} from '../../../src/components';
import {
  DataStoreContext,
  TableActionContextProvider,
} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {IReportDetails} from '../../../src/types';
import {
  mockReportDetailList,
  reportDetailsOwnerWithStatusOngoing,
  reportDetailsWithStatusFrozen,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {axiosGetMockFunc, setupAxios} from '../../utils/setupAxios';
import {mockBudgetVarianceReportData} from './data';
import {ReportType} from '../../../src/enums';
import CommentContextProvider from '../../../src/context/CommentContextProvider';

interface IArgs {
  reportDetails?: IReportDetails;
}

const wrappedComponent = (args?: IArgs) => {
  const {reportDetails = reportDetailsWithStatusOngoing} = args ?? {};

  return (
    <MemoryRouter>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: {
              ...reportDetails,
              createdOn: '2023-01-07T18:30:00.000Z',
            },
            reportDetailsList: mockReportDetailList,
            setReportDetailListData: jest.fn(),
            ownerPreferences: null,
          },
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
          },
        }}
      >
        <AlertContextProvider>
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockBudgetVarianceReportData}
                reportType={ReportType.VA}
              >
                <BudgetVarianceReport />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </AlertContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  );
};

describe('BudgetVarianceReport', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  it('Testing url mapping', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(
        screen.getByText(mockBudgetVarianceReportData[0].section),
      );
    });
  });

  test('testing date selection mutate', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const [_, secondInputElement] = screen.getAllByRole('textbox');

    const selectedEndDate = new Date(reportDetailsWithStatusOngoing.endDate);

    const selectedDay = selectedEndDate.getDate() - 1;

    await act(async () => {
      await fireEvent.focus(secondInputElement);
      await fireEvent.click(screen.getByText(selectedDay));
    });
  });

  it('Testing Approved Budgets button', async () => {
    mockBudgetVarianceReportData;

    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/variances/'))
        return {
          data: [
            {
              ...mockBudgetVarianceReportData[0],
              reportEndDate: new Date(Date.now()),
            },
            ...mockBudgetVarianceReportData.slice(1),
          ],
        };

      return axiosGetMockFunc(url);
    });

    await act(async () => {
      render(wrappedComponent());
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(1);

    await act(async () => {
      fireEvent.click(screen.getByText('Other Documents'));
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(2);
  });

  it('Testing Approved Budgets button', async () => {
    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/variances/'))
        return {
          data: [
            {
              ...mockBudgetVarianceReportData[0],
              reportEndDate: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000),
            },
            ...mockBudgetVarianceReportData.slice(1),
          ],
        };

      return axiosGetMockFunc(url);
    });

    await act(async () => {
      render(wrappedComponent());
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(1);

    await act(async () => {
      fireEvent.click(screen.getByText('Other Documents'));
    });

    expect(screen.getAllByText('Other Documents')).toHaveLength(2);

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListCloseBtn'));
    });
  });

  test('Testing BVR Remarks', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('bvrRemarkBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('closeRemarkModalBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('bvrRemarkBtn')[0]);
    });

    await act(async () => {
      fireEvent.change(screen.getByTestId('remarksTextArea'), {
        target: {
          value: 'Hello',
        },
      });
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('saveRemarkModalBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('bvrRemarkBtn')[0]);
    });

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Something went wrong');
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('saveRemarkModalBtn'));
    });
  });

  test('Testing BVR Remarks As Owner', async () => {
    await act(async () => {
      render(
        wrappedComponent({reportDetails: reportDetailsOwnerWithStatusOngoing}),
      );
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('bvrRemarkBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('remarksModalCloseBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('bvrRemarkBtn')[1]);
    });
  });

  //testing actions
  test('Testing Transaction Details Rendering', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    expect(screen.queryByText('Transaction Details')).toBeInTheDocument();
    expect(screen.queryByText('All Comments')).toBeInTheDocument();
  });

  //this tests has to be run at the end because their mutating the default mock implementation of axios
  //testing useFetchDataWithAlertOnFailure
  test('Testing useFetchDataWithAlertOnFailure error case', async () => {
    (axios.get as jest.Mock).mockReturnValue(Promise.reject({response: {}}));

    await act(async () => {
      render(wrappedComponent());
    });
  });
});

describe('use Report Status', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  it('renders without error', async () => {
    await act(async () => {
      render(wrappedComponent({reportDetails: reportDetailsWithStatusFrozen}));
    });

    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/variances/2996'))
        return {
          data: [
            {
              ...mockBudgetVarianceReportData[0],
              reportEndDate: new Date(Date.now() + 31 * 24 * 60 * 60 * 1000),
            },
            ...mockBudgetVarianceReportData.slice(1),
          ],
        };
      return axiosGetMockFunc(url);
    });
  });
});
