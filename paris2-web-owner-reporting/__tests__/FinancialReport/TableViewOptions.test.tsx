import {act, fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {TableViewOptions} from '../../src/components/FinancialReport/TableViewOptions';
import {DataStoreContext, TableActionContextProvider} from '../../src/context';
import {reportDetailsWithStatusOngoing} from '../data';
import {ownerReportingAllTrue} from '../data/owner-reporting-role';
import {setupAxios} from '../utils/setupAxios';
import AlertContextProvider from '../../src/context/AlertContextProvider';
import {ReportType} from '../../src/enums';
import {MemoryRouter} from 'react-router-dom';
import RowSelectContextProvider from '../../src/context/RowSelectProvider';

const onDateChange = jest.fn();

const wrappedComponent = (reportDetails = reportDetailsWithStatusOngoing) => (
  <AlertContextProvider>
    <MemoryRouter>
      <DataStoreContext.Provider
        value={{
          dataStore: {reportDetails, ownerPreferences: null},
          setDataStore: jest.fn(),
          ga4EventTrigger: jest.fn(),
          roleConfig: {ownerReporting: ownerReportingAllTrue} as any,
        }}
      >
        <RowSelectContextProvider>
          <TableActionContextProvider>
            <TableViewOptions
              onRefreshBtnClick={jest.fn()}
              reportType={ReportType.CFS_ACCRUAL}
            />
          </TableActionContextProvider>
        </RowSelectContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  </AlertContextProvider>
);

describe('TableViewOptions', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  test('renders without errors', () => {
    render(wrappedComponent());
  });

  test('renders with report detail status as ongoing', () => {
    render(wrappedComponent());

    expect(screen.getByText('From')).toBeInTheDocument();
    expect(screen.getByText('To')).toBeInTheDocument();
  });

  test('onDateRange selection', () => {
    render(wrappedComponent());

    const [_, secondInputElement] = screen.getAllByRole('textbox');

    const selectedEndDate = new Date(reportDetailsWithStatusOngoing.endDate);

    const selectedDay = selectedEndDate.getDate() - 1;

    selectedEndDate.setDate(selectedDay);

    fireEvent.focus(secondInputElement);
    fireEvent.click(screen.getByText(selectedDay));

    selectedEndDate.setDate(selectedDay);
  });

  test('what if the end date is greater than the current date', () => {
    render(
      wrappedComponent({
        ...reportDetailsWithStatusOngoing,
        endDate: new Date(Date.now() + 1000 * 60).toISOString(),
      }),
    );
  });

  test('Clear All Filters', () => {
    render(wrappedComponent());
    fireEvent.click(screen.getByTestId('clear-all-filters'));
  });

  test('Change Adv Search Input Value & handle Adv Search dropdown', async () => {
    render(wrappedComponent());

    await act(() => {
      fireEvent.change(screen.getByTestId('search-input'), {
        target: {value: 'testuser'},
      });
    });

    fireEvent.click(screen.getByTestId('advance-search-dropdown-toggle'));

    fireEvent.click(screen.getByTestId('advance-search-dropdown'));
  });

  test('Click Refresh Button', async () => {
    render(wrappedComponent());
    const refreshButton = screen.getAllByTestId('refresh')[0];
    fireEvent.keyDown(refreshButton, {
      key: 'Enter',
    });
  });
});
