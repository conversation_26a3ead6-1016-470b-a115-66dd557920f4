import {act, fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import CashFlowCollapsible from '../../../src/components/FinancialReport/CashFlowStatement/CashFlowCollapsible';
import {cashFlowHydration} from '../../../src/components/FinancialReport/CashFlowStatement/utils';
import {
  RowSelectContext,
  TableActionContextProvider,
} from '../../../src/context';
import DataStoreProvider, {
  DataStoreContext,
} from '../../../src/context/DataStoreProvider';
import DocumentContextProvider from '../../../src/context/DocumentProvider';
import {getDocumentWithCountLabel} from '../../../src/utils/document';
import {getReportTypeCFS} from '../../../src/utils/reportType';
import {reportDetailsWithStatusOngoing, userRoleConfig} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {axiosGetMockFunc, setupAxios} from '../../utils/setupAxios';
import {mockActionableItemsData, mockCashFlowData} from './data';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import axios from 'axios';
import {ReportType} from '../../../src/enums';
import CommentContextProvider from '../../../src/context/CommentContextProvider';

const hydrationData = cashFlowHydration(mockCashFlowData as any);

const props = {
  rows: (hydrationData.data[1] as any).children,
  title: hydrationData.data[1].section,
};

let rowSelected: any[] = [];

const defaultSetRowSelectedFunction = () => row => {
  rowSelected.length = 0;
  rowSelected.push(...row);
};

const mockSetRowSelected = jest.fn(defaultSetRowSelectedFunction());
const mockSetDataStoreSetter = jest.fn();

const wrappedComponent = (args?: Partial<typeof props>, extraRole?: object) => (
  <MemoryRouter initialEntries={['/owner-reporting/783/reports/3194/']}>
    <AlertContextProvider>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: reportDetailsWithStatusOngoing,
            ownerPreferences: null,
            actionableLineItem: mockActionableItemsData,
          },
          setDataStore: mockSetDataStoreSetter,
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: {...ownerReportingAllTrue, ...extraRole},
          } as any,
        }}
      >
        {/* <DataStoreProvider
        roleConfig={
          {user: userRoleConfig, ownerReporting: ownerReportingAllTrue} as any
        }
      > */}
        <RowSelectContext.Provider
          value={{rowSelected, setRowSelected: mockSetRowSelected}}
        >
          <TableActionContextProvider>
            <DocumentContextProvider
              reportId={reportDetailsWithStatusOngoing.id}
              reportType={getReportTypeCFS()}
            >
              <CommentContextProvider
                reportData={mockCashFlowData}
                reportType={ReportType.CFS_ACCRUAL}
              >
                <CashFlowCollapsible {...props} {...args} />
              </CommentContextProvider>
            </DocumentContextProvider>
          </TableActionContextProvider>
        </RowSelectContext.Provider>
      </DataStoreContext.Provider>
    </AlertContextProvider>
  </MemoryRouter>
);

describe('Testing CashFlowCollapsible', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  test('renders the component with the provided title', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    expect(
      screen.getByText(
        props.title.includes('B)') ? props.title.slice(3) : props.title,
      ),
    ).toBeInTheDocument();
  });

  test('displays the provided rows when rows are present', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    // Add assertions to check the rendered rows

    const rowElement = screen.getAllByText(props.rows[0].accountCode);
    expect(rowElement.length).toBe(3);
  });

  test('displays no rows when rows are empty', async () => {
    await act(async () => {
      render(wrappedComponent({rows: []}));
    });

    // Add assertions to check that no rows are rendered

    expect(
      screen.queryByTestId('cashFlowCollapsibleTable'),
    ).not.toBeInTheDocument();
  });

  test('renders the footer', async () => {
    const rows = (hydrationData.data[3] as any).children[1].children;

    await act(async () => {
      render(wrappedComponent({rows}));
    });

    expect(screen.getByText('Total')).toBeInTheDocument();
  });

  test('not rendering the footer', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    expect(screen.queryByText('Total')).not.toBeInTheDocument();
  });
  test('Testing Each List Item Document Sidebar Rendering', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDocumentCountNode = screen.getAllByTestId(
      'cashflowDocumentCount',
    )[0];

    await act(async () => {
      fireEvent.click(firstDocumentCountNode);
    });

    const [selectedDocuments, totalDocuments] = firstDocumentCountNode
      .textContent!.split('/')
      .map(val => Number(val));

    expect(
      screen.queryByText(
        getDocumentWithCountLabel({
          selectedDocuments,
          totalDocuments,
        }),
      ),
    ).toBeInTheDocument();
  });

  test('toggling the row selection checkbox', async () => {
    rowSelected = [];
    mockSetRowSelected.mockImplementation(defaultSetRowSelectedFunction());

    await act(async () => {
      render(wrappedComponent());
    });

    const allCheckboxes = screen.getAllByRole('checkbox');

    expect(mockSetRowSelected.mock.calls.length).toBe(0);
    expect(rowSelected.length).toBe(0);

    await act(async () => {
      fireEvent.click(allCheckboxes[1]);
    });

    expect(mockSetRowSelected.mock.calls.length).toBe(1);
    expect(rowSelected.length).toBe(1);

    await act(async () => {
      fireEvent.click(allCheckboxes[1]);
    });

    expect(mockSetRowSelected.mock.calls.length).toBe(2);
    expect(rowSelected.length).toBe(0);
  });
  test('toggling the all sub section row selection checkbox', async () => {
    rowSelected = [];
    mockSetRowSelected.mockImplementation(defaultSetRowSelectedFunction());

    await act(async () => {
      render(wrappedComponent());
    });

    const allCheckboxes = screen.getAllByRole('checkbox');

    expect(mockSetRowSelected.mock.calls.length).toBe(2);
    expect(rowSelected.length).toBe(0);

    await act(async () => {
      fireEvent.click(allCheckboxes[0]);
    });

    expect(mockSetRowSelected.mock.calls.length).toBe(3);
    expect(rowSelected.length).toBe(5);

    await act(async () => {
      fireEvent.click(allCheckboxes[0]);
    });

    expect(mockSetRowSelected.mock.calls.length).toBe(4);
    expect(rowSelected.length).toBe(5);
  });

  test('toggling the row checkbox for Optional Documents', async () => {
    await act(async () => {
      render(wrappedComponent({}, {canActionalbleLineItem: true}));
    });

    // as per provided data first two checkboxes will be seleceted by default for optional documents
    // as per provided data only first  checkboxes will be seleceted by default for hide from owner
    const allSuppDocCheckboxes = screen.getAllByTestId(
      'cashFlowStatementDisableSuppDocCheckbox',
    );
    const allHideFromOwnerCheckboxes = screen.getAllByTestId(
      'cashFlowStatementHideFromOwnerCheckbox',
    );
    expect(allSuppDocCheckboxes[0]).not.toBeChecked();
    expect(allSuppDocCheckboxes[1]).not.toBeChecked();
    expect(allSuppDocCheckboxes[2]).not.toBeChecked();

    expect(allHideFromOwnerCheckboxes[0]).not.toBeChecked();
    expect(allHideFromOwnerCheckboxes[1]).not.toBeChecked();
    expect(allHideFromOwnerCheckboxes[2]).not.toBeChecked();

    // selecting unchecked optional document checkbox
    await act(async () => {
      fireEvent.click(allSuppDocCheckboxes[2]);
    });

    // selecting checked Hide from Owner checkbox
    await act(async () => {
      fireEvent.click(allSuppDocCheckboxes[0]);
    });
  });

  test('selecting all the rows', async () => {
    rowSelected = [];
    mockSetRowSelected.mockClear();
    mockSetRowSelected.mockImplementation(defaultSetRowSelectedFunction());

    await act(async () => {
      render(wrappedComponent());
    });

    const allCheckboxes = screen.getAllByRole('checkbox');

    expect(mockSetRowSelected.mock.calls.length).toBe(0);
    expect(rowSelected.length).toBe(0);

    for (const checkbox of allCheckboxes) fireEvent.click(checkbox);

    expect(mockSetRowSelected.mock.calls.length).toBe(6);
    expect(rowSelected.length).toBe(0);

    for (const checkbox of allCheckboxes) fireEvent.click(checkbox);

    expect(mockSetRowSelected.mock.calls.length).toBe(12);
    expect(rowSelected.length).toBe(0);
  });

  test('testing selected item rendering', async () => {
    rowSelected = [props.rows[2], props.rows[1]];
    mockSetRowSelected.mockImplementation();

    await act(async () => {
      render(wrappedComponent());
    });

    const allCheckboxes = screen.getAllByRole('checkbox', {checked: true});

    expect(allCheckboxes.length).toBe(17);
  });

  //testing actions
  test('Testing Transaction Details Rendering', async () => {
    rowSelected = [];
    mockSetRowSelected.mockImplementation();
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });
    expect(screen.queryByText('Transaction Details')).toBeInTheDocument();
    expect(screen.queryByText('All Comments')).toBeInTheDocument();
  });
});
