import {act, render, screen} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import RenderSubRows from '../../../src/components/FinancialReport/CashFlowStatement/RenderSubRows';
import {cashFlowHydration} from '../../../src/components/FinancialReport/CashFlowStatement/utils';
import DataStoreProvider from '../../../src/context/DataStoreProvider';
import DocumentContextProvider from '../../../src/context/DocumentProvider';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {
  CashFlowHydrationExpandable,
  CashFlowNodeWithChildren,
} from '../../../src/types';
import {getReportTypeCFS} from '../../../src/utils/reportType';
import {reportDetailsWithStatusOngoing, userRoleConfig} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {setupAxios} from '../../utils/setupAxios';
import {mockCashFlowData} from './data';
import {TableActionContextProvider} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import CommentContextProvider from '../../../src/context/CommentContextProvider';
import {ReportType} from '../../../src/enums';

const hydrationData = cashFlowHydration(mockCashFlowData as any);

jest.mock('../../../src/components/Table', () => ({
  ...jest.requireActual('../../../src/components/Table'),
  TableAccordion: jest.fn().mockImplementation(childrenComponent => {
    return () => childrenComponent;
  }),
}));

const WrappedComponent = (
  data: CashFlowHydrationExpandable | CashFlowNodeWithChildren,
) => (
  <MemoryRouter initialEntries={['/owner-reporting/783/reports/3194/']}>
    <AlertContextProvider>
      <DataStoreProvider
        roleConfig={{
          ownerReporting: ownerReportingAllTrue,
          user: userRoleConfig,
        }}
      >
        <RowSelectContextProvider>
          <TableActionContextProvider>
            <DocumentContextProvider
              reportId={reportDetailsWithStatusOngoing.id}
              reportType={getReportTypeCFS()}
            >
              <CommentContextProvider
                reportData={mockCashFlowData}
                reportType={ReportType.CFS_ACCRUAL}
              >
                {RenderSubRows({data})!({} as any)}
              </CommentContextProvider>
            </DocumentContextProvider>
          </TableActionContextProvider>
        </RowSelectContextProvider>
      </DataStoreProvider>
    </AlertContextProvider>
  </MemoryRouter>
);

describe('Testing RenderSubRows', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  test('If Children is not available should return null', async () => {
    await act(async () => {
      expect(RenderSubRows({data: hydrationData.data[0]})).toBe(null);
    });
  });

  test('Testing with cashFlowHydrationExpandable value', async () => {
    const cashFlowHydrationExpandable = hydrationData.data[1];
    await act(async () => {
      render(WrappedComponent(cashFlowHydrationExpandable));
    });
    screen.getByTestId('cashFlowCollapsibleTable');
  });

  test('Testing with cashFlowNodeWithChildren value', async () => {
    const cashFlowNodeWithChildren = hydrationData.data[1];
    await act(async () => {
      render(WrappedComponent(cashFlowNodeWithChildren));
    });
    screen.getByTestId('cashFlowCollapsibleTable');
  });
});
