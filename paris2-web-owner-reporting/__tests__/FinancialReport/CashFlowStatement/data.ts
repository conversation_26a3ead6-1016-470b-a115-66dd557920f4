import {SyncStatus} from '../../../src/enums/sync-status.enum';
import {mockDocumentsCountList} from '../../data';

export const mockCashFlowData = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.850Z',
    modifiedOn: '2023-05-26T12:35:48.850Z',
    id: 1,
    serial: 0,
    rowType: null,
    fmlId: '0-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'A) Balance of Retained Earnings from last month',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 234340,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    syncStatus: SyncStatus.COMPLETE,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.851Z',
    modifiedOn: '2023-05-26T12:35:48.851Z',
    id: 2,
    serial: 1,
    rowType: null,
    fmlId: mockDocumentsCountList[0].fmlId,
    transactionDate: '2023-08-16',
    format: 'ACCRUAL',
    section: 'B) Funds Received',
    accountCode: 'VBLIOWN01',
    oppositeCode: null,
    account: null,
    accountType: 'OwnerCA',
    particulars:
      'OCEAN TIANBAO SHIPPING LIMITED-T54LCIC562970 Invoice# : ********** FM_Fund_Received USD',
    currency: 'USD',
    currencyAmount: 156849.4,
    exchangeRate: 1,
    usdAmount: 156849.4,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    syncStatus: SyncStatus.ERROR_LIST,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.852Z',
    modifiedOn: '2023-05-26T12:35:48.852Z',
    id: 3,
    serial: 2,
    rowType: null,
    fmlId: mockDocumentsCountList[1].fmlId,
    format: 'ACCRUAL',
    section: 'B) Funds Received',
    accountCode: 'VBLIOWN01',
    oppositeCode: null,
    account: 'Operating Funds',
    accountType: 'OwnerCA',
    particulars:
      'OCEAN TIANBAO SHIPPING LIMITED-T54LCIC562970 Invoice# : ********** FM_Fund_Received USD',
    currency: 'USD',
    currencyAmount: 156849.4,
    exchangeRate: 1,
    usdAmount: 156849.4,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    transactionDate: '2023-08-16',
    syncStatus: SyncStatus.COMPLETE,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.852Z',
    modifiedOn: '2023-05-26T12:35:48.852Z',
    id: 4,
    serial: 3,
    rowType: null,
    fmlId: mockDocumentsCountList[2].fmlId,
    format: 'ACCRUAL',
    section: 'B) Funds Received',
    accountCode: 'VBLIOWN01',
    oppositeCode: null,
    account: 'Operating Funds',
    accountType: 'OwnerCA',
    particulars:
      'OCEAN TIANBAO SHIPPING LIMITED-T54LCIC562970 Invoice# : ********** FM_Fund_Received USD',
    currency: 'USD',
    currencyAmount: 156849.4,
    exchangeRate: 1,
    usdAmount: 156849.4,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.852Z',
    modifiedOn: '2023-05-26T12:35:48.852Z',
    id: 5,
    serial: 4,
    rowType: null,
    fmlId: mockDocumentsCountList[3].fmlId,
    format: 'ACCRUAL',
    section: 'B) Funds Received',
    accountCode: 'VBLIOWN03',
    oppositeCode: null,
    account: 'Non-Budget Funds',
    accountType: 'OwnerCA',
    particulars:
      'OCEAN TIANBAO SHIPPING LIMITED-T54LCIC562970 Invoice# : ********** FM_Fund_Received USD',
    currency: 'USD',
    currencyAmount: 13554.11,
    exchangeRate: 1,
    usdAmount: 13554.11,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.852Z',
    modifiedOn: '2023-05-26T12:35:48.852Z',
    id: 6,
    serial: 5,
    rowType: null,
    fmlId: mockDocumentsCountList[4].fmlId,
    format: 'ACCRUAL',
    section: 'B) Funds Received',
    accountCode: 'VBLIOWN03',
    oppositeCode: null,
    account: 'Non-Budget Funds',
    accountType: 'OwnerCA',
    particulars:
      'OCEAN TIANBAO SHIPPING LIMITED-T54LCIC562970 Invoice# : ********** FM_Fund_Received USD',
    currency: 'USD',
    currencyAmount: 28031.81,
    exchangeRate: 1,
    usdAmount: 28031.81,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.852Z',
    modifiedOn: '2023-05-26T12:35:48.852Z',
    id: 7,
    serial: 6,
    rowType: null,
    fmlId: mockDocumentsCountList[5].fmlId,
    format: 'ACCRUAL',
    section: 'C) Funds available for use',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 512134.12,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.854Z',
    modifiedOn: '2023-05-26T12:35:48.854Z',
    id: 8,
    serial: 7,
    rowType: null,
    fmlId: '7-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'D) Expenses of Current Month',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.854Z',
    modifiedOn: '2023-05-26T12:35:48.854Z',
    id: 9,
    serial: 8,
    rowType: null,
    fmlId: '8-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Balance Sheet Item',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.855Z',
    id: 10,
    serial: 9,
    rowType: null,
    fmlId: '9-VOCCFC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Wages)',
    accountCode: 'VOCCFC001',
    oppositeCode: null,
    account: 'Fixed Crew Wages',
    accountType: 'CrewWages',
    particulars:
      'Epidemic Allowance for January-22 Invoice# : Epidemic Allowance for January-22 FM_DN_Exp_Recharge USD',
    currency: 'USD',
    currencyAmount: 3464.6,
    exchangeRate: 1,
    usdAmount: 3464.6,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.856Z',
    id: 11,
    serial: 10,
    rowType: null,
    fmlId: '10-VOCCFC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Wages)',
    accountCode: 'VOCCFC001',
    oppositeCode: null,
    account: 'Fixed Crew Wages',
    accountType: 'CrewWages',
    particulars: 'Fixed Crew Wages for the month of Jan-22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 93333,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.856Z',
    id: 12,
    serial: 11,
    rowType: null,
    fmlId: '11-VOCCFC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Wages)',
    accountCode: 'VOCCFC001',
    oppositeCode: null,
    account: 'Fixed Crew Wages',
    accountType: 'CrewWages',
    particulars:
      'Adj Fixed Crew - Wages - Oct to Dec 2021 Invoice# : Adj Fixed Crew - Wages - Oct to Dec 2021 FM_DN_Exp_Recharge USD',
    currency: 'USD',
    currencyAmount: 49999.************,
    exchangeRate: 1,
    usdAmount: 49999.************,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.856Z',
    id: 13,
    serial: 12,
    rowType: null,
    fmlId: '12-VOCCFC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Wages)',
    accountCode: 'VOCCFC001',
    oppositeCode: null,
    account: 'Fixed Crew Wages',
    accountType: 'CrewWages',
    particulars:
      'Reverse Epidemic Allowance for January-22 Invoice# : Reverse Epidemic Allowance for January-22 FM_DN_Exp_Recharge USD',
    currency: 'USD',
    currencyAmount: -3464.6,
    exchangeRate: 1,
    usdAmount: -3464.6,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.856Z',
    id: 14,
    serial: 13,
    rowType: null,
    fmlId: '13-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Wages)',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.856Z',
    modifiedOn: '2023-05-26T12:35:48.856Z',
    id: 15,
    serial: 14,
    rowType: null,
    fmlId: '14-VOCCCE001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Travel & Other Expenses)',
    accountCode: 'VOCCCE001',
    oppositeCode: null,
    account: 'Crew Licenses',
    accountType: 'CrewExpense',
    particulars:
      'THE HONGKONG SHIPPING REGISTRY/THE MARINE DEPARTMENT OF HKSAR INV-134L17609594-010621-300621 Invoice# : Manisha_THE HONGKONG SHIPPING REGISTRY/THE MARINE DEPARTMENT OF HKSAR Adjustment USD',
    currency: 'USD',
    currencyAmount: -315.**************,
    exchangeRate: 1,
    usdAmount: -315.**************,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.858Z',
    modifiedOn: '2023-05-26T12:35:48.858Z',
    id: 16,
    serial: 15,
    rowType: null,
    fmlId: '15-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Travel & Other Expenses)',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.858Z',
    modifiedOn: '2023-05-26T12:35:48.858Z',
    id: 17,
    serial: 16,
    rowType: null,
    fmlId: '16-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Crew (Victualling)',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.858Z',
    modifiedOn: '2023-05-26T12:35:48.858Z',
    id: 18,
    serial: 17,
    rowType: null,
    fmlId: '17-VOSTOT002-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTOT002',
    oppositeCode: null,
    account: 'Stores : Others',
    accountType: 'VslStores',
    particulars: '20 Invoice# : 147846',
    currency: 'USD',
    currencyAmount: 26.3,
    exchangeRate: 1,
    usdAmount: 26.3,
    invoiceNumber: '147846',
    invoiceDate: '2021-10-11T18:30:00.000Z',
    serialNumber: '502A220378',
    poNumber: '310/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=768acf73-a0fe-4f8e-b736-a52d26850cf9',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.858Z',
    modifiedOn: '2023-05-26T12:35:48.858Z',
    id: 19,
    serial: 18,
    rowType: null,
    fmlId: '18-VOSTCA006-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTCA006',
    oppositeCode: null,
    account: 'Stores : Medicines',
    accountType: 'VslStores',
    particulars: ' Invoice# : 98458',
    currency: 'USD',
    currencyAmount: 1250,
    exchangeRate: 1,
    usdAmount: 1250,
    invoiceNumber: '98458',
    invoiceDate: '2021-10-14T18:30:00.000Z',
    serialNumber: '502A22P006',
    poNumber: '286/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=282f4699-f11b-4263-a60a-246d7dee41ed',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.859Z',
    modifiedOn: '2023-05-26T12:35:48.859Z',
    id: 20,
    serial: 19,
    rowType: null,
    fmlId: '19-VOSTDK001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTDK001',
    oppositeCode: null,
    account: 'Stores : Charts & Publications',
    accountType: 'VslStores',
    particulars: ' Invoice# : ********',
    currency: 'USD',
    currencyAmount: 2899,
    exchangeRate: 1,
    usdAmount: 2899,
    invoiceNumber: '********',
    invoiceDate: '2021-12-30T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '011/22',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=efe67c3b-0148-4d01-ac95-1f550f8a1e18',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.859Z',
    modifiedOn: '2023-05-26T12:35:48.859Z',
    id: 21,
    serial: 20,
    rowType: null,
    fmlId: '20-VOSTCE001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTCE001',
    oppositeCode: null,
    account: 'Stores : COVID Supplies',
    accountType: 'VslStores',
    particulars: ' Invoice# : P10-62915-05',
    currency: 'USD',
    currencyAmount: 670,
    exchangeRate: 1,
    usdAmount: 670,
    invoiceNumber: 'P10-62915-05',
    invoiceDate: '2021-10-06T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '170/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=0c15def0-4691-41e0-a222-0cf45c1958ac',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.859Z',
    modifiedOn: '2023-05-26T12:35:48.859Z',
    id: 22,
    serial: 21,
    rowType: null,
    fmlId: '21-VOSTCE001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTCE001',
    oppositeCode: null,
    account: 'Stores : COVID Supplies',
    accountType: 'VslStores',
    particulars: 'Invoice# : P10-62915-05',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: -1340,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.860Z',
    modifiedOn: '2023-05-26T12:35:48.860Z',
    id: 23,
    serial: 22,
    rowType: null,
    fmlId: '22-VOSTAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTAC001',
    oppositeCode: null,
    account: 'Stores : Committed',
    accountType: 'VslStores',
    particulars: '1001_CC_JAN_22_004/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 4065.13,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.861Z',
    modifiedOn: '2023-05-26T12:35:48.861Z',
    id: 24,
    serial: 23,
    rowType: null,
    fmlId: '23-VOSTAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTAC001',
    oppositeCode: null,
    account: 'Stores : Committed',
    accountType: 'VslStores',
    particulars: '1001_CC_JAN_22_009/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 428.54,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.861Z',
    modifiedOn: '2023-05-26T12:35:48.861Z',
    id: 25,
    serial: 24,
    rowType: null,
    fmlId: '24-VOSTAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTAC001',
    oppositeCode: null,
    account: 'Stores : Committed',
    accountType: 'VslStores',
    particulars: '1001_CC_JAN_22_005/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 348.1,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.861Z',
    modifiedOn: '2023-05-26T12:35:48.861Z',
    id: 26,
    serial: 25,
    rowType: null,
    fmlId: '25-VOSTAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: 'VOSTAC001',
    oppositeCode: null,
    account: 'Stores : Committed',
    accountType: 'VslStores',
    particulars: '1001_CC_JAN_22_019/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 3851,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.861Z',
    modifiedOn: '2023-05-26T12:35:48.861Z',
    id: 27,
    serial: 26,
    rowType: null,
    fmlId: '26-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Store',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.861Z',
    modifiedOn: '2023-05-26T12:35:48.861Z',
    id: 28,
    serial: 27,
    rowType: null,
    fmlId: '27-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Spare Parts',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 29,
    serial: 28,
    rowType: null,
    fmlId: '28-VOLULU002-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Lub Oil',
    accountCode: 'VOLULU002',
    oppositeCode: null,
    account: 'Lubricating Oil (A/E)',
    accountType: 'VslLubes',
    particulars: '1001_Luboil consumption-01/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 399,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 30,
    serial: 29,
    rowType: null,
    fmlId: '29-VOLULU003-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Lub Oil',
    accountCode: 'VOLULU003',
    oppositeCode: null,
    account: 'Lubricating Oil (M/E Crank/System)',
    accountType: 'VslLubes',
    particulars: '1001_Luboil consumption-01/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 780.69,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 31,
    serial: 30,
    rowType: null,
    fmlId: '30-VOLULU004-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Lub Oil',
    accountCode: 'VOLULU004',
    oppositeCode: null,
    account: 'Lubricating Oil (M/E Cylinder)',
    accountType: 'VslLubes',
    particulars: '1001_Luboil consumption-01/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 4401.6,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 32,
    serial: 31,
    rowType: null,
    fmlId: '31-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Lub Oil',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 33,
    serial: 32,
    rowType: null,
    fmlId: '32-VORMSF003-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMSF003',
    oppositeCode: null,
    account: 'R&M : Survey Others',
    accountType: 'VslRepairs',
    particulars: ' Invoice# : M054031',
    currency: 'USD',
    currencyAmount: 200,
    exchangeRate: 1,
    usdAmount: 200,
    invoiceNumber: 'M054031',
    invoiceDate: '2021-11-30T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '154/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=fba5aeb4-5381-4ba1-a8fc-a809c85f5ac3',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 66,
    serial: 65,
    rowType: null,
    fmlId: '65-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Drydock',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 67,
    serial: 66,
    rowType: null,
    fmlId: '66-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Insurance',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 34,
    serial: 33,
    rowType: null,
    fmlId: '33-VORMSF002-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMSF002',
    oppositeCode: null,
    account: 'R&M : Survey Class',
    accountType: 'VslRepairs',
    particulars:
      '********-Vessel Expense Recoverable - Radio station Licence renew Invoice# : TR585',
    currency: 'HKD',
    currencyAmount: 150,
    exchangeRate: 0.1316,
    usdAmount: 19.***************,
    invoiceNumber: 'TR585',
    invoiceDate: '2022-01-26T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: null,
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=travel&code=502A&id=16dc1ffd-fa28-4c60-80aa-2d4c5aa48120',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 35,
    serial: 34,
    rowType: null,
    fmlId: '34-VORMOT006-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMOT006',
    oppositeCode: null,
    account: 'R&M : Others',
    accountType: 'VslRepairs',
    particulars:
      'Captain_Cash Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 200,
    exchangeRate: 1,
    usdAmount: 200,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 36,
    serial: 35,
    rowType: null,
    fmlId: '35-VORMSF003-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMSF003',
    oppositeCode: null,
    account: 'R&M : Survey Others',
    accountType: 'VslRepairs',
    particulars:
      'Captain_Cash Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 500,
    exchangeRate: 1,
    usdAmount: 500,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 37,
    serial: 36,
    rowType: null,
    fmlId: '36-VORMSF003-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMSF003',
    oppositeCode: null,
    account: 'R&M : Survey Others',
    accountType: 'VslRepairs',
    particulars: 'Invoice# : M054031',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: -200,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 38,
    serial: 37,
    rowType: null,
    fmlId: '37-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_012/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 17764.11,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 39,
    serial: 38,
    rowType: null,
    fmlId: '38-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_001/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 3605.26,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 40,
    serial: 39,
    rowType: null,
    fmlId: '39-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_014/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 1235,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 41,
    serial: 40,
    rowType: null,
    fmlId: '40-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_018/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 600,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 42,
    serial: 41,
    rowType: null,
    fmlId: '41-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_017/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 100,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 43,
    serial: 42,
    rowType: null,
    fmlId: '42-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_016/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 100,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 44,
    serial: 43,
    rowType: null,
    fmlId: '43-VORMAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: 'VORMAC001',
    oppositeCode: null,
    account: 'Repair & Maintenance : Committed',
    accountType: 'VslRepairs',
    particulars: '1001_CC_JAN_22_020/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 68.59,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 45,
    serial: 44,
    rowType: null,
    fmlId: '44-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Repairing',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 46,
    serial: 45,
    rowType: null,
    fmlId: '45-VOMFMF001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Management Fee',
    accountCode: 'VOMFMF001',
    oppositeCode: null,
    account: 'Management Fees',
    accountType: 'VslFees',
    particulars: 'Management Fees for the month of Jan-22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 9500,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.862Z',
    modifiedOn: '2023-05-26T12:35:48.862Z',
    id: 47,
    serial: 46,
    rowType: null,
    fmlId: '46-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Management Fee',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 48,
    serial: 47,
    rowType: null,
    fmlId: '47-VOMEME024-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME024',
    oppositeCode: null,
    account: 'Ships Printing & Stationery',
    accountType: 'VslMisc',
    particulars: 'Technical Form Fees for the month of Jan-22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 250,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 49,
    serial: 48,
    rowType: null,
    fmlId: '48-VOMEME018-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME018',
    oppositeCode: null,
    account: 'Ships Communication Satelite',
    accountType: 'VslMisc',
    particulars: ' Invoice# : *********2',
    currency: 'USD',
    currencyAmount: 40,
    exchangeRate: 1,
    usdAmount: 40,
    invoiceNumber: '*********2',
    invoiceDate: '2021-12-30T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '002/22',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=f2eb7fd9-6597-4702-b1f6-230421b3bda5',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 50,
    serial: 49,
    rowType: null,
    fmlId: '49-VOMEME018-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME018',
    oppositeCode: null,
    account: 'Ships Communication Satelite',
    accountType: 'VslMisc',
    particulars: ' Invoice# : ********',
    currency: 'USD',
    currencyAmount: 158,
    exchangeRate: 1,
    usdAmount: 158,
    invoiceNumber: '********',
    invoiceDate: '2021-11-29T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '165/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=452540cf-55b5-48ca-9573-0acc169617fc',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 51,
    serial: 50,
    rowType: null,
    fmlId: '50-VOMEME019-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME019',
    oppositeCode: null,
    account: 'Ships Entertainment Expenses',
    accountType: 'VslMisc',
    particulars:
      'Bonded stores Consumption Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 30,
    exchangeRate: 1,
    usdAmount: 30,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 52,
    serial: 51,
    rowType: null,
    fmlId: '51-VOMEME002-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME002',
    oppositeCode: null,
    account: 'Ctm Charges Paid',
    accountType: 'VslMisc',
    particulars: 'CTM Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 112,
    exchangeRate: 1,
    usdAmount: 112,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 53,
    serial: 52,
    rowType: null,
    fmlId: '52-VOMEME017-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME017',
    oppositeCode: null,
    account: 'Ships Communication Others',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 30,
    exchangeRate: 1,
    usdAmount: 30,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 54,
    serial: 53,
    rowType: null,
    fmlId: '53-VOMEME023-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME023',
    oppositeCode: null,
    account: 'Ships Postage & Courier',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 210,
    exchangeRate: 1,
    usdAmount: 210,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 55,
    serial: 54,
    rowType: null,
    fmlId: '54-VOMEME017-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME017',
    oppositeCode: null,
    account: 'Ships Communication Others',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 30,
    exchangeRate: 1,
    usdAmount: 30,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 56,
    serial: 55,
    rowType: null,
    fmlId: '55-VOMEME023-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME023',
    oppositeCode: null,
    account: 'Ships Postage & Courier',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 51,
    exchangeRate: 1,
    usdAmount: 51,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 57,
    serial: 56,
    rowType: null,
    fmlId: '56-VOMEME023-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME023',
    oppositeCode: null,
    account: 'Ships Postage & Courier',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 200,
    exchangeRate: 1,
    usdAmount: 200,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 58,
    serial: 57,
    rowType: null,
    fmlId: '57-VOMEME025-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME025',
    oppositeCode: null,
    account: 'Ships Sundry Expenses',
    accountType: 'VslMisc',
    particulars:
      'Captain_Cash Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 300,
    exchangeRate: 1,
    usdAmount: 300,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 59,
    serial: 58,
    rowType: null,
    fmlId: '58-VOMEME019-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME019',
    oppositeCode: null,
    account: 'Ships Entertainment Expenses',
    accountType: 'VslMisc',
    particulars:
      'Provision Consumption Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 8,
    exchangeRate: 1,
    usdAmount: 8,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 60,
    serial: 59,
    rowType: null,
    fmlId: '59-VOMEME016-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME016',
    oppositeCode: null,
    account: 'Ships Bank Charges',
    accountType: 'VslMisc',
    particulars:
      'Bank charge for Jan 22 - US$238,026.9 / 386 vessels Invoice# : Bank Charge Allocation 202201 FM_Vessel_Exp_Alloc USD',
    currency: 'USD',
    currencyAmount: 616.65,
    exchangeRate: 1,
    usdAmount: 616.65,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '502A',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 61,
    serial: 60,
    rowType: null,
    fmlId: '60-VOMEME017-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME017',
    oppositeCode: null,
    account: 'Ships Communication Others',
    accountType: 'VslMisc',
    particulars:
      'Communication charge for Jan 22 Invoice# : Bank Charge Allocation 202201 FM_Vessel_Exp_Alloc USD',
    currency: 'USD',
    currencyAmount: 150,
    exchangeRate: 1,
    usdAmount: 150,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '502A',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 62,
    serial: 61,
    rowType: null,
    fmlId: '61-VOMEME024-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME024',
    oppositeCode: null,
    account: 'Ships Printing & Stationery',
    accountType: 'VslMisc',
    particulars:
      'Photocopy charge for Jan 22 - US$9,283.3 / 386 vessels Invoice# : Bank Charge Allocation 202201 FM_Vessel_Exp_Alloc USD',
    currency: 'USD',
    currencyAmount: 24.05,
    exchangeRate: 1,
    usdAmount: 24.05,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '502A',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 63,
    serial: 62,
    rowType: null,
    fmlId: '62-VOMEME018-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEME018',
    oppositeCode: null,
    account: 'Ships Communication Satelite',
    accountType: 'VslMisc',
    particulars: 'Invoice# : ********',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: -158,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.863Z',
    modifiedOn: '2023-05-26T12:35:48.863Z',
    id: 64,
    serial: 63,
    rowType: null,
    fmlId: '63-VOMEAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: 'VOMEAC001',
    oppositeCode: null,
    account: 'Miscellaneous Expenses : Committed',
    accountType: 'VslMisc',
    particulars: '1001_CC_JAN_22_015/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 158,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 65,
    serial: 64,
    rowType: null,
    fmlId: '64-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Miscellaneous',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 68,
    serial: 67,
    rowType: null,
    fmlId: '67-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Pre-Delivery Expenses',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 69,
    serial: 68,
    rowType: null,
    fmlId: '68-VOCHCH004-1001-Jan-22',
    format: 'ACCRUAL',
    section: "Charterer's Entertainment Expenses",
    accountCode: 'VOCHCH004',
    oppositeCode: null,
    account: 'Charterers : Entertainment',
    accountType: 'VslCharterer',
    particulars:
      'Bond Consumption Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 240,
    exchangeRate: 1,
    usdAmount: 240,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 70,
    serial: 69,
    rowType: null,
    fmlId: '69-VOCHCH004-1001-Jan-22',
    format: 'ACCRUAL',
    section: "Charterer's Entertainment Expenses",
    accountCode: 'VOCHCH004',
    oppositeCode: null,
    account: 'Charterers : Entertainment',
    accountType: 'VslCharterer',
    particulars:
      'Bonded stores Consumption Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 205,
    exchangeRate: 1,
    usdAmount: 205,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 71,
    serial: 70,
    rowType: null,
    fmlId: '70-VONBME034-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: 'VONBME034',
    oppositeCode: null,
    account: 'Non-Budget Miscellaneous Expenses',
    accountType: 'VslNonBudget',
    particulars: ' Invoice# : 21-491601',
    currency: 'SGD',
    currencyAmount: 17262,
    exchangeRate: 0.***************,
    usdAmount: 12662.89,
    invoiceNumber: '21-491601',
    invoiceDate: '2021-10-06T18:30:00.000Z',
    serialNumber: '**********',
    poNumber: '131/21',
    originalEntity: '502A',
    invoiceLink:
      'https://fleetmanagement.eye-share.com/#/?doctype=costinvoice&code=502A&id=5dcd4bee-49ce-4b93-95ad-b45b88d3b1a4',
    documentNum: ********,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 72,
    serial: 71,
    rowType: null,
    fmlId: '71-VONBCC009-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: 'VONBCC009',
    oppositeCode: null,
    account: 'Non-Budget Crew Wages',
    accountType: 'VslNonBudget',
    particulars:
      'Captain_Cash Dec 2021 Invoice# : Manisha_PBB-Dec 2021 Adjustment USD',
    currency: 'USD',
    currencyAmount: 2500,
    exchangeRate: 1,
    usdAmount: 2500,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 73,
    serial: 72,
    rowType: null,
    fmlId: '72-VONBCC009-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: 'VONBCC009',
    oppositeCode: null,
    account: 'Non-Budget Crew Wages',
    accountType: 'VslNonBudget',
    particulars:
      'Captain_Cash Jan 2022 Invoice# : Manisha_PBB-Jan 2022 Adjustment USD',
    currency: 'USD',
    currencyAmount: 2000,
    exchangeRate: 1,
    usdAmount: 2000,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: '501B',
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 74,
    serial: 73,
    rowType: null,
    fmlId: '73-VONBME034-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: 'VONBME034',
    oppositeCode: null,
    account: 'Non-Budget Miscellaneous Expenses',
    accountType: 'VslNonBudget',
    particulars: 'Invoice# : 21-491601',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: -8969.47,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 75,
    serial: 74,
    rowType: null,
    fmlId: '74-VONBAC001-1001-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: 'VONBAC001',
    oppositeCode: null,
    account: 'Non Budgeted Expenses : Committed',
    accountType: 'VslNonBudget',
    particulars: '1001_CC_JAN_22_021/22',
    currency: null,
    currencyAmount: 0,
    exchangeRate: 1,
    usdAmount: 8500,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 76,
    serial: 75,
    rowType: null,
    fmlId: '75-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Non-Budget Expenses',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 77,
    serial: 76,
    rowType: null,
    fmlId: '76-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Deductible Claims',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 78,
    serial: 77,
    rowType: null,
    fmlId: '77-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'BWTS',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 79,
    serial: 78,
    rowType: null,
    fmlId: '78-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Scrubbers',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.864Z',
    modifiedOn: '2023-05-26T12:35:48.864Z',
    id: 80,
    serial: 79,
    rowType: null,
    fmlId: '79-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Prior Year',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.865Z',
    modifiedOn: '2023-05-26T12:35:48.865Z',
    id: 81,
    serial: 80,
    rowType: null,
    fmlId: '80-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Projects',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 0,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.865Z',
    modifiedOn: '2023-05-26T12:35:48.865Z',
    id: 82,
    serial: 81,
    rowType: null,
    fmlId: '81-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'Total Expenses',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 213836.6,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.865Z',
    modifiedOn: '2023-05-26T12:35:48.865Z',
    id: 83,
    serial: 82,
    rowType: null,
    fmlId: '82-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'E) Balance of Retained Earnings carried to Next Month',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 298297.52,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-05-26T12:35:48.865Z',
    modifiedOn: '2023-05-26T12:35:48.865Z',
    id: 84,
    serial: 83,
    rowType: null,
    fmlId: '83-null-null-Jan-22',
    format: 'ACCRUAL',
    section: 'G) Estimated Fund Balance',
    accountCode: null,
    oppositeCode: null,
    account: null,
    accountType: null,
    particulars: null,
    currency: null,
    currencyAmount: 0,
    exchangeRate: null,
    usdAmount: 298297.52,
    invoiceNumber: null,
    invoiceDate: null,
    serialNumber: null,
    poNumber: null,
    originalEntity: null,
    invoiceLink: null,
    documentNum: null,
    createdBy: null,
    modifiedBy: null,
    reportDetailId: 1,
    comments: {
      total: 2,
      resolved: 3,
    },
  },
];

export const mockActionableItemsData = {
  hideFromOwner: {
    CFS: ['ASFDHJJHF-1001-MAR-12'],
  },
  disableSuppDoc: {
    CFS: ['ASFDHJJHF-1001-MAR-12', 'WEFADSFAS-5328268-9'],
  },
  validate: {
    TB: ['ASFDHJJHF-1001-MAR-12'],
    CFS: ['ASFDHJJHF-1001-MAR-12'],
  },
};
