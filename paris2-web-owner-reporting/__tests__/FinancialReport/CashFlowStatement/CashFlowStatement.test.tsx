import {
  act,
  fireEvent,
  render,
  screen,
  waitForElementToBeRemoved,
} from '@testing-library/react';
import axios from 'axios';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {CashFlowAccrualStatement} from '../../../src/components';
import {
  DataStoreContext,
  TableActionContextProvider,
} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {
  ChannelType,
  INTEGERS,
  ReportType,
  ReportDetailsStatusEnum,
} from '../../../src/enums';
import {
  mockDocumentsCountList,
  mockViewCommentList,
  reportDetailsOwnerWithStatusOngoing,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {axiosGetMockFunc, setupAxios} from '../../utils/setupAxios';
import {mockCashFlowData} from './data';
import AddComment from '../../../src/components/FinancialReport/TransactionDetails/Comment/AddComment';
import CommentContextProvider from '../../../src/context/CommentContextProvider';

interface IArgs {
  initialEntries?: string[];
  ownerReportingRole?: typeof ownerReportingAllTrue;
  hideFromOwnerFMLId?: string;
}

const wrappedComponent = (args?: IArgs) => {
  const {
    ownerReportingRole = ownerReportingAllTrue,
    hideFromOwnerFMLId = '0-null-null-Jan-22',
  } = args ?? {};

  return (
    <MemoryRouter initialEntries={args?.initialEntries}>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: {
              ...reportDetailsWithStatusOngoing,
              status: ReportDetailsStatusEnum.RE_OPENED,
            },
            ownerPreferences: null,
            actionableLineItem: {
              hideFromOwner: {
                CFS: [hideFromOwnerFMLId],
                BS: [],
                TB: [],
              },

              disableSuppDoc: {
                CFS: ['0-null-null-Jan-22'],
                BS: [],
                TB: [],
              },
              validate: {
                CFS: [],
                TB: [],
                [ReportType.BS]: [],
              },
            },
            reportDetailsList: null,
            setReportDetailListData: null,
          },
          ga4EventTrigger: jest.fn(),
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingRole,
          } as any,
        }}
      >
        <RowSelectContextProvider>
          <TableActionContextProvider>
            <AlertContextProvider>
              <CommentContextProvider
                reportData={mockCashFlowData}
                reportType={ReportType.CFS_ACCRUAL}
              >
                <CashFlowAccrualStatement />
              </CommentContextProvider>
            </AlertContextProvider>
          </TableActionContextProvider>
        </RowSelectContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  );
};

interface IProps {
  draftMessageId?: number;
  hideOwnerFMLId?: string;
  draftComment?: string;
  chanaelTypeReply?: ChannelType;
  usersList?: {
    id: string | number;
    display: string;
  }[];
}
const AddCommentWrappedComponent = (props?: IProps) => {
  const {
    draftMessageId = 2,
    hideOwnerFMLId = '0-null-null-Jan-22',
    draftComment = 'Draft Comment',
    chanaelTypeReply = ChannelType.OWNER,
    usersList = [],
  } = props ?? {};
  return (
    <MemoryRouter>
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: reportDetailsWithStatusOngoing,
            ownerPreferences: null,
            usersList: usersList,
            actionableLineItem: {
              hideFromOwner: {
                CFS: [hideOwnerFMLId],
                BS: [],
              },
              disableSuppDoc: {
                CFS: ['0-null-null-Jan-22'],
                BS: [],
              },
            },
          },
          setDataStore: jest.fn(),
          roleConfig: {
            user: userRoleConfig,
            ownerReporting: ownerReportingAllTrue,
            owner: false,
          } as any,
        }}
      >
        <RowSelectContextProvider>
          <TableActionContextProvider>
            <AlertContextProvider>
              <AddComment
                onMessageSubmit={jest.fn()}
                type={'main'}
                reportType={ReportType.CFS_ACCRUAL}
                fmlId={'FML-0001'}
                draftComment={draftComment}
                parrentComentId={1}
                draftId={draftMessageId}
                draftChannelType={chanaelTypeReply}
                replyChannelType={ChannelType.OWNER}
              />
            </AlertContextProvider>
          </TableActionContextProvider>
        </RowSelectContextProvider>
      </DataStoreContext.Provider>
    </MemoryRouter>
  );
};

describe('CashFlowStatement', () => {
  beforeAll(async () => {
    await setupAxios();
  });

  test('renders CashFlowStatement component without error', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: null}),
    );

    await act(async () => {
      render(wrappedComponent());
    });
  });

  test('throwing error', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.reject({message: 'Error'}),
    );

    await act(async () => {
      render(wrappedComponent());
    });
  });

  test('renders GenericTable component with correct data and options', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockCashFlowData}),
    );

    await act(async () => {
      render(wrappedComponent());
    });

    expect(screen.getByText(mockCashFlowData[0].section)).toBeInTheDocument();

    const secondRow = screen.getByText(mockCashFlowData[1].section);
    expect(secondRow).toBeInTheDocument();

    fireEvent.click(secondRow);

    expect(screen.getAllByText(mockCashFlowData[2].section).length).toBe(1);
  });

  test('Testing The document count sidebar open/close', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('cashflowDocumentCount')[0]);
    });

    await act(async () => {
      fireEvent.keyDown(screen.getAllByTestId('cashflowDocumentCount')[0], {
        key: 'Enter',
      });
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListCloseBtn'));
    });
  });

  test('testing date selection mutate', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce(
      Promise.resolve({data: mockCashFlowData}),
    );

    await act(async () => {
      render(wrappedComponent());
    });

    const [_, secondInputElement] = screen.getAllByRole('textbox');

    const selectedEndDate = new Date(reportDetailsWithStatusOngoing.endDate);

    const selectedDay = selectedEndDate.getDate() - 1;

    await act(async () => {
      await fireEvent.focus(secondInputElement);
      await fireEvent.click(screen.getByText(selectedDay));
    });
  });

  test('testing collapse all expand all functionality', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    expect(screen.getByText('Collapse All')).toBeInTheDocument();
    expect(screen.getAllByText('Account Code')).toHaveLength(1);

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    expect(screen.queryAllByText('Account Code')).toHaveLength(1);
  });

  test('testing scrolling of collapsible container', async () => {
    //mocking request animation frame

    window.requestAnimationFrame = jest
      .fn()
      .mockImplementation(callback => callback());

    // Mocking the scrollWidth property
    Object.defineProperty(HTMLElement.prototype, 'scrollWidth', {
      get() {
        return 200;
      },
      configurable: true,
    });

    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
      get() {
        return 80;
      },
      configurable: true,
    });

    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    await act(async () => {
      fireEvent.scroll(screen.getByTestId('defaultBottomScrollWrapper'), {
        target: {scrollLeft: 50},
      });
    });
  });

  test('testing url query mapping for section selection', async () => {
    Object.defineProperty(window.Element.prototype, 'scrollIntoView', {
      writable: true,
      value: jest.fn(),
    });

    await act(async () => {
      render(
        wrappedComponent({
          initialEntries: [
            '/owner-reporting/783/reports/3194?selectedSection=Crew+%28Wages%29',
          ],
        }),
      );
    });

    expect(
      screen.getByText('A) Balance of Retained Earnings from last month'),
    ).toBeInTheDocument();
  });

  test('testing url query for line item document sidebar', async () => {
    Object.defineProperty(window.Element.prototype, 'scrollIntoView', {
      writable: true,
      value: jest.fn(),
    });

    await act(async () => {
      render(
        wrappedComponent({
          initialEntries: [
            `/owner-reporting/783/reports/3194?fmlId=${mockDocumentsCountList[0].fmlId}`,
          ],
        }),
      );
    });
  });

  test('Testing upload modal wrapper div as null', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const expandCollapseAll = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(expandCollapseAll);
    });

    const allCheckboxes = screen.getAllByRole('checkbox');

    await act(async () => {
      fireEvent.click(allCheckboxes[0]);
    });
  });

  test('testing table column show/hide', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    const toggleButton = screen.getByTestId('tableColumnShowHideBtn');

    await act(async () => {
      fireEvent.click(toggleButton);
    });

    const selectFirstItem = screen.getAllByTestId(
      'tableColumnShowHideToggleItem',
    )[0];
    const selectedItemText = selectFirstItem.textContent!;

    await act(async () => {
      fireEvent.click(selectFirstItem);
    });

    expect(screen.queryAllByText(selectedItemText)).toHaveLength(1);

    await act(async () => {
      fireEvent.click(selectFirstItem);
    });

    expect(screen.getAllByText(selectedItemText)).toHaveLength(2);
  });

  test('Testing Manual Refresh', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Refresh'));
    });
  });

  test('Covering Manual Refresh Error case', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    (axios.post as jest.Mock).mockImplementationOnce((url: string) => {
      const err: any = new Error();
      err.response = {status: 400};
      throw err;
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Refresh'));
    });

    (axios.post as jest.Mock).mockImplementationOnce((url: string) => {
      const err: any = new Error();
      err.response = {status: INTEGERS.FourHundredNine};
      throw err;
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Refresh'));
    });
  });

  test('handle Hide From Owner Checkbox in CashFlow', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    expect(
      screen.getAllByTestId('cashFlowStatementHideFromOwnerCheckbox')[0],
    ).toBeInTheDocument();

    fireEvent.click(
      screen.getAllByTestId('cashFlowStatementHideFromOwnerCheckbox')[0],
    );

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: {message: 'success'},
        status: 200,
      });
    });
  });

  test('Error while creating actionable Items for Hide from owner type checkbox', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    expect(
      screen.getAllByTestId('cashFlowStatementHideFromOwnerCheckbox')[0],
    ).toBeInTheDocument();

    fireEvent.click(
      screen.getAllByTestId('cashFlowStatementHideFromOwnerCheckbox')[0],
    );

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.reject({});
    });
  });

  test('handle Supporting Doc Checkbox in CashFlow', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const collapseExpandAllEle = screen.getByText('Expand All');

    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });

    expect(
      screen.getAllByTestId('cashFlowStatementDisableSuppDocCheckbox')[0],
    ).toBeInTheDocument();

    fireEvent.click(
      screen.getAllByTestId('cashFlowStatementDisableSuppDocCheckbox')[0],
    );
  });
  test('handle Selecting All line items of sub section Checkbox in CashFlow', async () => {
    await act(async () => {
      render(wrappedComponent());
    });
    const collapseExpandAllEle = screen.getByText('Expand All');
    fireEvent.click(collapseExpandAllEle);
    expect(screen.getAllByTestId('selectAllCheckbox')[0]).toBeInTheDocument();
    expect(
      screen.getAllByTestId('cashFlowStatementCheckbox')[0],
    ).toBeInTheDocument();
    fireEvent.click(screen.getAllByTestId('selectAllCheckbox')[0]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[0]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[1]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[2]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[3]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[8]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[9]);
    fireEvent.click(screen.getAllByTestId('cashFlowStatementCheckbox')[10]);
    fireEvent.click(screen.getAllByTestId('selectAllCheckbox')[0]);
    fireEvent.click(screen.getAllByTestId('selectAllCheckbox')[0]);
  });
});

//testing actions
describe('Testing TransactionDetails', () => {
  test('Testing Transaction Details Rendering', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    expect(screen.queryByText('Transaction Details')).toBeInTheDocument();
    expect(screen.queryByText('All Comments')).toBeInTheDocument();

    await act(async () => {
      fireEvent.click(screen.getByTestId('transactionDetailsClose'));
    });
  });

  test('Testing Transaction Details Flow', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('commentCard')[0]);
    });
  });

  test('Testing Transaction Without having comment access', async () => {
    await act(async () => {
      render(
        wrappedComponent({
          ownerReportingRole: {...ownerReportingAllTrue, canComment: false},
        }),
      );
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    expect(screen.queryAllByTestId('commentCard')).toHaveLength(0);
  });

  test('Testing Transaction Details Comments', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: 'Hi @'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });

    //changing channel type
    await act(async () => {
      fireEvent.click(screen.getByTestId('addCommentChannelType'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('addCommentOwner'));
    });

    //will throw an error
    await act(async () => {
      fireEvent.click(screen.getByTestId('commentInputSend'));
    });

    //changing channel type
    await act(async () => {
      fireEvent.click(screen.getByTestId('addCommentChannelType'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('addCommentInternal'));
    });

    await act(async () => {
      fireEvent.change(screen.getByTestId('mentionInputEnable'), {
        target: {value: 'Hi @'},
      });
    });

    (axios.get as jest.Mock).mockImplementationOnce(() => Promise.resolve({}));
    await act(async () => {
      fireEvent.click(screen.getByTestId('commentInputSend'));
    });
  });

  test('Testing Resolved comment with an error', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    (axios.put as jest.Mock).mockImplementationOnce(() => Promise.reject({}));

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('replyThreadResolveBtn')[0]);
    });
  });

  test('Testing ReplyThread', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    //covering adding reply
    Element.prototype.scrollIntoView = jest.fn();

    const lastCommentCard = screen.getAllByTestId('commentCard').at(-1)!;

    await act(async () => {
      fireEvent.click(lastCommentCard);
    });

    const textMessage = 'Message';

    await act(async () => {
      fireEvent.change(screen.getByTestId('mentionInputReplyEnable'), {
        target: {value: textMessage},
      });
    });

    (axios.get as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        data: [
          {
            ...mockViewCommentList[0],
            replies: [
              ...mockViewCommentList[0].replies,
              {
                ...mockViewCommentList[0].replies.at(-1),
                id: 23423,
                message: textMessage,
              },
            ],
          },

          ...mockViewCommentList.slice(1),
        ],
      }),
    ); //adding the newly created reply

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentInputReplySend'));
    });

    await act(async () => {
      fireEvent.click(lastCommentCard);
    });

    expect(screen.queryByDisplayValue(textMessage)).toBeInTheDocument(); //verifying if the reply is in the ui or not

    //covering error case
    await act(async () => {
      fireEvent.click(lastCommentCard);
    });
    await act(async () => {
      fireEvent.change(screen.getByTestId('mentionInputReplyEnable'), {
        target: {value: 'error'},
      });
    });

    (axios.post as jest.Mock).mockImplementationOnce(() => Promise.reject({}));

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentInputReplySend'));
    });

    //covering replies view toggle btn
    await act(async () => {
      fireEvent.click(screen.getAllByTestId('replyThreadToggleBtn').at(-1)!);
    });

    //covering the switch between selected CommentCard

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('commentCard')[0]);
    });

    //covering comment resolve

    const resolveBtn = screen.getAllByTestId('replyThreadResolveBtn')[0];

    (axios.get as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        data: [
          {
            ...mockViewCommentList[0],
            status: 1,
          },

          ...mockViewCommentList.slice(1),
        ],
      }),
    );

    await act(async () => {
      fireEvent.click(resolveBtn);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('resolveBtnModalCancelBtn'));
      fireEvent.click(resolveBtn);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('resolveBtnModalDeleteBtn'));
    });

    expect(screen.queryAllByTestId('commentCardDisabled')).toHaveLength(2);

    //covering error cases

    (axios.patch as jest.Mock).mockImplementationOnce(() => Promise.reject({}));

    await act(async () => {
      fireEvent.click(resolveBtn);
    });
    await act(async () => {
      fireEvent.click(screen.getByTestId('resolveBtnModalDeleteBtn'));
    });
  });

  test('Testing Transaction Details Comments as Owner', async () => {
    (axios.get as jest.Mock).mockImplementation(url => {
      if (url.includes('/reports'))
        return {data: reportDetailsOwnerWithStatusOngoing};

      return axiosGetMockFunc(url);
    });

    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    await act(async () => {
      fireEvent.change(
        screen.getByTestId('mentionInputEnable', {value: 'Hi @'} as any),
      );
    });

    const sendBtn = screen.getByTestId('commentInputSend');

    expect(sendBtn).not.toHaveAttribute('disabled', 'true');

    await act(async () => {
      fireEvent.click(sendBtn);
    });

    //covering the error case

    (axios.post as jest.Mock).mockImplementationOnce(() => Promise.reject({}));

    await act(async () => {
      fireEvent.change(screen.getByTestId('mentionInputEnable'), {
        target: {value: 'error'},
      });
    });

    expect(sendBtn).not.toHaveAttribute('disabled', 'true');

    await act(async () => {
      fireEvent.click(sendBtn);
    });
  });
});

describe('Testing Comment Card Kebab Menu', () => {
  beforeEach(async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    if (screen.queryByTestId('defaultLoader'))
      await waitForElementToBeRemoved(screen.getByTestId('defaultLoader'));

    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('commentCardKebabMenuBtn')[0]);
    });
  });

  test('Testing Modify comment with successful response', async () => {
    const modifyBtn = screen.getByTestId('modifyCommentBtn');

    expect(modifyBtn).not.toHaveAttribute('disabled');

    await act(async () => {
      fireEvent.click(modifyBtn);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentUpdateChannelTypeDropdown'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentUpdateChannelTypeInternal'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentUpdateChannelTypeDropdown'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('commentUpdateChannelTypeOwner'));
    });

    const commentInput = screen
      .getByTestId('commentMessageContainer')
      .querySelector('[data-testid=mentionInputEnable]');

    if (commentInput)
      await act(async () => {
        fireEvent.change(commentInput, {
          target: {value: 'Hi Message @'},
        });

        fireEvent.focus(commentInput);
      });

    await act(async () => {
      fireEvent.click(screen.getByTestId('updateCommentBtn'));
    });
  });

  test('Testing Modify comment with error', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('modifyCommentBtn'));
    });

    (axios.patch as jest.Mock).mockImplementationOnce(() => Promise.reject({}));

    await act(async () => {
      fireEvent.click(screen.getByTestId('updateCommentBtn'));
    });
  });

  test('Covering other case for update comment modal', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('modifyCommentBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('closeCommentUpdateModalBtn'));
    });
  });

  test('Testing Delete comment with success response', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('deleteCommentBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('deleteCommentModalBtn'));
    });
  });

  test('Testing Delete comment with error response', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('deleteCommentBtn'));
    });

    (axios.delete as jest.Mock).mockImplementationOnce(() =>
      Promise.reject({}),
    );

    await act(async () => {
      fireEvent.click(screen.getByTestId('deleteCommentModalBtn'));
    });
  });

  test('Covering other case for delete comment modal', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('deleteCommentBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('closeCommentDeleteModalBtn'));
    });
  });
});

describe('Test Add Comment', () => {
  test('Render Component', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent({hideOwnerFMLId: 'FML-0001'}));
    });
    expect(screen.getByTestId('add-comment')).toBeInTheDocument();
  });

  test('Change Chanel Type', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent());
    });
    fireEvent.click(screen.getByTestId('addCommentChannelType'));

    fireEvent.click(screen.getByTestId('addCommentInternal'));

    // open modal
    expect(screen.getByTestId('change-modal')).toBeInTheDocument();

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: 'Hi Zack'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });

    // click yes btn
    fireEvent.click(screen.getByTestId('change-chanel-type'));
  });

  test('Comments on input', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent());
    });

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: 'Hi Zack'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });

    await new Promise(r => setTimeout(r, 300));

    fireEvent.click(screen.getByTestId('addCommentChannelType'));
    fireEvent.click(screen.getByTestId('addCommentInternal'));

    fireEvent.click(screen.getByTestId('commentInputSend'));
  });

  test('without draft messgae id', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent({draftMessageId: 0}));
    });
    fireEvent.click(screen.getByTestId('addCommentChannelType'));
    fireEvent.click(screen.getByTestId('addCommentInternal'));

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: 'Hi Zack'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });

    (axios.post as jest.Mock).mockImplementationOnce(() => {
      return Promise.resolve({
        data: {id: '001'},
        status: 200,
      });
    });

    (axios.patch as jest.Mock).mockImplementationOnce({message: 'success'});
  });

  test('Click no when popup to change owner modal opens', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent());
    });
    fireEvent.click(screen.getByTestId('addCommentChannelType'));

    fireEvent.click(screen.getByTestId('addCommentInternal'));
    // open modal
    expect(screen.getByTestId('change-modal')).toBeInTheDocument();
    // click yes btn
    fireEvent.click(screen.getByTestId('change-chanel-no-btn'));
  });

  test('Throw Error while updating draft comments', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent({draftMessageId: 0}));
    });
    fireEvent.click(screen.getByTestId('addCommentChannelType'));
    fireEvent.click(screen.getByTestId('addCommentInternal'));

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: 'Hi Zack'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });

    const mockAxiosPostCallWith409Status = jest.fn(async () => {
      const error = new Error('Message');
      (error as any).response = {
        status: 409,
        data: {error: {message: ''}},
      };

      throw error;
    });

    (axios.patch as jest.Mock).mockImplementationOnce(
      mockAxiosPostCallWith409Status,
    );
  });

  test('Delete Draft Comment', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent());
    });
    await act(async () => {
      fireEvent.click(screen.getByTestId('commentInputSend'));
    });

    (axios.delete as jest.Mock).mockImplementationOnce(() => {
      throw new Error('Unable to delete a draft comment');
    });
  });

  test('open channel change popup and select Owner', async () => {
    await act(async () => {
      render(AddCommentWrappedComponent());
    });
    fireEvent.click(screen.getByTestId('addCommentChannelType'));

    fireEvent.click(screen.getByTestId('addCommentOwner'));

    expect(screen.getByTestId('addCommentChannelType')).toHaveTextContent(
      'owner',
    );
  });

  test('Mention Input', async () => {
    await act(async () => {
      render(
        AddCommentWrappedComponent({
          chanaelTypeReply: ChannelType.INTERNAL,
          draftComment: '@ash',
          usersList: [
            {
              id: 1,
              display: 'ash',
            },
            {
              id: 2,
              display: 'Daniel',
            },
          ],
        }),
      );
    });

    const commentBoxEle = screen.getByTestId('mentionInputEnable');

    await act(async () => {
      fireEvent.change(commentBoxEle, {
        target: {value: '@ash'},
      });
      commentBoxEle.dispatchEvent(new Event('change'));
    });
  });
});
