import {OperatingCostNode} from '../../../src/types';
import {operatingCostSheetHydration} from '../../../src/components/FinancialReport/OperatingCost/utils';
import {mockOCReportDataNewFormat, mockOperatingCostData} from './data';

describe('operatingCostSheetHydration', () => {
  it('should return an empty array when operatingCostData is empty', () => {
    const result = operatingCostSheetHydration([], false);
    expect(result).toEqual([]);
  });

  it('should process data correctly for the old version', () => {
    const result = operatingCostSheetHydration(mockOperatingCostData, false);

    // Ensure the result is defined and has the expected length
    expect(result).toBeDefined();
    expect(result).toHaveLength(5);

    // Validate the "Revenue" section
    const revenue = result.find(item => item.node.section === 'Receipts');
    expect(revenue).toBeDefined();
    expect(revenue.node.periodAmount).toBe(null);
    expect(revenue.node.yearToDate).toBe(null);
    const totalExpenditure = result.find(
      item => item.node.section === 'Total Expenditure',
    );
    expect(totalExpenditure).toBeDefined();
    expect(totalExpenditure.node.periodAmount).toBe(163781.94);
    expect(totalExpenditure.node.yearToDate).toBe(967515.1600000003);

    // Validate the "Expenditures" section
    const expenditures = result.find(
      item => item.node.section === 'Expenditure',
    );
    expect(expenditures).toBeDefined();
    expect(expenditures.children).toHaveLength(12);
  });

  it('should process data correctly for the new version', () => {
    const result = operatingCostSheetHydration(mockOCReportDataNewFormat, true);
    expect(result).toBeDefined();
    expect(result).toHaveLength(3);

    const revenue = result.find(item => item.node.section === 'Fund Receipts');
    expect(revenue).toBeDefined();
    expect(revenue.node.periodAmount).toBe(0);
    expect(revenue.node.yearToDate).toBe(428455.97000000003);

    const expenditures = result.find(
      item => item.node.section === 'Expenditures',
    );
    expect(expenditures).toBeDefined();
    expect(expenditures.children).toHaveLength(8);

    const surplusDeficit = result.find(
      item => item.node.section === 'Surplus / (Deficit)',
    );
    expect(surplusDeficit).toBeDefined();
    expect(surplusDeficit.node.periodAmount).toBe(-9550.4);
    expect(surplusDeficit.node.yearToDate).toBe(192865.75);
  });
});
