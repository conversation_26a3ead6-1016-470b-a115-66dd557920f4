import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {OperatingCost} from '../../../src/components/FinancialReport/OperatingCost';
import MemoCommentCount from '../../../src/components/icons/CommentCount';
import {TableActionContextProvider} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import DataStoreProvider, {
  DataStoreContext,
} from '../../../src/context/DataStoreProvider';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {FinancialReport} from '../../../src/pages/FinancialReport/FinancialReport.page';
import {
  Down<PERSON>rrow,
  DownSortArrow,
  History,
  SortIcon,
  UpArrow,
  UpSortArrow,
} from '../../../src/utils/svgIcons';
import {
  ownerReportingAllTrue,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
  vesselAccountantRoleConfig,
} from '../../data';
import {setupAxios} from '../../utils';
import CommentContextProvider from '../../../src/context/CommentContextProvider';
import {ReportType} from '../../../src/enums';
import {mockOCReportDataNewFormat, mockOperatingCostData} from './data';

interface IArgs {
  ownerReportingRole?: typeof ownerReportingAllTrue;
  initialEntries?: string;
}
const wrappedComponent2 = (args?: IArgs) => {
  const {ownerReportingRole = ownerReportingAllTrue} = args ?? {};

  return (
    <MemoryRouter
      initialEntries={['/owner-reporting/783/reports/2996/?activeTab=3']}
    >
      <AlertContextProvider>
        <DataStoreProvider
          dataStore={{
            reportDetails: {
              ...reportDetailsWithStatusOngoing,
              reportFormatVersion: 2,
            },
            reportVersion: 2,
            filters: 'test',
          }}
          roleConfig={{
            user: userRoleConfig,
            ownerReporting: ownerReportingRole,
          }}
          ga4EventTrigger={jest.fn()}
        >
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockOCReportDataNewFormat}
                reportType={ReportType.OC_V2}
              >
                <OperatingCost />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </DataStoreProvider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};

const wrappedComponent = (args?: IArgs) => {
  const {ownerReportingRole = ownerReportingAllTrue} = args ?? {};

  return (
    <MemoryRouter
      initialEntries={['/owner-reporting/783/reports/2996/?activeTab=3']}
    >
      <AlertContextProvider>
        <DataStoreProvider
          roleConfig={{
            user: userRoleConfig,
            ownerReporting: ownerReportingRole,
          }}
          ga4EventTrigger={jest.fn()}
        >
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockOperatingCostData}
                reportType={ReportType.OC}
              >
                <FinancialReport />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </DataStoreProvider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};
const operatingCostComponent = (args?: IArgs) => {
  const {initialEntries = '/owner-reporting/783/reports/2996/?activeTab=3'} =
    args ?? {};

  return (
    <MemoryRouter initialEntries={[initialEntries]}>
      <AlertContextProvider>
        <DataStoreContext.Provider
          value={{
            dataStore: {
              reportDetails: reportDetailsWithStatusOngoing,
              ownerPreferences: null,
            },
            ga4EventTrigger: jest.fn(),
            setDataStore: jest.fn(),
            roleConfig: {
              user: vesselAccountantRoleConfig,
              ownerReporting: ownerReportingAllTrue,
            },
          }}
        >
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <CommentContextProvider
                reportData={mockOperatingCostData}
                reportType={ReportType.OC}
              >
                <OperatingCost />
              </CommentContextProvider>
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </DataStoreContext.Provider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};

describe('OperatingCost', () => {
  beforeEach(async () => {
    await setupAxios();

    await act(async () => {
      render(wrappedComponent());
    });

    await waitFor(() => {
      expect(screen.getByText('Expand All')).toBeInTheDocument();
    });
  });

  test('Rendering OperatingCost With Expand All', async () => {
    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });
  });

  test('Opening OperatingCost TransactionDetails', async () => {
    await act(async () => {
      fireEvent.click(screen.getByText('Expand All'));
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
    });
  });
});

describe('Icons Component', () => {
  test('Render Comment Count', async () => {
    await act(async () => {
      render(<MemoCommentCount />);
    });
  });
  test('Render Sort Icon', async () => {
    await act(async () => {
      render(<SortIcon />);
    });
  });

  test('History Sort Icon', async () => {
    await act(async () => {
      render(<History />);
    });
  });

  test('DownSortArrow  Icon', async () => {
    await act(async () => {
      render(<DownSortArrow />);
    });
  });

  test('UpArrow  Icon', async () => {
    await act(async () => {
      render(<UpArrow />);
    });
  });

  test('DownArrow  Icon', async () => {
    await act(async () => {
      render(<DownArrow />);
    });
  });

  test('UpSortArrow  Icon', async () => {
    await act(async () => {
      render(<UpSortArrow />);
    });
  });
});

describe('Operating Cost Column', () => {
  beforeEach(async () => {
    setupAxios();
    await act(async () => {
      render(operatingCostComponent());
    });
  });

  test('Navigate to child section when Name Column is clicked & Open transaction details modal', () => {
    const nameColumn = screen.getAllByTestId('operating-cost-name-column')[0];
    fireEvent.click(nameColumn);
    fireEvent.click(screen.getAllByTestId('addCommentBtn')[0]);
  });

  test('Open Drawer when enter key is pressed', async () => {
    render(operatingCostComponent());

    const nameColumn = screen.getAllByTestId('operating-cost-name-column')[0];

    await act(async () => {
      fireEvent.keyDown(nameColumn, {
        key: 'Enter',
      });
    });
  });
  test('testing report with new format ', async () => {
    await act(async () => {
      render(wrappedComponent2());
    });

    await waitFor(() => {
      expect(screen.getAllByText('Expand All')[0]).toBeInTheDocument();
    });
    await act(async () => {
      fireEvent.click(screen.getAllByText('Expand All')[0]);
    });
  });
});
