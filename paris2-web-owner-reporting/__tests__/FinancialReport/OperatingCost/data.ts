import {ReportType} from '../../../src/enums';

export const mockOperatingCostData = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.038Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160856,
    serial: 0,
    fmlId: '1001Aug-23*Receipts',
    section: '*Receipts',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
    mappedAccountType: 'Crew (Wages)',
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160857,
    serial: 0,
    fmlId: '1001Aug-23VBLIOWN01',
    section: '*Receipts',
    accountCode: 'VBLIOWN01',
    accountDescription: 'Operating Funds',
    periodAmount: 0,
    yearToDate: 610173,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160858,
    serial: 2,
    fmlId: '1001Aug-23VBLIOWN03',
    section: '*Receipts',
    accountCode: 'VBLIOWN03',
    accountDescription: 'Non-Budget Funds',
    periodAmount: 0,
    yearToDate: 71116.***********,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160859,
    serial: 3,
    fmlId: '1001Aug-23VBLIOWN04',
    section: '*Receipts',
    accountCode: 'VBLIOWN04',
    accountDescription: 'Dry Dock Funds',
    periodAmount: 0,
    yearToDate: 103143.77,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160860,
    serial: 4,
    fmlId: '1001Aug-23VBLIOWN05',
    section: '*Receipts',
    accountCode: 'VBLIOWN05',
    accountDescription: "Charterer's Funds",
    periodAmount: 0,
    yearToDate: 106972.57,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160861,
    serial: 5,
    fmlId: '1001Aug-23**Total-0',
    section: '**Total-0',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '0',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160862,
    serial: 6,
    fmlId: '1001Aug-23*Expenditure',
    section: '*Expenditure',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160863,
    serial: 48,
    fmlId: '1001Aug-23*Crew Fixed Wages & Allowances',
    section: '*Crew Fixed Wages & Allowances',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.48',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160864,
    serial: 48,
    fmlId: '1001Aug-23VOCCFC001',
    section: '*Crew Fixed Wages & Allowances',
    accountCode: 'VOCCFC001',
    accountDescription: 'Fixed Crew Wages',
    periodAmount: 98333,
    yearToDate: 589998,
    transactionDate: '2023-08-01',
    sectionGroup: '6.48',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160865,
    serial: 49,
    fmlId: '1001Aug-23**Total-6.48',
    section: '**Total-6.48',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.48',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160866,
    serial: 50,
    fmlId: '1001Aug-23*Consumables Stores',
    section: '*Consumables Stores',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160867,
    serial: 53,
    fmlId: '1001Aug-23VOSTCA006',
    section: '*Consumables Stores',
    accountCode: 'VOSTCA006',
    accountDescription: 'Stores : Medicines',
    periodAmount: 0,
    yearToDate: 1025.04,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160868,
    serial: 55,
    fmlId: '1001Aug-23VOSTCE001',
    section: '*Consumables Stores',
    accountCode: 'VOSTCE001',
    accountDescription: 'Stores : COVID Supplies',
    periodAmount: 1750,
    yearToDate: 2041,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160869,
    serial: 56,
    fmlId: '1001Aug-23VOSTCG002',
    section: '*Consumables Stores',
    accountCode: 'VOSTCG002',
    accountDescription: 'Stores : Chemicals Tanks & Holds Cleaning',
    periodAmount: 0,
    yearToDate: 2839,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160870,
    serial: 57,
    fmlId: '1001Aug-23VOSTDK001',
    section: '*Consumables Stores',
    accountCode: 'VOSTDK001',
    accountDescription: 'Stores : Charts & Publications',
    periodAmount: 3256.84,
    yearToDate: 6316.84,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160871,
    serial: 59,
    fmlId: '1001Aug-23VOSTDK003',
    section: '*Consumables Stores',
    accountCode: 'VOSTDK003',
    accountDescription: 'Stores : Deck Maintenance',
    periodAmount: 1807.57,
    yearToDate: 1807.57,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160872,
    serial: 62,
    fmlId: '1001Aug-23VOSTDK009',
    section: '*Consumables Stores',
    accountCode: 'VOSTDK009',
    accountDescription: 'Stores : Mooring Ropes',
    periodAmount: 4550,
    yearToDate: 4550,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160873,
    serial: 64,
    fmlId: '1001Aug-23VOSTDK011',
    section: '*Consumables Stores',
    accountCode: 'VOSTDK011',
    accountDescription: 'Stores : Deck Paints',
    periodAmount: 4896,
    yearToDate: 7234.63,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160874,
    serial: 65,
    fmlId: '1001Aug-23VOSTEL001',
    section: '*Consumables Stores',
    accountCode: 'VOSTEL001',
    accountDescription: 'Stores : Electrical Equipment',
    periodAmount: 533.41,
    yearToDate: 1007.02,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160875,
    serial: 67,
    fmlId: '1001Aug-23VOSTER002',
    section: '*Consumables Stores',
    accountCode: 'VOSTER002',
    accountDescription: 'Stores : Engine Maintenance Equipment',
    periodAmount: 2842.06,
    yearToDate: 2960.16,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160876,
    serial: 69,
    fmlId: '1001Aug-23VOSTER005',
    section: '*Consumables Stores',
    accountCode: 'VOSTER005',
    accountDescription: 'Stores : Gases',
    periodAmount: 0,
    yearToDate: 470.44,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160877,
    serial: 71,
    fmlId: '1001Aug-23VOSTOE002',
    section: '*Consumables Stores',
    accountCode: 'VOSTOE002',
    accountDescription: 'Stores : Fire Fighting Applicances',
    periodAmount: 243.**************,
    yearToDate: 243.**************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160878,
    serial: 72,
    fmlId: '1001Aug-23VOSTOE003',
    section: '*Consumables Stores',
    accountCode: 'VOSTOE003',
    accountDescription: 'Stores : It Equipment & Stationery',
    periodAmount: 0,
    yearToDate: 533.61,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160879,
    serial: 73,
    fmlId: '1001Aug-23VOSTOE004',
    section: '*Consumables Stores',
    accountCode: 'VOSTOE004',
    accountDescription: 'Stores : Life Saving Appliances',
    periodAmount: 350.36,
    yearToDate: 672.*************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160880,
    serial: 74,
    fmlId: '1001Aug-23VOSTOE005',
    section: '*Consumables Stores',
    accountCode: 'VOSTOE005',
    accountDescription: 'Stores : Navigation Equipment',
    periodAmount: 0,
    yearToDate: 53.68,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160881,
    serial: 75,
    fmlId: '1001Aug-23VOSTOT001',
    section: '*Consumables Stores',
    accountCode: 'VOSTOT001',
    accountDescription: 'Stores : Delivery Expenses',
    periodAmount: 608.5,
    yearToDate: 645.02,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160882,
    serial: 76,
    fmlId: '1001Aug-23VOSTOT002',
    section: '*Consumables Stores',
    accountCode: 'VOSTOT002',
    accountDescription: 'Stores : Others',
    periodAmount: 12407.67,
    yearToDate: 25577.89,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.039Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160883,
    serial: 79,
    fmlId: '1001Aug-23VOSTAC001',
    section: '*Consumables Stores',
    accountCode: 'VOSTAC001',
    accountDescription: 'Stores : Committed',
    periodAmount: 0,
    yearToDate: 48234.21,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160884,
    serial: 80,
    fmlId: '1001Aug-23*Total Consumables Stores',
    section: '*Total Consumables Stores',
    accountCode: null,
    accountDescription: null,
    periodAmount: 33245.61,
    yearToDate: 106211.99,
    transactionDate: '2023-08-01',
    sectionGroup: '6.50',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160885,
    serial: 82,
    fmlId: '1001Aug-23*Spare Parts',
    section: '*Spare Parts',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160886,
    serial: 83,
    fmlId: '1001Aug-23VOSPCA009',
    section: '*Spare Parts',
    accountCode: 'VOSPCA009',
    accountDescription: 'Spares : Galley & Pantry',
    periodAmount: 0,
    yearToDate: 432,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160887,
    serial: 84,
    fmlId: '1001Aug-23VOSPCG003',
    section: '*Spare Parts',
    accountCode: 'VOSPCG003',
    accountDescription: 'Spares : Cargo Gear',
    periodAmount: 0,
    yearToDate: 1687,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160888,
    serial: 87,
    fmlId: '1001Aug-23VOSPDK013',
    section: '*Spare Parts',
    accountCode: 'VOSPDK013',
    accountDescription: 'Spares : Deck Hydraulic Equipment',
    periodAmount: 190,
    yearToDate: 190,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160889,
    serial: 89,
    fmlId: '1001Aug-23VOSPER006',
    section: '*Spare Parts',
    accountCode: 'VOSPER006',
    accountDescription: 'Spares : Auxiliary Engine',
    periodAmount: 970.25,
    yearToDate: 970.25,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160890,
    serial: 92,
    fmlId: '1001Aug-23VOSPER010',
    section: '*Spare Parts',
    accountCode: 'VOSPER010',
    accountDescription: 'Spares : Compressed Air System',
    periodAmount: 0,
    yearToDate: 571.9,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160891,
    serial: 94,
    fmlId: '1001Aug-23VOSPER015',
    section: '*Spare Parts',
    accountCode: 'VOSPER015',
    accountDescription: 'Spares : Fresh Water System',
    periodAmount: 0,
    yearToDate: 700,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160892,
    serial: 95,
    fmlId: '1001Aug-23VOSPER017',
    section: '*Spare Parts',
    accountCode: 'VOSPER017',
    accountDescription: 'Spares : Fuel Oil Pumps',
    periodAmount: 90,
    yearToDate: 90,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160893,
    serial: 97,
    fmlId: '1001Aug-23VOSPER022',
    section: '*Spare Parts',
    accountCode: 'VOSPER022',
    accountDescription: 'Spares : Lube Oil Purifiers',
    periodAmount: 0,
    yearToDate: 90,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160894,
    serial: 98,
    fmlId: '1001Aug-23VOSPER023',
    section: '*Spare Parts',
    accountCode: 'VOSPER023',
    accountDescription: 'Spares : Main Engine',
    periodAmount: 710,
    yearToDate: 710,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160895,
    serial: 100,
    fmlId: '1001Aug-23VOSPOE009',
    section: '*Spare Parts',
    accountCode: 'VOSPOE009',
    accountDescription: 'Spares : Fire Fighting Applicances',
    periodAmount: 300,
    yearToDate: 300,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160896,
    serial: 104,
    fmlId: '1001Aug-23VOSPOE013',
    section: '*Spare Parts',
    accountCode: 'VOSPOE013',
    accountDescription: 'Spares : Radar',
    periodAmount: 1990,
    yearToDate: 1990,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160897,
    serial: 105,
    fmlId: '1001Aug-23VOSPOT004',
    section: '*Spare Parts',
    accountCode: 'VOSPOT004',
    accountDescription: 'Spares : Delivery Expenses',
    periodAmount: 2264.52,
    yearToDate: 3341.96,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160898,
    serial: 106,
    fmlId: '1001Aug-23VOSPOT005',
    section: '*Spare Parts',
    accountCode: 'VOSPOT005',
    accountDescription: 'Spares : Others',
    periodAmount: 4931.85,
    yearToDate: 15731.85,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160899,
    serial: 108,
    fmlId: '1001Aug-23VOSPAC001',
    section: '*Spare Parts',
    accountCode: 'VOSPAC001',
    accountDescription: 'Spare Parts : Committed',
    periodAmount: 0,
    yearToDate: 19895.09,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160900,
    serial: 109,
    fmlId: '1001Aug-23*Total Spare Parts',
    section: '*Total Spare Parts',
    accountCode: null,
    accountDescription: null,
    periodAmount: 11446.62,
    yearToDate: 46700.05,
    transactionDate: '2023-08-01',
    sectionGroup: '6.82',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160901,
    serial: 111,
    fmlId: '1001Aug-23*Repair and Maintenance',
    section: '*Repair and Maintenance',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160902,
    serial: 111,
    fmlId: '1001Aug-23VORMDK016',
    section: '*Repair and Maintenance',
    accountCode: 'VORMDK016',
    accountDescription: 'R&M : Bilge & Ballast System',
    periodAmount: 0,
    yearToDate: 1285,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160903,
    serial: 118,
    fmlId: '1001Aug-23VORMOE016',
    section: '*Repair and Maintenance',
    accountCode: 'VORMOE016',
    accountDescription: 'R&M : It Equipment',
    periodAmount: 0,
    yearToDate: 2650,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160904,
    serial: 120,
    fmlId: '1001Aug-23VORMOE020',
    section: '*Repair and Maintenance',
    accountCode: 'VORMOE020',
    accountDescription: 'R&M : Navigational Equipments',
    periodAmount: 280,
    yearToDate: 280,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160905,
    serial: 122,
    fmlId: '1001Aug-23VORMOT006',
    section: '*Repair and Maintenance',
    accountCode: 'VORMOT006',
    accountDescription: 'R&M : Others',
    periodAmount: 718.37,
    yearToDate: 23311.54,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160906,
    serial: 123,
    fmlId: '1001Aug-23**Total-6.111',
    section: '**Total-6.111',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.111',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160907,
    serial: 124,
    fmlId: '1001Aug-23*Survey',
    section: '*Survey',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.124',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160908,
    serial: 125,
    fmlId: '1001Aug-23VORMSF002',
    section: '*Survey',
    accountCode: 'VORMSF002',
    accountDescription: 'R&M : Survey Class',
    periodAmount: 0,
    yearToDate: 4198.89,
    transactionDate: '2023-08-01',
    sectionGroup: '6.124',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160909,
    serial: 126,
    fmlId: '1001Aug-23VORMSF003',
    section: '*Survey',
    accountCode: 'VORMSF003',
    accountDescription: 'R&M : Survey Others',
    periodAmount: 0,
    yearToDate: 3947.52,
    transactionDate: '2023-08-01',
    sectionGroup: '6.124',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160910,
    serial: 128,
    fmlId: '1001Aug-23VORMAC001',
    section: '*Survey',
    accountCode: 'VORMAC001',
    accountDescription: 'Repair & Maintenance : Committed',
    periodAmount: 0,
    yearToDate: -14563.35,
    transactionDate: '2023-08-01',
    sectionGroup: '6.124',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160911,
    serial: 129,
    fmlId: '1001Aug-23*Total Repair and Maintenance and Survey',
    section: '*Total Repair and Maintenance and Survey',
    accountCode: null,
    accountDescription: null,
    periodAmount: 998.37,
    yearToDate: 21109.************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.124',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160912,
    serial: 131,
    fmlId: '1001Aug-23*Lubricating Oil',
    section: '*Lubricating Oil',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160913,
    serial: 132,
    fmlId: '1001Aug-23VOLULU002',
    section: '*Lubricating Oil',
    accountCode: 'VOLULU002',
    accountDescription: 'Lubricating Oil (A/E)',
    periodAmount: 1325.24,
    yearToDate: 3136.*************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160914,
    serial: 133,
    fmlId: '1001Aug-23VOLULU003',
    section: '*Lubricating Oil',
    accountCode: 'VOLULU003',
    accountDescription: 'Lubricating Oil (M/E Crank/System)',
    periodAmount: 1882.95,
    yearToDate: 6606.04,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160915,
    serial: 134,
    fmlId: '1001Aug-23VOLULU004',
    section: '*Lubricating Oil',
    accountCode: 'VOLULU004',
    accountDescription: 'Lubricating Oil (M/E Cylinder)',
    periodAmount: 3521.35,
    yearToDate: 23408.************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.040Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160916,
    serial: 135,
    fmlId: '1001Aug-23VOLULU005',
    section: '*Lubricating Oil',
    accountCode: 'VOLULU005',
    accountDescription: 'Lubricating Oil (Others)',
    periodAmount: 0,
    yearToDate: 13985.62,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160917,
    serial: 137,
    fmlId: '1001Aug-23*Total Lubricating Oil',
    section: '*Total Lubricating Oil',
    accountCode: null,
    accountDescription: null,
    periodAmount: 6729.54,
    yearToDate: 47136.57,
    transactionDate: '2023-08-01',
    sectionGroup: '6.131',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160918,
    serial: 143,
    fmlId: '1001Aug-23*Management Fees',
    section: '*Management Fees',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.143',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160919,
    serial: 143,
    fmlId: '1001Aug-23VOMFMF001',
    section: '*Management Fees',
    accountCode: 'VOMFMF001',
    accountDescription: 'Management Fees',
    periodAmount: 0,
    yearToDate: 49250,
    transactionDate: '2023-08-01',
    sectionGroup: '6.143',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160920,
    serial: 145,
    fmlId: '1001Aug-23**Total-6.143',
    section: '**Total-6.143',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.143',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160921,
    serial: 146,
    fmlId: '1001Aug-23*Superintendents Expenses',
    section: '*Superintendents Expenses',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.146',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160922,
    serial: 146,
    fmlId: '1001Aug-23VOMEME028',
    section: '*Superintendents Expenses',
    accountCode: 'VOMEME028',
    accountDescription: "Superintendent'S Travel - Airfare",
    periodAmount: 7123.2,
    yearToDate: 8044.************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.146',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160923,
    serial: 147,
    fmlId: '1001Aug-23VOMEME029',
    section: '*Superintendents Expenses',
    accountCode: 'VOMEME029',
    accountDescription: "Superintendent'S Travel - Others",
    periodAmount: 0,
    yearToDate: 1282,
    transactionDate: '2023-08-01',
    sectionGroup: '6.146',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160924,
    serial: 149,
    fmlId: '1001Aug-23**Total-6.146',
    section: '**Total-6.146',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.146',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160925,
    serial: 150,
    fmlId: '1001Aug-23*Vessels Communication Expenses',
    section: '*Vessels Communication Expenses',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.150',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160926,
    serial: 150,
    fmlId: '1001Aug-23VOMEME017',
    section: '*Vessels Communication Expenses',
    accountCode: 'VOMEME017',
    accountDescription: 'Ships Communication Others',
    periodAmount: 0,
    yearToDate: 2113.92,
    transactionDate: '2023-08-01',
    sectionGroup: '6.150',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160927,
    serial: 151,
    fmlId: '1001Aug-23VOMEME018',
    section: '*Vessels Communication Expenses',
    accountCode: 'VOMEME018',
    accountDescription: 'Ships Communication Satelite',
    periodAmount: 0,
    yearToDate: 5652.35,
    transactionDate: '2023-08-01',
    sectionGroup: '6.150',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160928,
    serial: 152,
    fmlId: '1001Aug-23**Total-6.150',
    section: '**Total-6.150',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.150',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160929,
    serial: 153,
    fmlId: '1001Aug-23*Vessels Miscellaneous Expenses',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160930,
    serial: 155,
    fmlId: '1001Aug-23VOMEME004',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME004',
    accountDescription: 'Exchange Difference',
    periodAmount: 0,
    yearToDate: 663.03,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160931,
    serial: 158,
    fmlId: '1001Aug-23VOMEME008',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME008',
    accountDescription: 'Incidental Port Expenses',
    periodAmount: 0,
    yearToDate: 1200,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160932,
    serial: 161,
    fmlId: '1001Aug-23VOMEME015',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME015',
    accountDescription: 'Opa 90 Consultant',
    periodAmount: 0,
    yearToDate: 628.*************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160933,
    serial: 162,
    fmlId: '1001Aug-23VOMEME016',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME016',
    accountDescription: 'Ships Bank Charges',
    periodAmount: 0,
    yearToDate: 2972.02,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160934,
    serial: 163,
    fmlId: '1001Aug-23VOMEME019',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME019',
    accountDescription: 'Ships Entertainment Expenses',
    periodAmount: 0,
    yearToDate: 283,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160935,
    serial: 165,
    fmlId: '1001Aug-23VOMEME021',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME021',
    accountDescription: 'Ships Audit Fee',
    periodAmount: 4900,
    yearToDate: 4900,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160936,
    serial: 166,
    fmlId: '1001Aug-23VOMEME023',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME023',
    accountDescription: 'Ships Postage & Courier',
    periodAmount: 0,
    yearToDate: 337.5,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160937,
    serial: 167,
    fmlId: '1001Aug-23VOMEME024',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME024',
    accountDescription: 'Ships Printing & Stationery',
    periodAmount: 0,
    yearToDate: 1335.39,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160938,
    serial: 168,
    fmlId: '1001Aug-23VOMEME025',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME025',
    accountDescription: 'Ships Sundry Expenses',
    periodAmount: 0,
    yearToDate: 2907.57,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160939,
    serial: 170,
    fmlId: '1001Aug-23VOMEME032',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEME032',
    accountDescription: 'Vetting Inspection',
    periodAmount: 0,
    yearToDate: 190.97,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160940,
    serial: 172,
    fmlId: '1001Aug-23VOMEAC001',
    section: '*Vessels Miscellaneous Expenses',
    accountCode: 'VOMEAC001',
    accountDescription: 'Miscellaneous Expenses : Committed',
    periodAmount: 0,
    yearToDate: 11467.************,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160941,
    serial: 173,
    fmlId: '1001Aug-23*Total Miscellaneous Expenses',
    section: '*Total Miscellaneous Expenses',
    accountCode: null,
    accountDescription: null,
    periodAmount: 4900,
    yearToDate: 26885.62,
    transactionDate: '2023-08-01',
    sectionGroup: '6.153',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160942,
    serial: 175,
    fmlId: "1001Aug-23*Charterer's Expenses",
    section: "*Charterer's Expenses",
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160943,
    serial: 177,
    fmlId: '1001Aug-23VOCHCH008',
    section: "*Charterer's Expenses",
    accountCode: 'VOCHCH008',
    accountDescription: 'Charterers : Sundry Expenses - Spot',
    periodAmount: 0,
    yearToDate: 14763,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.041Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160944,
    serial: 178,
    fmlId: '1001Aug-23VOCHCH009',
    section: "*Charterer's Expenses",
    accountCode: 'VOCHCH009',
    accountDescription: 'Charterers : Sundry Expenses - Tc',
    periodAmount: 0,
    yearToDate: 4900,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160945,
    serial: 179,
    fmlId: '1001Aug-23VOCHCH010',
    section: "*Charterer's Expenses",
    accountCode: 'VOCHCH010',
    accountDescription: 'Charterers : Victualling',
    periodAmount: 0,
    yearToDate: 3930,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160946,
    serial: 180,
    fmlId: '1001Aug-23VOCHCH011',
    section: "*Charterer's Expenses",
    accountCode: 'VOCHCH011',
    accountDescription: 'Hold Cleaning Allowances',
    periodAmount: 0,
    yearToDate: 15297,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160947,
    serial: 181,
    fmlId: '1001Aug-23VOCHCH012',
    section: "*Charterer's Expenses",
    accountCode: 'VOCHCH012',
    accountDescription: 'Voyage Expenses',
    periodAmount: 0,
    yearToDate: 20557.9,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160948,
    serial: 182,
    fmlId: '1001Aug-23**Total-6.175',
    section: '**Total-6.175',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.175',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160949,
    serial: 183,
    fmlId: '1001Aug-23*Non-Budget',
    section: '*Non-Budget',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160950,
    serial: 184,
    fmlId: '1001Aug-23VONBCC008',
    section: '*Non-Budget',
    accountCode: 'VONBCC008',
    accountDescription: 'Non-Budget Crew Expenses',
    periodAmount: 1005.6,
    yearToDate: 11099.22,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160951,
    serial: 187,
    fmlId: '1001Aug-23VONBME034',
    section: '*Non-Budget',
    accountCode: 'VONBME034',
    accountDescription: 'Non-Budget Miscellaneous Expenses',
    periodAmount: 0,
    yearToDate: 1500,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160952,
    serial: 188,
    fmlId: '1001Aug-23VONBNB003',
    section: '*Non-Budget',
    accountCode: 'VONBNB003',
    accountDescription: 'Non-Budget Insurance',
    periodAmount: 0,
    yearToDate: -8000,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160953,
    serial: 192,
    fmlId: '1001Aug-23VONBSF006',
    section: '*Non-Budget',
    accountCode: 'VONBSF006',
    accountDescription: 'Non-Budget Survey (Others)',
    periodAmount: 0,
    yearToDate: -2850,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160954,
    serial: 197,
    fmlId: '1001Aug-23VONBAC001',
    section: '*Non-Budget',
    accountCode: 'VONBAC001',
    accountDescription: 'Non Budgeted Expenses : Committed',
    periodAmount: 0,
    yearToDate: 1933.33,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160955,
    serial: 198,
    fmlId: '1001Aug-23*Total Non-Budget',
    section: '*Total Non-Budget',
    accountCode: null,
    accountDescription: null,
    periodAmount: 1005.6,
    yearToDate: 3682.55,
    transactionDate: '2023-08-01',
    sectionGroup: '6.183',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160956,
    serial: 223,
    fmlId: '1001Aug-23*Total Expenses',
    section: '*Total Expenses',
    accountCode: null,
    accountDescription: null,
    periodAmount: 163781.94,
    yearToDate: 967515.16,
    transactionDate: '2023-08-01',
    sectionGroup: '6',
    reportId: 3769,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2022-10-03T00:00:00.000Z',
    modifiedOn: '2023-10-03T06:09:51.042Z',
    createdBy: '000',
    modifiedBy: null,
    id: 160957,
    serial: 224,
    fmlId: '1001Aug-23*Surplus / (Deficit)',
    section: '*Surplus / (Deficit)',
    accountCode: null,
    accountDescription: null,
    periodAmount: -163781.94,
    yearToDate: -76109.39,
    transactionDate: '2023-08-01',
    sectionGroup: '',
    reportId: 3769,
  },
];

export const mockReportRowCustomizationData = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-08-20T07:02:46.739Z',
    modifiedOn: '2024-08-20T07:02:46.739Z',
    createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    id: 16917,
    type: 1,
    fmlId: 'VBLIOWN03-6014280-2',
    reportType: 'CFS-ACCRUAL',
    reportId: 544,
  },

  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-08-20T07:20:24.606Z',
    modifiedOn: '2024-08-20T07:20:24.606Z',
    createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    id: 16919,
    type: 1,
    fmlId: 'VBLIOWN03-6014280-2',
    reportType: 'CFS-FLEET',
    reportId: 544,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-08-01T12:29:51.333Z',
    modifiedOn: '2024-08-01T12:29:51.333Z',
    createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    id: 14262,
    type: 2,
    fmlId: '1001Jul-23VBLIAC002',
    reportType: 'BS',
    reportId: 544,
  },
];

export const ValidatedLineItemsData = [
  {
    id: 1,
    reportId: 101,
    reportType: ReportType.CFS_ACCRUAL,
    fmlId: 'FML123456',
    isValidated: true,
    validatedBy: 1,
    createdBy: 'user1',
    modifiedBy: 'user2',
  },
  {
    id: 2,
    reportId: 102,
    reportType: ReportType.TB,
    fmlId: 'FML7891011',
    isValidated: false,
    validatedBy: 1,
    createdBy: 'user2',
    modifiedBy: 'user3',
  },
];
export const mockOCReportDataNewFormat = [
  {
    id: ********,
    serial: 2,
    fmlId: '1000Dec-test',
    section: 'Fund Receipts',
    accountCode: 'VBLIOWN03',
    accountDescription: 'Non-Budget Funds',
    periodAmount: 0,
    yearToDate: 428455.***********,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'B) Funds Received',
    comments: {
      resolved: 3,
      total: 5,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 3,
    fmlId: '1000Dec-test2',
    section: 'Fund Receipts',
    accountCode: 'test',
    accountDescription: 'test',
    periodAmount: 0,
    yearToDate: 330000,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'B) Funds Received',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 9,
    fmlId: '1000Dec-24null',
    section: null,
    accountCode: null,
    accountDescription: null,
    periodAmount: 0,
    yearToDate: 2459943.97,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 10,
    fmlId: '1000Dec-24Expenditures',
    section: 'Expenditures',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 11,
    fmlId: '1000Dec-24test',
    section: 'test',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Crew Wages Total',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 12,
    fmlId: '1000Dec-test',
    section: 'Fixed wages',
    accountCode: 'test',
    accountDescription: 'Fixed test Wages',
    periodAmount: 90459,
    yearToDate: 1068000,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Fixed wages',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 21,
    fmlId: '1000Dec-24Crew Wages Total',
    section: 'Crew Wages Total',
    accountCode: null,
    accountDescription: null,
    periodAmount: 90459,
    yearToDate: 1068000,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 81,
    fmlId: '1000Dec-test',
    section: 'Spares test',
    accountCode: 'VOSPER006',
    accountDescription: 'S test',
    periodAmount: 5078,
    yearToDate: 6090.12,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Spares test',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 108,
    fmlId: '1000Dec-24Spares Total',
    section: 'Spares Total',
    accountCode: null,
    accountDescription: null,
    periodAmount: 7651.33,
    yearToDate: 75053.36,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 143,
    fmlId: '1000Dec-24VORMSF002',
    section: 'Surveys & Registration',
    accountCode: 'VORMSF002',
    accountDescription: 'R&M : Survey Class',
    periodAmount: 1580,
    yearToDate: 27085.14,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Surveys & Registration',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 144,
    fmlId: '1000Dec-24VORMSF003',
    section: 'Surveys & Registration',
    accountCode: 'VORMSF003',
    accountDescription: 'R&M : Survey Others',
    periodAmount: 0,
    yearToDate: 2939.32,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Surveys & Registration',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 145,
    fmlId: '1000Dec-24Repairs Total',
    section: 'Repairs Total',
    accountCode: null,
    accountDescription: null,
    periodAmount: -6622.33,
    yearToDate: 64367.87,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 246,
    fmlId: '1000Dec-24Projects',
    section: 'Projects',
    accountCode: null,
    accountDescription: null,
    periodAmount: null,
    yearToDate: null,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Projects',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 247,
    fmlId: '1000Dec-24null',
    section: null,
    accountCode: null,
    accountDescription: null,
    periodAmount: 0,
    yearToDate: 0,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 249,
    fmlId: '1000Dec-test',
    section: 'Deductions / Claims',
    accountCode: '5test',
    accountDescription: 'Other test',
    periodAmount: 0,
    yearToDate: 376070.***********,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    mappedAccountType: 'Deductions / Claims',
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 252,
    fmlId: '1000Dec-24null',
    section: null,
    accountCode: null,
    accountDescription: null,
    periodAmount: 0,
    yearToDate: 376070.***********,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 253,
    fmlId: '1000Dec-24Total incl. Outlays',
    section: 'Total incl. Outlays',
    accountCode: null,
    accountDescription: null,
    periodAmount: 9550.4,
    yearToDate: 2267078.22,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
  {
    id: ********,
    serial: 254,
    fmlId: '1000Dec-24Surplus / (Deficit)',
    section: 'Surplus / (Deficit)',
    accountCode: null,
    accountDescription: null,
    periodAmount: -9550.4,
    yearToDate: 192865.75,
    transactionDate: '2024-12-31',
    createdBy: '000',
    modifiedBy: null,
    sectionGroup: '',
    reportFormatVersion: 2,
    comments: {
      resolved: 0,
      total: 0,
    },
    reportId: 123,
  },
];
