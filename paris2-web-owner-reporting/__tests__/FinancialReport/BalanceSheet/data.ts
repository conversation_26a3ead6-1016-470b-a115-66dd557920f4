import {IOwnerPreferences} from '../../../src/types';

export const mockBalanceSheetData = [
  {
    id: 25264,
    serial: 0,
    section: '#Assets',
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '0-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25265,
    serial: 1,
    section: null,
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '1-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25266,
    serial: 2,
    section: '*Current Assets',
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '2-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25267,
    serial: 3,
    section: '~Prepayment, Deposits and Receivables',
    amount: 668342.64,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '3-CBASBK001-1001-Jan-23',
    accountCode: 'CBASBK001',
    accountDescription: 'Vessel Bank',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25268,
    serial: 4,
    section: '~Prepayment, Deposits and Receivables',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '4-VBASPP003-1001-Jan-23',
    accountCode: 'VBASPP003',
    accountDescription: 'Other Prepaid Expenses',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25269,
    serial: 5,
    section: '~Prepayment, Deposits and Receivables',
    amount: -5967.24,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '5-VBASPP004-1001-Jan-23',
    accountCode: 'VBASPP004',
    accountDescription: 'P & I Recoverable',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25270,
    serial: 6,
    section: '~Prepayment, Deposits and Receivables',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '6-VBASPP006-1001-Jan-23',
    accountCode: 'VBASPP006',
    accountDescription: 'Advance to Agents and Suppliers',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25271,
    serial: 7,
    section: '^',
    amount: 662375.4,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '7-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25272,
    serial: 8,
    section: '~On Board Stocks',
    amount: 2091.*************,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '8-VBASSK001-1001-Jan-23',
    accountCode: 'VBASSK001',
    accountDescription: 'Bonded Stores',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25273,
    serial: 9,
    section: '~On Board Stocks',
    amount: 50711.5,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '9-VBASSK004-1001-Jan-23',
    accountCode: 'VBASSK004',
    accountDescription: 'Luboil Stock On Board',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25274,
    serial: 10,
    section: '~On Board Stocks',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '10-VBASSK006-1001-Jan-23',
    accountCode: 'VBASSK006',
    accountDescription: 'Stock - Order In Progress',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25275,
    serial: 11,
    section: '^',
    amount: 52803.22,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '11-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25276,
    serial: 12,
    section: '>Total Assets',
    amount: 715178.62,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '12-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25277,
    serial: 13,
    section: null,
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '13-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25278,
    serial: 14,
    section: '#Liabilities',
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '14-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25279,
    serial: 15,
    section: null,
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '15-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25280,
    serial: 16,
    section: '*Current Liabilities',
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '16-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25281,
    serial: 17,
    section: '~Dues to Crew',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '17-VBLICD006-1001-Jan-23',
    accountCode: 'VBLICD006',
    accountDescription: 'Crew Allotment Payable',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25282,
    serial: 18,
    section: '^',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '18-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25283,
    serial: 19,
    section: '*Recoveries from Crew',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '19-VBLICD011-1001-Jan-23',
    accountCode: 'VBLICD011',
    accountDescription: 'Recovery From Crew : Bonded Stores',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25284,
    serial: 20,
    section: '^',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '20-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25285,
    serial: 21,
    section: '*Accruals & Commitments',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '21-VBLIAC001-1001-Jan-23',
    accountCode: 'VBLIAC001',
    accountDescription: 'Accrued - Dry-Docking (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25286,
    serial: 22,
    section: '*Accruals & Commitments',
    amount: 22356.12,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '22-VBLIAC002-1001-Jan-23',
    accountCode: 'VBLIAC002',
    accountDescription: 'Accrued - Lubricating Oil (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25287,
    serial: 23,
    section: '*Accruals & Commitments',
    amount: 831.49,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '23-VBLIAC003-1001-Jan-23',
    accountCode: 'VBLIAC003',
    accountDescription: 'Accrued - Miscellaneous Expenses (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25288,
    serial: 24,
    section: '*Accruals & Commitments',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '24-VBLIAC004-1001-Jan-23',
    accountCode: 'VBLIAC004',
    accountDescription: 'Accrued - Non Budgeted Expenses (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25289,
    serial: 25,
    section: '*Accruals & Commitments',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '25-VBLIAC005-1001-Jan-23',
    accountCode: 'VBLIAC005',
    accountDescription: 'Accrued - Pre-Delivery (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25290,
    serial: 26,
    section: '*Accruals & Commitments',
    amount: 20925.98,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '26-VBLIAC006-1001-Jan-23',
    accountCode: 'VBLIAC006',
    accountDescription: 'Accrued - Repair & Maintenance (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25291,
    serial: 27,
    section: '*Accruals & Commitments',
    amount: 5462,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '27-VBLIAC007-1001-Jan-23',
    accountCode: 'VBLIAC007',
    accountDescription: 'Accrued - Spare Parts (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25292,
    serial: 28,
    section: '*Accruals & Commitments',
    amount: 632.64,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '28-VBLIAC008-1001-Jan-23',
    accountCode: 'VBLIAC008',
    accountDescription: 'Accrued - Stores (BS)',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25293,
    serial: 29,
    section: '*Accruals & Commitments',
    amount: 864,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '29-VBLIAC015-1001-Jan-23',
    accountCode: 'VBLIAC015',
    accountDescription: 'Supplies In Transit',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25294,
    serial: 30,
    section: '*Accruals & Commitments',
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '30-VBLIAC016-1001-Jan-23',
    accountCode: 'VBLIAC016',
    accountDescription: 'Accrued - Insurance',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25295,
    serial: 31,
    section: '*Accruals & Commitments',
    amount: 4052.*************,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '31-VBLIAC017-1001-Jan-23',
    accountCode: 'VBLIAC017',
    accountDescription: 'Sundry Accounts Payable',
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25296,
    serial: 32,
    section: '^',
    amount: 55124.72,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '32-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25297,
    serial: 33,
    section: '>Total Liabilities',
    amount: 55124.72,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '33-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25298,
    serial: 34,
    section: null,
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '34-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25299,
    serial: 35,
    section: '*Equity',
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '35-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25300,
    serial: 36,
    section: 'RETAINED EARNINGS - CURRENT YEAR',
    amount: 155548.75,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '36-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25301,
    serial: 37,
    section: 'RETAINED EARNINGS - PREVIOUS YEARS',
    amount: 504505.15,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '37-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25302,
    serial: 38,
    section: "OWNER'S CURRENT ACCOUNT",
    amount: null,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '38-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25303,
    serial: 39,
    section: '^',
    amount: 660053.9,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '39-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25304,
    serial: 40,
    section: 'Total of Equity and Liabilities',
    amount: 715178.62,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '40-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
  {
    id: 25305,
    serial: 41,
    section: null,
    amount: 0,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-01-07T00:00:00.000Z',
    modifiedOn: '2023-06-14T05:14:17.907Z',
    rowType: null,
    fmlId: '41-null-null-Jan-23',
    accountCode: null,
    accountDescription: null,
    createdBy: '000',
    modifiedBy: null,
    reportId: 3194,
  },
];

export const mockOwnerPrefrenceDataBS: (IOwnerPreferences & {id: number})[] = [
  {
    id: 1,
    preference: 'TAB:BS',
    value: 'false',
  },
];
export const mockOwnerPrefrenceDataOC: (IOwnerPreferences & {id: number})[] = [
  {
    id: 1,
    preference: 'TAB:OC',
    value: 'false',
  },
];
