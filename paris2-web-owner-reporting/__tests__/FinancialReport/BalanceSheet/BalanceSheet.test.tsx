import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {TableActionContextProvider} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import DataStoreProvider from '../../../src/context/DataStoreProvider';
import {FinancialReport} from '../../../src/pages/FinancialReport/FinancialReport.page';
import {reportDetailsOwnerWithStatusOngoing, userRoleConfig} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {setupAxios} from '../../utils/setupAxios';
import RowSelectContextProvider from '../../../src/context/RowSelectProvider';
import {balanceSheetCollapsibleData} from '../../../src/components/FinancialReport/BalanceSheet/column';

interface IArgs {
  ownerReportingRole?: typeof ownerReportingAllTrue;
  owner?: boolean;
}

const wrappedComponent = (args?: IArgs) => {
  const {ownerReportingRole = ownerReportingAllTrue, owner = false} =
    args ?? {};

  return (
    <MemoryRouter
      initialEntries={['/owner-reporting/783/reports/2996/?activeTab=4']}
    >
      <AlertContextProvider>
        <DataStoreProvider
          ga4EventTrigger={jest.fn()}
          roleConfig={{
            user: userRoleConfig,
            ownerReporting: ownerReportingRole,
            isOwner: owner,
          }}
        >
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <FinancialReport />
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </DataStoreProvider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};
const wrappedComponent2 = (args?: IArgs) => {
  const {ownerReportingRole = ownerReportingAllTrue, owner = false} =
    args ?? {};

  return (
    <MemoryRouter
      initialEntries={['/owner-reporting/783/reports/2996/?activeTab=4']}
    >
      <AlertContextProvider>
        <DataStoreProvider
          dataStore={{
            reportDetails: {
              ...reportDetailsOwnerWithStatusOngoing,
              reportCfsFormat: 2,
              reportFormatVersion: 2,
            },
            reportVersion: 2,
          }}
          ga4EventTrigger={jest.fn()}
          roleConfig={{
            user: userRoleConfig,
            ownerReporting: ownerReportingRole,
            isOwner: owner,
          }}
        >
          <RowSelectContextProvider>
            <TableActionContextProvider>
              <FinancialReport />
            </TableActionContextProvider>
          </RowSelectContextProvider>
        </DataStoreProvider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};

describe('BalanceSheet', () => {
  beforeEach(async () => {
    await setupAxios();
    await act(async () => {
      render(wrappedComponent());
    });

    await waitFor(() => {
      expect(screen.getByText('Expand All')).toBeInTheDocument();
    });
  });

  test('testing collapse all expand all functionality', async () => {
    const collapseExpandAllEle = screen.getByText('Expand All');
    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });
    expect(screen.getByText('Collapse All')).toBeInTheDocument();
    expect(screen.getAllByText('Account Code')).toHaveLength(1);
    await act(async () => {
      fireEvent.click(collapseExpandAllEle);
    });
    expect(screen.queryAllByText('Account Code')).toHaveLength(1);
  });

  test('Collapse Balance Sheet Row and select Row', async () => {
    await act(async () => {
      render(wrappedComponent({owner: true}));
    });

    const collapseExpandAllEle = screen.getAllByText('Expand All');
    await act(async () => {
      fireEvent.click(collapseExpandAllEle[0]);
    });

    expect(
      screen.getAllByTestId('balance-sheet-collapsible')[0],
    ).toBeInTheDocument();

    const allCheckboxes = screen.getAllByTestId('cashFlowStatementCheckbox');

    await act(async () => {
      fireEvent.click(allCheckboxes[0]);
    });
  });
  test('testing report with new format ', async () => {
    await act(async () => {
      render(wrappedComponent2());
    });

    await waitFor(() => {
      expect(screen.getAllByText('Expand All')[0]).toBeInTheDocument();
    });
  });
});
