import {balanceSheetHydration} from '../../../src/components/FinancialReport/BalanceSheet/utils';
import {
  BalanceSheetNode,
  BalanceSheetExpandableHeaderNode,
} from '../../../src/components/FinancialReport/BalanceSheet/types';

describe('balanceSheetHydration', () => {
  it('should return an empty array when balanceSheetData is empty', () => {
    const result = balanceSheetHydration([]);
    expect(result).toEqual([]);
  });

  it('should handle new version data correctly', () => {
    const input: BalanceSheetNode[] = [
      {section: 'Assets', accountCode: 'A1', amount: 100},
      {section: 'Total Assets', accountCode: null, amount: 100},
      {section: 'Liabilities', accountCode: 'L1', amount: 50},
      {section: 'Total Liabilities', accountCode: null, amount: 50},
      {section: 'Equity', accountCode: 'E1', amount: 50},
      {section: 'Total Equity and Liabilities', accountCode: null, amount: 100},
    ];

    const result = balanceSheetHydration(input, true);

    expect(result).toEqual([
      {
        node: {
          accountCode: 'A1',
          accountDescription: undefined,
          amount: 100,
          createdBy: undefined,
          createdOn: undefined,
          deleted: undefined,
          deletedBy: undefined,
          deletedOn: undefined,
          fmlId: 'test',
          id: undefined,
          modifiedBy: undefined,
          modifiedOn: undefined,
          reportFormatVersion: undefined,
          reportId: undefined,
          section: 'Assets',
          serial: undefined,
          transactionDate: undefined,
        },
        section: 'Assets',
      },
      {
        node: {
          accountCode: 'L1',
          accountDescription: undefined,
          amount: 50,
          createdBy: undefined,
          createdOn: undefined,
          deleted: undefined,
          deletedBy: undefined,
          deletedOn: undefined,
          fmlId: 'test',
          id: undefined,
          modifiedBy: undefined,
          modifiedOn: undefined,
          reportFormatVersion: undefined,
          reportId: undefined,
          section: 'Liabilities',
          serial: undefined,
          transactionDate: undefined,
        },
        section: 'Liabilities',
      },
      {
        children: [
          {
            node: {
              accountCode: 'E1',
              accountDescription: undefined,
              amount: 50,
              createdBy: undefined,
              createdOn: undefined,
              deleted: undefined,
              deletedBy: undefined,
              deletedOn: undefined,
              fmlId: 'test',
              id: undefined,
              modifiedBy: undefined,
              modifiedOn: undefined,
              reportFormatVersion: undefined,
              reportId: undefined,
              section: 'Equity',
              serial: undefined,
              transactionDate: undefined,
            },
            section: 'Equity',
          },
        ],
        node: {
          accountCode: 'E1',
          accountDescription: undefined,
          amount: 50,
          createdBy: undefined,
          createdOn: undefined,
          deleted: undefined,
          deletedBy: undefined,
          deletedOn: undefined,
          fmlId: 'test',
          id: undefined,
          modifiedBy: undefined,
          modifiedOn: undefined,
          reportFormatVersion: undefined,
          reportId: undefined,
          section: 'Equity',
          serial: undefined,
          transactionDate: undefined,
        },
        section: 'Equity',
      },
    ]);
  });

  it('should handle old version data correctly', () => {
    const input: BalanceSheetNode[] = [
      {section: '#Header1', accountCode: null, amount: null},
      {section: '*SubHeader1', accountCode: 'A1', amount: 100},
      {section: '^', accountCode: null, amount: 100},
      {section: '>SubHeader2', accountCode: 'A2', amount: 50},
      {section: '~SubHeader3', accountCode: 'A3', amount: 25},
    ];

    const result = balanceSheetHydration(input, false);

    expect(result).toEqual([
      {
        node: {accountCode: 'A2', amount: 50, section: '>SubHeader2'},
        section: 'SubHeader2',
      },
    ]);
  });

  it('should handle special sections correctly', () => {
    const input: BalanceSheetNode[] = [
      {section: '#Header1', accountCode: null, amount: null},
      {section: '*SubHeader1', accountCode: 'A1', amount: 100},
      {section: '^', accountCode: null, amount: 100},
      {section: '>SubHeader2', accountCode: 'A2', amount: 50},
      {section: '~SubHeader3', accountCode: 'A3', amount: 25},
    ];

    const result = balanceSheetHydration(input, false);

    expect(result).toEqual([
      {
        node: {accountCode: 'A2', amount: 50, section: '>SubHeader2'},
        section: 'SubHeader2',
      },
    ]);
  });

  it('should handle missing sections gracefully', () => {
    const input: BalanceSheetNode[] = [
      {section: null, accountCode: 'A1', amount: 100},
      {section: 'Assets', accountCode: null, amount: 200},
    ];

    const result = balanceSheetHydration(input, true);

    expect(result).toEqual([]);
  });
});
