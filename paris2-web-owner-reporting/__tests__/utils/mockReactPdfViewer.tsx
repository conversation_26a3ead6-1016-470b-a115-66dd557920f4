import {ViewerProps, Plugin} from '@react-pdf-viewer/core';
import {mockTwoPagePdfFile} from './pdfFileData';
import React, {useEffect} from 'react';
import {ThumbnailsProps} from '@react-pdf-viewer/thumbnail';

const totalPages = 2;

export let mockViewerPlugins: Plugin[] | undefined;

export const MockViewer = jest.fn((props: ViewerProps) => {
  const {onDocumentLoad, fileUrl, plugins} = props;
  mockViewerPlugins = plugins;

  useEffect(() => {
    onDocumentLoad?.({
      doc: {
        getData: async () => {
          return mockTwoPagePdfFile();
        },
        numPages: totalPages,
        getMetadata: async () => {
          return {
            info: {
              Author: 'Pdf',
              CreationDate: new Date().toISOString(),
              Creator: 'PdfLib',
              Keywords: '12424',
              PDFFormatVersion: '1.5',
              Producer: 'Adobe',
              Subject: 'Pdf Subject',
              Title: 'Pdf Title',
              Language: 'en-us',
              ModDate: new Date().toISOString(),
            },
            contentDispositionFilename: 'Pdf File Name',
          };
        },
      } as any,
      file: {data: 'pdf://url', name: 'Pdf Name'},
    });
  }, [fileUrl]);

  return null;
});

let isCalled = false;

export const MockThumbnails = (props: ThumbnailsProps) => {
  const {renderThumbnailItem, thumbnailDirection} = props;

  const classNames = isCalled ? 'rpv-thumbnail__list--horizontal' : '';

  if (!isCalled) isCalled = true;
  return (
    <div className={classNames}>
      {Array.from({length: totalPages}, (_, i) =>
        renderThumbnailItem?.({
          pageIndex: i,
          renderPageLabel: <React.Fragment>{i}</React.Fragment>,
          numPages: totalPages,
          currentPage: i,
          onJumpToPage: jest.fn(),
          onRotatePage: jest.fn(),
          key: i.toString(),
          renderPageThumbnail: 'Thumbnail' as any,
        }),
      )}
    </div>
  );
};
