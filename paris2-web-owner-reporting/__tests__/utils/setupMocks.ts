class IntersectionObserverMock {
  constructor(public callback) {}
  root = null;
  rootMargin = '';
  thresholds = [];
  takeRecords = jest.fn();

  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

class CompressionStream {}

export const setupMocks = async () => {
  Element.prototype.scrollIntoView = jest.fn();

  window.CompressionStream = CompressionStream as any;
  window.Response = CompressionStream as any;
  window.IntersectionObserver = IntersectionObserverMock;
  window.open = jest.fn();
};
