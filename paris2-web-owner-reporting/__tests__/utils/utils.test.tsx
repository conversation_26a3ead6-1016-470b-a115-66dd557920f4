import {render} from '@testing-library/react';
import moment from 'moment';
import {extractDocumentNameFromHeader} from '../../src/utils/document';
import {
  getDifferenceBetweenDays,
  getLastDayCurrentMonth,
  rotateMonthStats,
} from '../../src/utils/time';

import React from 'react';
import {DateTimeFormatter} from '../../src/components/common';
import {PdfMimeType} from '../../src/constants/file';
import {
  createObjectFromString,
  generateEditedPdfFileName,
  numberConvertor,
  truncateString,
} from '../../src/utils/common';
import {extractErrorMessage} from '../../src/utils/error';
import {validateEditedPdfFiles, validateFileInfo} from '../../src/utils/file';
import {
  filterDuplicatesByKey,
  formatCurrency,
  formatDate,
  formatDecimalNumberToTwoPlace,
  getFormattedDateInHongKongTimezone,
  hasLengthOneOrGreater,
  nameGenerator,
} from '../../src/utils/helper';
import {mockTwoPagePdfFile} from './pdfFileData';

describe('time util Functions', () => {
  test('Difference Between Days', () => {
    const startDate = '2024-08-01';
    const endDate = '2024-08-10';
    expect(getDifferenceBetweenDays(startDate, endDate)).toBe(10);
  });

  test('Get last day of the current month', () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const expectedLastDay = moment(
      new Date(currentYear, currentMonth + 1, 0),
    ).format('YYYY-MM-DD');

    expect(getLastDayCurrentMonth()).toBe(expectedLastDay);
  });

  test('correctly rotate the array starting from index 3', () => {
    const monthStats = [
      {
        monthIndex: 1,
        shortName: 'Feb',
      },
      {
        monthIndex: 2,
        shortName: 'Mar',
      },
      {
        monthIndex: 3,
        shortName: 'Apr',
      },
      {
        monthIndex: 4,
        shortName: 'May',
      },
      {
        monthIndex: 5,
        shortName: 'Jun',
      },
      {
        monthIndex: 6,
        shortName: 'Jul',
      },
      {
        monthIndex: 7,
        shortName: 'Aug',
      },
      {
        monthIndex: 8,
        shortName: 'Sep',
      },
      {
        monthIndex: 9,
        shortName: 'Oct',
      },
      {
        monthIndex: 10,
        shortName: 'Nov',
      },
      {
        monthIndex: 11,
        shortName: 'Dec',
      },
      {
        monthIndex: 0,
        shortName: 'Jan',
      },
    ];

    expect(rotateMonthStats(1)).toEqual(monthStats);
  });
});

describe('document util function', () => {
  test('extract the document name from a valid content-disposition header', () => {
    const headers = {
      'content-disposition': 'attachment; filename=document.pdf',
    };
    expect(extractDocumentNameFromHeader(headers)).toBe('document.pdf');
  });
});

describe('Common util function', () => {
  test('number converter', () => {
    expect(numberConvertor('2')).toBe(2);
  });

  test('number converter should return 0 for NAN', () => {
    expect(numberConvertor(undefined)).toBe(0);
  });

  test('Create Object from string key value', () => {
    expect(
      createObjectFromString('name=Jack&email=<EMAIL>'),
    ).toStrictEqual({
      name: 'Jack',
      email: '<EMAIL>',
    });
  });

  test('truncate String', () => {
    const text =
      'Lorem ipsum dolor sit amet consectetur adipisicing elit. Natus molestias culpa reiciendis itaque corrupti. Adipisci';

    const expectedResult = text.slice(0, 50) + '...';

    expect(truncateString(text)).toBe(expectedResult);
  });

  test('Generate Edited Pdf File Name', () => {
    const timestamp = Math.floor(Date.now() / 1000);

    expect(generateEditedPdfFileName('upcoming_events_edited_121.pdf')).toBe(
      `upcoming_events_edited_${timestamp}.pdf`,
    );
  });
});

describe('Error Util Functions', () => {
  const errorMsg = 'Something went wrong. Please try again';
  const errorMsgFromServer = 'error message from server';
  test('Error Message', () => {
    const errorObj = {
      message: errorMsg,
      isAxiosError: true,
      response: {
        data: {
          message: errorMsgFromServer,
        },
      },
    };
    expect(extractErrorMessage(errorObj)).toBe(errorMsgFromServer);
  });
});

describe('Helper utils Functions', () => {
  test('format Date function should return Invalid Date', () => {
    expect(formatDate('test')).toBe('Invalid Date');
  });

  test('should return date in US timezone', () => {
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    };

    const expectedResult = new Date().toLocaleString('en-US', options);

    expect(formatDate(new Date())).toBe(expectedResult);
  });

  test('should return date in Hongkong timezone', () => {
    expect(getFormattedDateInHongKongTimezone('2024-08-19')).toBe('19/08/2024');
  });

  test('format Decimal no it should return null', () => {
    expect(formatDecimalNumberToTwoPlace(null)).toBe(null);
  });

  test('it should return the no in decimal format', () => {
    expect(formatDecimalNumberToTwoPlace(5)).toBe('5.00');
  });

  test('format Currency return null', () => {
    expect(formatCurrency(null, false, false)).toBe(null);
  });

  test('format Currency', () => {
    expect(formatCurrency(4, false, false)).toBe('4.00');
  });

  test('filterDuplicatesByKey', () => {
    expect(
      filterDuplicatesByKey(
        [
          {
            id: '1',
            name: 'John',
          },
          {
            id: '2',
            name: 'Tom',
          },

          {
            id: '1',
            name: 'Sean',
          },
        ],
        'id',
      ),
    ).toEqual([
      {
        id: '1',
        name: 'John',
      },
      {
        id: '2',
        name: 'Tom',
      },
    ]);
  });

  test('should return full name when both first and last names are provided', () => {
    const firstName = 'John';
    const lastName = 'Doe';
    const result = nameGenerator(firstName, lastName);
    expect(result).toBe('John Doe');
  });

  test('should return only the first name when only the first name is provided', () => {
    const firstName = 'Jane';
    const lastName = null;
    const result = nameGenerator(firstName, lastName);
    expect(result).toBe('Jane');
  });

  test('should return only the last name when only the last name is provided', () => {
    const firstName = null;
    const lastName = 'Smith';
    const result = nameGenerator(firstName, lastName);
    expect(result).toBe('Smith');
  });

  test('should return an empty string when both names are null', () => {
    const firstName = null;
    const lastName = null;
    const result = nameGenerator(firstName, lastName);
    expect(result).toBe('');
  });

  test('has Length Greater than one', () => {
    expect(
      hasLengthOneOrGreater({
        id: '123',
      }),
    ).toBe(true);
  });
});

describe('validateEditedPdfFiles', () => {
  test('should return an error message if file type is not supported', async () => {
    const file = new File(['dummy content'], 'test.pdf', {
      type: 'application/pdf',
    });
    const mergedPdfSize = 1024 * 1024;

    const result = await validateEditedPdfFiles(file, mergedPdfSize);
    expect(result).toBe(
      'File type is not supported. Only PDF files are allowed to upload',
    );
  });

  test('should return an error message if file size is greater than 50MB', async () => {
    const pdfFile = new File([mockTwoPagePdfFile()], 'fileName.pdf', {
      type: PdfMimeType,
    });

    const mergedPdfSize = 10247 * 10247;

    const result = await validateEditedPdfFiles(pdfFile, mergedPdfSize);
    expect(result).toBe('Maximum size of the file should be up to 50 MB');
  });

  test('validate file Name and return error for special characters', async () => {
    const pdfFile = new File([mockTwoPagePdfFile()], 'fileName$$$.pdf', {
      type: PdfMimeType,
    });

    const mergedPdfSize = 1024 * 1024;

    const result = await validateEditedPdfFiles(pdfFile, mergedPdfSize);
    expect(result).toBe(
      'File name should not contain any special characters. Kindly use only the alphabets, numbers, period (.), hyphen (-), space',
    );
  });

  test('validate File, file size should be greater than the max size', async () => {
    function createBlob(sizeInBytes) {
      const arrayBuffer = new ArrayBuffer(sizeInBytes);
      const view = new Uint8Array(arrayBuffer);
      view.fill(0);
      return new Blob([view], {type: 'application/pdf'});
    }

    const blob = createBlob(2 * 10249 * 10249);

    const pdfFile = new File([blob], 'fileName.pdf', {
      type: PdfMimeType,
    });

    const result = await validateFileInfo([], pdfFile, true);
    expect(result).toBe(
      'File type is not supported. Only Excel & PDF files are allowed to be uploaded',
    );
  });
});

describe('date time formatter', () => {
  test('renders with date as null', () => {
    const {container} = render(<DateTimeFormatter date={null} />);
    expect(container.firstChild).toHaveTextContent('- - -');
  });
});
