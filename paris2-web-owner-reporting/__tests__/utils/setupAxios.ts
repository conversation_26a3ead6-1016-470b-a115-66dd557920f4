import axios from 'axios';
import {mockBalanceSheetData} from '../FinancialReport/BalanceSheet/data';
import {
  mockBVRRemarkNode,
  mockBudgetVarianceReportData,
} from '../FinancialReport/BudgetVarianceReport/data';
import {mockCashFlowData} from '../FinancialReport/CashFlowStatement/data';
import {
  keycloakOwnerAllAttribute,
  keycloakUserDetails,
  keycloasOwnerDetails,
  mockDocumentsCountList,
  mockDocumentsList,
  mockReportDetailList,
  mockViewCommentList,
  ownerPreferencesData,
  reportDetailsWithStatusOngoing,
  vesselData,
} from '../data';
import {mockTwoPagePdfFile} from './pdfFileData';
import {
  mockOperatingCostData,
  mockReportRowCustomizationData,
  ValidatedLineItemsData,
} from '../FinancialReport/OperatingCost/data';

const signedUrls = [
  {url: 'https://test.com?val=test&key=asdjf-asdf/sadfj-adsfj'},
  {url: 'https://test2.com?val=test2&key=asdjf-asdf/sadfj-adsfj1'},
];

const pdfUrl = 'https://www.pdfUrl.sample.pdf';

export const mockAxiosGetMap = {
  '/reports?year=': mockReportDetailList,
  '/documents/upload-with-meta/generate-url?count=': {
    signedUrls,
    uuid: '23432',
  },
  '/download/generate-url': pdfUrl,
  [pdfUrl]: mockTwoPagePdfFile(),
  '/reports/submit/': {url: 'http://test.23'},
  '/documents/save': {failed: []},
  '/documents/upload/save/': [
    {id: 234, mimeType: 'application/pdf', path: 'value', fmlId: 'data-test'},
  ],
  '/comments': [...mockViewCommentList],
  '/vessel/v2/ownerships/': {...vesselData},
  '/keycloak-admin/users-with-attr': [...keycloasOwnerDetails],
  '/keycloak-admin/users?returnAttribute': {users: {...keycloakUserDetails}},
  '/keycloak-admin/users/attr?ship_party_id': [...keycloakOwnerAllAttribute],
  '/reports': reportDetailsWithStatusOngoing,
  '/owner-preferences': ownerPreferencesData,
  '/cash-flow-statements/': mockCashFlowData,
  '/balance-sheets/': mockBalanceSheetData,
  '/variances/': mockBudgetVarianceReportData,
  '/documents/count?reportId': mockDocumentsCountList,
  '/documents': mockDocumentsList,
  '/operating-cost/': mockOperatingCostData,
  '/report-row-customization': mockReportRowCustomizationData,
  '/validated-line-items?reportId=12&reportType=TB': ValidatedLineItemsData,
};

export const axiosGetMockFunc = async (url: string) => {
  for (const key in mockAxiosGetMap) {
    if (url.includes(key)) return {data: mockAxiosGetMap[key]};
  }

  return {data: {}};
};

export const axiosPostMockFunc = async (url: string) => {
  if (url.includes('/reports/manual-refresh-report/')) return {data: {}};
  if (url.includes('/export')) return {data: {url: ''}};
  if (url.includes('/accounting-report-export'))
    return {data: {url: 'http://test.221'}};
  if (url.includes('/remarks/')) return {data: mockBVRRemarkNode};

  return {data: {}};
};

export const axiosPatchMockFunc = async (url: string) => {
  return {data: {}};
};

export const axiosDeleteMockFunc = async (url: string) => {
  return {data: {}};
};

export const setupAxios = async () => {
  (axios.get as jest.Mock).mockImplementation(axiosGetMockFunc);

  (axios.post as jest.Mock).mockImplementation(axiosPostMockFunc);

  (axios.patch as jest.Mock).mockImplementation(axiosPatchMockFunc);

  (axios.delete as jest.Mock).mockImplementation(axiosDeleteMockFunc);
};
