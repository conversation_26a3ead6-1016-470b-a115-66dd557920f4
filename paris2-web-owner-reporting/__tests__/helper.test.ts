import moment from 'moment-timezone';
import {ReportType} from '../src/enums';
import {
  formatDate,
  getFormattedDateInHongKongTimezone,
  formatDecimalNumberToTwoPlace,
  formatCurrency,
  toArray,
  filterDuplicatesByKey,
  getSelectedFmlIdParentNode,
  expandAllLevelsOfTheAccordion,
  waitAsync,
  getClassNameAsObj,
  nameGenerator,
  convertPercentToPixel,
  actionableItemGenerator,
  hasLengthOneOrGreater,
  isBackDate,
  filterReportHistoryData,
  validateRemarks,
  getNewReportTypeCFS,
} from '../src/utils/helper';

describe('Helper Utility Functions', () => {
  describe('formatDate', () => {
    it('should format a valid date correctly', () => {
      const date = '2023-10-01T10:00:00Z';
      const formattedDate = formatDate(date);
      expect(formattedDate).toBeTruthy();
    });

    it('should return null for invalid date', () => {
      const formattedDate = formatDate('');
      expect(formattedDate).toBeNull();
    });
  });

  describe('getFormattedDateInHongKongTimezone', () => {
    it('should format a valid date in Hong Kong timezone', () => {
      const date = '2023-10-01T10:00:00Z';
      const formattedDate = getFormattedDateInHongKongTimezone(date);
      expect(formattedDate).toBe('01/10/2023');
    });

    it('should return null for invalid date', () => {
      const formattedDate = getFormattedDateInHongKongTimezone('invalid-date');
      expect(formattedDate).toBeNull();
    });
  });

  describe('formatDecimalNumberToTwoPlace', () => {
    it('should format a number to two decimal places', () => {
      const result = formatDecimalNumberToTwoPlace(123.456);
      expect(result).toBe('123.46');
    });

    it('should return null for invalid input', () => {
      const result = formatDecimalNumberToTwoPlace(null);
      expect(result).toBeNull();
    });
  });

  describe('formatCurrency', () => {
    it('should format a number as currency with commas', () => {
      const result = formatCurrency(1234567.89);
      expect(result).toBe('1,234,567.89');
    });

    it('should handle negative numbers', () => {
      const result = formatCurrency(-1234.56);
      expect(result).toBe('-1,234.56');
    });

    it('should return null for invalid input', () => {
      const result = formatCurrency(null);
      expect(result).toBeNull();
    });
  });

  describe('toArray', () => {
    it('should convert a string to an array', () => {
      const result = toArray('test');
      expect(result).toEqual(['test']);
    });

    it('should return the same array if input is already an array', () => {
      const result = toArray(['test']);
      expect(result).toEqual(['test']);
    });
  });

  describe('filterDuplicatesByKey', () => {
    it('should filter duplicates based on a key', () => {
      const objects = [
        {id: 1, name: 'A'},
        {id: 2, name: 'B'},
        {id: 1, name: 'A'},
      ];
      const result = filterDuplicatesByKey(objects, 'id');
      expect(result).toEqual([
        {id: 1, name: 'A'},
        {id: 2, name: 'B'},
      ]);
    });
  });

  describe('getClassNameAsObj', () => {
    it('should convert class names to an object', () => {
      const result = getClassNameAsObj('class1 class2');
      expect(result).toEqual({class1: true, class2: true});
    });
  });

  describe('nameGenerator', () => {
    it('should generate full name from first and last name', () => {
      const result = nameGenerator('John', 'Doe');
      expect(result).toBe('John Doe');
    });

    it('should return first name if last name is null', () => {
      const result = nameGenerator('John', null);
      expect(result).toBe('John');
    });

    it('should return an empty string if both names are null', () => {
      const result = nameGenerator(null, null);
      expect(result).toBe('');
    });
  });

  describe('convertPercentToPixel', () => {
    it('should convert percentage to pixels', () => {
      const result = convertPercentToPixel(50, 200);
      expect(result).toBe(100);
    });
  });

  describe('actionableItemGenerator', () => {
    it('should generate an actionable item object', () => {
      const result = actionableItemGenerator();
      expect(result).toEqual({
        CFS: [],
        [ReportType.BS]: [],
        [ReportType.BS_V2]: [],
        [ReportType.TB]: [],
        [ReportType.TB_V2]: [],
      });
    });
  });

  describe('hasLengthOneOrGreater', () => {
    it('should return true if any key has length >= 1', () => {
      const data = {key1: [], key2: [1]};
      expect(hasLengthOneOrGreater(data)).toBe(true);
    });

    it('should return false if all keys have length < 1', () => {
      const data = {key1: [], key2: []};
      expect(hasLengthOneOrGreater(data)).toBe(false);
    });
  });

  describe('isBackDate', () => {
    it('should return "orange" for backdated transactions', () => {
      const result = isBackDate(
        moment().toISOString(),
        moment().subtract(2, 'days').toISOString(),
      );
      expect(result).toBe('orange');
    });

    it('should return "yellow" for non-backdated transactions', () => {
      const result = isBackDate(moment().toISOString(), moment().toISOString());
      expect(result).toBe('yellow');
    });

    it('should return null if current date is not the same as changed date', () => {
      const result = isBackDate(
        moment().subtract(1, 'days').toISOString(),
        moment().toISOString(),
      );
      expect(result).toBeNull();
    });
  });

  describe('validateRemarks', () => {
    it('should return valid for meaningful remarks', () => {
      const result = validateRemarks('Valid remarks');
      expect(result).toEqual({isValid: true});
    });

    it('should return invalid for empty remarks', () => {
      const result = validateRemarks('');
      expect(result).toEqual({
        isValid: false,
        errorMessage: 'Remarks are required',
      });
    });

    it('should return invalid for remarks with only special characters', () => {
      const result = validateRemarks('!!!');
      expect(result).toEqual({
        isValid: false,
        errorMessage:
          'Remarks must contain actual text, not just special characters, spaces, or hyphens',
      });
    });
  });

  describe('getNewReportTypeCFS', () => {
    it('should return the correct report type for version 2', () => {
      const result = getNewReportTypeCFS(ReportType.CFS_FLEET, 2);
      expect(result).toBe(ReportType.CFS_FLEET_V2);
    });

    it('should return the same report type for version 1', () => {
      const result = getNewReportTypeCFS(ReportType.CFS_FLEET, 1);
      expect(result).toBe(ReportType.CFS_FLEET);
    });
  });
});
