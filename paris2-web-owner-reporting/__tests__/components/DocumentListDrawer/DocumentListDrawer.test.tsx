import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import axios from 'axios';
import React from 'react';
import {MemoryRouter} from 'react-router-dom';
import {DocumentPreviewLinkWrapper} from '../../../src/components/DocumentListDrawer/DocumentListItem';
import {DocumentListDrawer} from '../../../src/components/DocumentListDrawer/index';
import {TableActionContextProvider} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import {DataStoreContext} from '../../../src/context/DataStoreProvider';
import DocumentContextProvider from '../../../src/context/DocumentProvider';
import {Format, IReportDetails} from '../../../src/types';
import {getDocumentWithCountLabel} from '../../../src/utils/document';
import {getReportTypeCFS} from '../../../src/utils/reportType';
import {
  mockDocumentsCountList,
  mockDocumentsList,
  reportDetailsOwnerWithStatusOngoing,
  reportDetailsWithAsInternalGroup,
  reportDetailsWithStatusClosed,
  reportDetailsWithStatusOngoing,
  userRoleConfig,
  vesselAccountantRoleConfig,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';
import {setupAxios, setupMocks} from '../../utils';

const onClose = jest.fn();

interface IArgs {
  user?: typeof vesselAccountantRoleConfig;
  reportDetails?: IReportDetails;
  fmlId?: string;
  renderRemark?: boolean;
}

const wrappedComponent = (args?: IArgs) => {
  const {
    user = vesselAccountantRoleConfig,
    reportDetails = reportDetailsWithStatusOngoing,
    fmlId = mockDocumentsList[0].fmlId,
    renderRemark = false,
  } = args || {};

  return (
    <MemoryRouter>
      <AlertContextProvider>
        <DataStoreContext.Provider
          value={{
            dataStore: {reportDetails, ownerPreferences: null},
            setDataStore: jest.fn(),
            ga4EventTrigger: jest.fn(),
            roleConfig: {user, ownerReporting: ownerReportingAllTrue},
          }}
        >
          <TableActionContextProvider>
            <DocumentContextProvider
              reportId={reportDetails.id}
              reportType={getReportTypeCFS()}
            >
              <DocumentListDrawer
                show
                onClose={onClose}
                reportType={getReportTypeCFS({format: Format.Fleet})}
                fmlId={fmlId}
                title={getDocumentWithCountLabel({
                  selectedDocuments: mockDocumentsList.filter(
                    doc => doc.status === 'SELECTED',
                  ).length,
                  totalDocuments: mockDocumentsList.length,
                })}
                shouldRenderSaveBtn
                canRenderRemark={renderRemark}
              />
            </DocumentContextProvider>
          </TableActionContextProvider>
        </DataStoreContext.Provider>
      </AlertContextProvider>
    </MemoryRouter>
  );
};

const pdfSignature = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
const blob = new Blob([pdfSignature], {type: 'application/pdf'});

beforeEach(async () => {
  await setupAxios();
  onClose.mockReset();

  await setupMocks();
});

describe('DocumentListDrawer', () => {
  test('Rendering DocumentListDrawer as Owner', async () => {
    reportDetailsOwnerWithStatusOngoing;

    await act(async () => {
      render(
        wrappedComponent({reportDetails: reportDetailsOwnerWithStatusOngoing}),
      );
    });
  });

  test('Testing Row Document Select/UnSelect ', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDocumentNode = screen.getAllByTestId(
      'documentListItemUncheckBtn',
    )[0];

    expect(screen.getAllByTestId('documentListItemUncheckBtn')).toHaveLength(1);

    await act(async () => {
      fireEvent.click(firstDocumentNode); //selecting
    });

    expect(screen.queryAllByTestId('documentListItemUncheckBtn')).toHaveLength(
      0,
    );

    await act(async () => {
      fireEvent.click(firstDocumentNode); //rm selection
    });

    expect(screen.queryAllByTestId('documentListItemUncheckBtn')).toHaveLength(
      1,
    );
  });

  test('Testing edit page handler option', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Edit Pages'));
    });
  });

  test('Testing download document option', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Download Document'));
    });

    process.env.AWS_S3_URI = 'test';

    await act(async () => {
      fireEvent.click(screen.getByText('Download Document'));
    });
  });

  test('Testing saving the docs', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen
      .getAllByTestId('documentListItemCheckBtn')
      .slice(0, -1);

    await act(async () => {
      firstDoc.map(ele => fireEvent.click(ele));
    });

    expect(screen.getAllByTestId('documentListItemCheckBtn')).toHaveLength(2);

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListSaveBtn'));
    });
  });

  test('Covering image onError', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const thumbnailImage = screen.getAllByAltText('thumbnail')[0];

    fireEvent.error(thumbnailImage);
  });

  //This tests should be executed last since we are modifying the default mock implementation of Axios get in it.

  test('Testing while saving the docs getting error', async () => {
    (axios.post as jest.Mock).mockImplementation((url: string) => {
      return {data: {failed: [{}]}};
    });
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListItemCheckBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListSaveBtn'));
    });
  });

  test('Testing Error State of DocumentList', async () => {
    (axios.get as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('documents/count'))
        return Promise.resolve({
          data: mockDocumentsCountList,
        });

      throw new Error('');
    });

    await act(async () => {
      render(wrappedComponent());
    });
  });

  test('Testing Empty State of DocumentList', async () => {
    (axios.get as jest.Mock).mockImplementation((url: string) => {
      if (url.includes('documents/count'))
        return Promise.resolve({
          data: mockDocumentsCountList,
        });

      return {
        data: [],
      };
    });

    await act(async () => {
      render(wrappedComponent());
    });
  });
});

//pdf preview tests

describe('PDF Preview', () => {
  test('Testing Pdf Preview', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('documentListKebabBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Edit Pages'));
    });

    //covering the cases of switching between the selection tool

    //selecting hand pointer from selection tool
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererHandToolBtn'));
    });

    //selecting cursor pointer from selection tool
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererTextBtn'));
    });

    //covering pdf download
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererDownloadBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererCloseBtn'));
    });
  });

  test('Testing Preview Document Modal Pop up on thumbnail click', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('documentListThumbnail')[0]);
    });

    expect(screen.queryByText('Preview Document')).toBeInTheDocument();
  });

  test('Testing Preview Document Modal Pop up on thumbnail click with non pdf file', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('documentListThumbnail')[1]);
    });

    expect(screen.queryByText('Preview Document')).toBe(null);
  });

  test('render pdf when enter key is pressed', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.keyDown(screen.getAllByTestId('documentListThumbnail')[0], {
        key: 'Enter',
      });
    });

    expect(screen.getByTestId('pdf-viewer-container')).toBeInTheDocument();
  });

  test('if remarks present in the document', async () => {
    await act(async () => {
      render(wrappedComponent({fmlId: 'ASFDHJJHF-1001-AUG-5'}));
    });

    expect(screen.getByText('Remarks: remark 1')).toBeInTheDocument();
  });

  test('if parent editted doc is true show tooltip', async () => {
    await act(async () => {
      render(wrappedComponent({fmlId: 'ASFDHJJHF-1001-AUG-5'}));
    });

    expect(screen.getAllByTestId('doc-actions')[4]).toHaveAttribute(
      'title',
      'Cannot save the original document. An updated version already exists.',
    );
  });

  test('if edited by is present', async () => {
    await act(async () => {
      render(wrappedComponent({fmlId: 'ASFDHJJHF-1001-AUG-5'}));
    });

    expect(screen.getAllByTestId('edited-by')[1]).toHaveTextContent(
      'Edited by: Jimmy',
    );
  });

  test('if document is selected', async () => {
    await act(async () => {
      render(wrappedComponent({fmlId: 'ASFDHJJHF-1001-AUG-5'}));
    });

    expect(
      screen.getAllByTestId('documentListItemCheckBtn')[0],
    ).toBeInTheDocument();
  });

  test('if checkbox is disabled', async () => {
    await act(async () => {
      render(
        wrappedComponent({
          reportDetails: reportDetailsWithStatusClosed,
          fmlId: 'ASFDHJJHF-1001-AUG-5',
        }),
      );
    });

    expect(screen.getAllByTestId('documentListItemCheckBtn')[0]).toBeDisabled();
  });

  test('Edited by text exists for Eyeshare upload', async () => {
    await act(async () => {
      render(
        wrappedComponent({
          fmlId: 'ASFDHJJHF-1001-AUG-5',
        }),
      );
    });
    const eyeshare = screen.getAllByTestId('synced-from-eyeshare')[3];
    expect(eyeshare).toBeInTheDocument();
    expect(eyeshare).toHaveTextContent('Synced from Eye-Share Edited By Jimmy');
  });

  test('Synced from Eyeshare', async () => {
    await act(async () => {
      render(
        wrappedComponent({
          fmlId: 'ASFDHJJHF-1001-AUG-5',
        }),
      );
    });
    const eyeshare = screen.getAllByTestId('synced-from-eyeshare')[0];
    expect(eyeshare).toBeInTheDocument();
    expect(eyeshare).toHaveTextContent('Synced from Eye-Share');
  });
});

describe('DocumentListDrawer File Uploading', () => {
  const dataTransfer = {
    effectAllowed: 'move',
    setData: jest.fn(),
    getData: jest.fn(),
  };

  // Mock the drag event
  const dragStart = {
    dataTransfer,
  };

  test('Testing The file upload button', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const fileUploadBtn = screen.getByTestId('documentListUploadBtn');

    await act(async () => {
      fireEvent.click(fileUploadBtn);
    });
  });

  test('Testing The file upload button', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const closeBtn = screen.getByTestId('documentListCloseBtn');

    await act(async () => {
      fireEvent.click(closeBtn);
    });

    expect(onClose).toHaveBeenCalledTimes(1);
  });

  test('Testing File Uploading With Success', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListUploadBtn'));
    });

    const file = new File([blob, 'sample content'], 'sample.pdf', {
      type: 'application/pdf',
    });
    const fileWithoutName = new File([blob, 'sample content'], '', {
      type: 'application/pdf',
    });

    const fileUploadInput = screen.getByLabelText('Browse Files');

    await act(async () => {
      fireEvent.dragOver(fileUploadInput); //covering dragover event
    });

    await act(async () => {
      fireEvent.drop(fileUploadInput, {
        dataTransfer: {files: [file]},
      });
    });

    await act(async () => {
      fireEvent.change(fileUploadInput, {
        target: {files: [file]},
      });
    });

    //handing the case of name being empty
    await act(async () => {
      fireEvent.change(fileUploadInput, {
        target: {
          files: [fileWithoutName],
        },
      });
    });

    //handing the case of name being empty
    await act(async () => {
      fireEvent.change(fileUploadInput, {
        target: {
          files: [fileWithoutName],
        },
      });
    });

    //handing the case of name being empty with drop event
    await act(async () => {
      fireEvent.drop(fileUploadInput, {
        dataTransfer: {
          files: [fileWithoutName],
        },
      });
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Save'));
    });
  });

  test('Testing File Uploading With Error', async () => {
    process.env.UPLOAD_MAX_AT_ONCE = '5';
    process.env.UPLOAD_MAX_SIZE = '30000';
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListUploadBtn'));
    });

    const file = new File([blob, 'sample content'], 'sample.pdf', {
      type: 'application/pdf',
    });
    const fileWithoutName = new File([blob, 'sample content'], '', {
      type: 'text/content',
    });

    const fileUploadInput = screen.getByLabelText('Browse Files');

    await act(async () => {
      fireEvent.change(fileUploadInput, {
        target: {
          files: Array.from({length: 6}, () => file),
        },
      });
    });

    //waiting for the state to update after file selection
    await act(
      async () => await new Promise(resolve => setTimeout(resolve, 10)),
    );

    await act(async () => {
      fireEvent.drop(fileUploadInput, {
        dataTransfer: {files: Array.from({length: 5}, () => file)},
      });
    });

    await act(
      async () => await new Promise(resolve => setTimeout(resolve, 10)),
    );

    //covering dragover and dragenter handlers
    await act(async () => {
      fireEvent.dragOver(fileUploadInput);
      fireEvent.dragEnter(fileUploadInput);
    });

    await act(
      async () => await new Promise(resolve => setTimeout(resolve, 10)),
    );

    //covering input change and drop data as null cases
    await act(async () => {
      fireEvent.drop(fileUploadInput, {dataTransfer: {files: null}});
      fireEvent.change(fileUploadInput, {target: {files: null}});
    });

    await act(
      async () => await new Promise(resolve => setTimeout(resolve, 10)),
    );

    //covering the max upload size
    await act(async () => {
      process.env.UPLOAD_MAX_SIZE = '3';
      fireEvent.drop(fileUploadInput, {
        dataTransfer: {
          files: [fileWithoutName],
        },
      });
      process.env.UPLOAD_MAX_SIZE = '30000';
    });

    await act(
      async () => await new Promise(resolve => setTimeout(resolve, 10)),
    );

    //rm first file

    await act(async () => {
      fireEvent.click(screen.getAllByTestId('uploadItemRemoveBtn')[0]);
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Cancel'));
    });
  });

  test('render remarks', async () => {
    await act(async () => {
      render(wrappedComponent({renderRemark: true}));
    });
    const fileUploadBtn = screen.getByTestId('documentListUploadBtn');

    await act(async () => {
      fireEvent.click(fileUploadBtn);
    });

    await act(async () => {
      fireEvent.change(screen.getByTestId('render-remark-test-case'), {
        target: {
          value: 'Textarea value',
        },
      });
    });
  });

  test('Testing File Uploading with drag events', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListUploadBtn'));
    });

    const file = new File([blob, 'sample content'], 'sample.pdf', {
      type: 'application/pdf',
    });

    const fileUploadInput = screen.getByTestId('upload-document-box');
    expect(fileUploadInput).toBeInTheDocument();

    fireEvent.dragOver(fileUploadInput); //covering dragover event

    fireEvent.drop(fileUploadInput, {
      dataTransfer: {files: [file]},
    });

    await waitFor(() => {
      expect(screen.queryByText('sample.pdf')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.dragStart(screen.getByTestId('upload-other-files'), dragStart); //covering dragover event
    });
    await act(async () => {
      fireEvent.dragEnter(screen.getByTestId('upload-other-files'), dragStart); //covering dragover event
    });
    await act(async () => {
      fireEvent.dragEnd(screen.getByTestId('upload-other-files'), dragStart); //covering dragover event
    });

    expect(screen.getByTestId('save-file-btn')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('save-file-btn'));

    (axios.post as jest.Mock).mockImplementationOnce((url, form, config) => {
      if (config.onUploadProgress) {
        const progressEvent = {
          loaded: 500,
          total: 1000,
        };

        config.onUploadProgress(progressEvent);
      }
      return Promise.resolve({
        data: '',
        status: 200,
      });
    });
  });
});

describe('DocumentListDrawer File Deleting', () => {
  test('Testing delete document with normal user', async () => {
    await act(async () => {
      render(
        wrappedComponent({
          user: userRoleConfig,
          reportDetails: reportDetailsWithAsInternalGroup,
        }),
      );
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListDeleteBtn'));
    });

    expect(screen.queryByText('Yes')).toBeInTheDocument();
  });

  test('Testing closing the delete modal pop up', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListDeleteBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('No'));
    });
  });

  test('Testing delete document with success response', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListDeleteBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Yes'));
    });
  });

  test('Testing delete document with error response', async () => {
    (axios.delete as jest.Mock).mockImplementationOnce(() => {
      throw new Error();
    });
    await act(async () => {
      render(wrappedComponent());
    });

    const firstDoc = screen.getAllByTestId('documentListKebabBtn')[0];

    await act(async () => {
      fireEvent.click(firstDoc);
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('documentListDeleteBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByText('Yes'));
    });
  });
});

// Helper component to render children
const TestComponent = () => <span>Test Child</span>;

describe('DocumentPreviewLinkWrapper', () => {
  it('should render children inside a Link when disablePreview is false', () => {
    const documentId = 123;
    render(
      <MemoryRouter>
        <AlertContextProvider>
          <DataStoreContext.Provider
            value={{
              dataStore: {
                reportDetails: reportDetailsOwnerWithStatusOngoing,
                ownerPreferences: null,
              },
              ga4EventTrigger: jest.fn(),
              setDataStore: jest.fn(),
              roleConfig: {
                user: vesselAccountantRoleConfig,
                ownerReporting: ownerReportingAllTrue,
              },
            }}
          >
            <TableActionContextProvider>
              <DocumentContextProvider
                reportId={reportDetailsOwnerWithStatusOngoing.id}
                reportType={getReportTypeCFS()}
              >
                <DocumentPreviewLinkWrapper
                  documentId={documentId}
                  disablePreview={false}
                  children={<TestComponent />}
                />
                ,
              </DocumentContextProvider>
            </TableActionContextProvider>
          </DataStoreContext.Provider>
        </AlertContextProvider>
      </MemoryRouter>,
    );

    // Check that the Link component is rendered with the correct href
    const linkElement = screen.getByRole('link');
    expect(linkElement).toHaveAttribute(
      'href',
      `/document-preview/${documentId}`,
    );

    // Check that the children are rendered inside the Link
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  describe('DocumentListDrawer Save Button', () => {
    test('Save button should be disabled when isSaveButtonDisabled is true', async () => {
      await act(async () => {
        render(
          wrappedComponent({
            user: vesselAccountantRoleConfig,
            reportDetails: reportDetailsWithStatusOngoing,
          }),
        );
      });

      const saveButton = screen.getByTestId('documentListSaveBtn');
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toBeDisabled();
    });

    test('Save button should not be rendered when conditions are not met', async () => {
      await act(async () => {
        render(
          wrappedComponent({
            user: vesselAccountantRoleConfig,
            reportDetails: reportDetailsWithStatusClosed,
          }),
        );
      });

      expect(
        screen.queryByTestId('documentListSaveBtn'),
      ).not.toBeInTheDocument();
    });
  });
});
