/* eslint-disable @typescript-eslint/no-empty-function */
import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';

import {
  MockThumbnails,
  MockViewer,
  mockTwoPagePdfFile,
  setupAxios,
  setupMocks,
  mockViewerPlugins,
} from '../../utils';
import ReactPdfViewer, {
  LayerRenderStatus,
  Worker,
} from '@react-pdf-viewer/core';
import workerSrc from 'pdfjs-dist/build/pdf.worker.entry';
import PdfPreview from '../../../src/components/PdfPreview/PdfPreview';
import {TableActionContextProvider} from '../../../src/context';
import AlertContextProvider from '../../../src/context/AlertContextProvider';
import {DataStoreContext} from '../../../src/context/DataStoreProvider';
import DocumentContextProvider from '../../../src/context/DocumentProvider';
import {DocumentNodeType, IReportDetails} from '../../../src/types';
import {getReportTypeCFS} from '../../../src/utils/reportType';
import {
  mockDocumentsList,
  mockDocumentsNotes,
  mockNotesData,
  reportDetailsWithStatusOngoing,
  vesselAccountantRoleConfig,
} from '../../data';
import {ownerReportingAllTrue} from '../../data/owner-reporting-role';

import {PdfMimeType} from '../../../src/constants/file';
import SideBarRenderer from '../../../src/components/PdfPreview/PdfRenderer/SideBarRenderer';
import {
  HighlightTriggerState,
  Notes,
} from '../../../src/components/PdfPreview/PdfRenderer/highlightPlugin';
import React, {useState} from 'react';
import {highlightPlugin} from '@react-pdf-viewer/highlight';

const onClose = jest.fn();

interface IArgs {
  user?: typeof vesselAccountantRoleConfig;
  reportDetails?: IReportDetails;
  canEdit?: boolean;
  canHighlight?: boolean;
  fileMockData?: DocumentNodeType;
}

const wrappedComponent = (args?: IArgs) => {
  const {
    user = vesselAccountantRoleConfig,
    reportDetails = reportDetailsWithStatusOngoing,
    canEdit,
    canHighlight,
    fileMockData = mockDocumentsList[0],
  } = args || {};

  return (
    <MemoryRouter>
      <Worker workerUrl={workerSrc}>
        <AlertContextProvider>
          <DataStoreContext.Provider
            value={{
              dataStore: {reportDetails, ownerPreferences: null},
              setDataStore: jest.fn(),
              roleConfig: {user, ownerReporting: ownerReportingAllTrue},
            }}
          >
            <TableActionContextProvider>
              <DocumentContextProvider
                reportId={reportDetails.id}
                reportType={getReportTypeCFS()}
              >
                <PdfPreview
                  show
                  onClose={onClose}
                  canEditPdf={canEdit}
                  canHighlightPdf={canHighlight}
                  fileData={fileMockData}
                />
              </DocumentContextProvider>
            </TableActionContextProvider>
          </DataStoreContext.Provider>
        </AlertContextProvider>
      </Worker>
    </MemoryRouter>
  );
};

jest.mock('@react-pdf-viewer/thumbnail', () => ({
  ...jest.requireActual('@react-pdf-viewer/thumbnail'),
  thumbnailPlugin: args => ({
    ...jest.requireActual('@react-pdf-viewer/thumbnail').thumbnailPlugin(args),
    Thumbnails: MockThumbnails,
  }),
}));

jest.mock('@react-pdf-viewer/core', () => ({
  ...jest.requireActual('@react-pdf-viewer/core'),
  Viewer: MockViewer,
}));

beforeEach(async () => {
  await setupAxios();
  await setupMocks();
});

describe('Testing PdfPreview', () => {
  beforeEach(async () => {
    await act(async () => {
      render(wrappedComponent());
    });
  });

  test('Testing PdfPreview selection tool', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererHandToolBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererTextBtn'));
    });
  });
});

describe('Testing PdfPreview Edit', () => {
  beforeEach(async () => {
    jest.useFakeTimers();
    await act(async () => {
      render(wrappedComponent({canEdit: true}));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfPreviewEditBtn'));
    });
  });

  test('Testing All Page Selection', async () => {
    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-1'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-0'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-0'));
    });
  });

  test('Covering PdfPreview Edit Modal Close', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfEditCloseBtn'));
    });
  });

  test('Adding Pdf File', async () => {
    const pdfFile = new File([mockTwoPagePdfFile()], 'fileName.pdf', {
      type: PdfMimeType,
    });

    File.prototype.arrayBuffer = jest.fn(async () => mockTwoPagePdfFile());

    await act(async () => {
      fireEvent.change(screen.getByTestId('pdfEditAddPageBtn'), {
        target: {files: [pdfFile]},
      });
    });
  });

  test('Covering Error case for Adding Pdf File', async () => {
    const pdfFile = new File([mockTwoPagePdfFile()], 'fileName.pdf');

    await act(async () => {
      fireEvent.change(screen.getByTestId('pdfEditAddPageBtn'), {
        target: {files: [pdfFile]},
      });
    });

    act(() => {
      jest.advanceTimersByTime(5000); // Fast forward the timer by 1000 milliseconds
    });
  });

  test('Covering No File Selection case for Adding Pdf File', async () => {
    await act(async () => {
      fireEvent.change(screen.getByTestId('pdfEditAddPageBtn'), {
        target: {files: []},
      });
    });
  });

  test('Closing PdfPreview Delete Modal', async () => {
    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-1'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeletePagesBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeleteModalCancelBtn'));
    });
  });

  test('Deleting Pdf Page', async () => {
    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-1'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeletePagesBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeleteModalDeleteBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfEditSaveBtn'));
    });
  });

  test('undo deleting pdf page', async () => {
    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-1'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeletePagesBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeleteModalDeleteBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfEditUndoBtn'));
    });
  });

  test('Deleting Pdf Page With Error', async () => {
    (ReactPdfViewer.Viewer as jest.Mock).mockImplementationOnce(jest.fn());

    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-1'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeletePagesBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfDeleteModalDeleteBtn'));
    });
    jest.clearAllMocks();
  });

  test('select pdf page', async () => {
    await act(async () => {
      render(wrappedComponent());
    });

    await act(async () => {
      fireEvent.keyDown(screen.getByTestId('pdfThumbnail-0'), {
        key: 'Enter',
      });
    });
  });

  test('Rotate Page', async () => {
    await waitFor(async () => {
      fireEvent.click(screen.getByTestId('pdfThumbnail-0'));
    });

    const rotateBtn = screen
      .getAllByTestId('rotate-document-icon')[0]
      .querySelector('button');
    expect(rotateBtn).toBeInTheDocument();
    if (rotateBtn) fireEvent.click(rotateBtn);
  });
});

describe('Testing PdfHighlight', () => {
  beforeEach(async () => {
    jest.useFakeTimers();
    await act(async () => {
      render(wrappedComponent({canHighlight: true}));
    });

    const viewerContainer = screen.getByTestId('pdf-viewer-container');
    const pageEle = document.createElement('div');
    pageEle.dataset.testid = 'core__text-layer-0';
    pageEle.style.height = '900px';
    pageEle.style.width = '1000px';
    Object.defineProperty(pageEle, 'clientHeight', {
      configurable: true,
      value: 900,
    });
    Object.defineProperty(pageEle, 'clientWidth', {
      configurable: true,
      value: 1000,
    });
    viewerContainer.appendChild(pageEle);

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererBrushBtn'));
    });

    const highlightPlugin = mockViewerPlugins?.at(-1);

    highlightPlugin?.onTextLayerRender?.({
      ele: pageEle,
      pageIndex: 0,
      scale: 1,
      status: LayerRenderStatus.DidRender,
    });
    //
    await act(async () => {
      fireEvent.mouseDown(pageEle, {clientX: 10, clientY: 10});
    });

    await act(async () => {
      fireEvent.mouseMove(pageEle, {clientX: 100, clientY: 150});
    });

    await act(async () => {
      fireEvent.mouseUp(pageEle);
    });

    //calling renderPageLayer after adding a highlight node and adding it to the selected page
    const {container: node} = render(
      highlightPlugin?.renderPageLayer?.({pageIndex: 0} as any) as any,
    );
    pageEle.appendChild(node);
  });

  // test('Testing Highlighting and erasing in pdf', async () => {
  //   //handling all cases of brush button
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('pdfRendererBrushBtn'));
  //   });
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('pdfRendererBrushBtn'));
  //   });

  //   const highlightPlugin = mockViewerPlugins?.at(-1);
  //   const page1 = screen.getByTestId('core__text-layer-0');

  //   //handling the case where eraser is not selected but we try to delete the highlight
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('highlightedNode-0'));
  //   });

  //   //erasing the added highlighted node and handling all the case of erase btn
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('pdfRendererEraseBtn'));
  //   });
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('pdfRendererEraseBtn'));
  //   });
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('pdfRendererEraseBtn'));
  //   });

  //   //calling renderPageLayer after selecting eraser
  //   highlightPlugin?.renderPageLayer?.({pageIndex: 0} as any);

  //   //handling the edge cases for this three events
  //   await act(async () => {
  //     fireEvent.mouseDown(page1);
  //   });

  //   await act(async () => {
  //     fireEvent.mouseMove(page1);
  //   });

  //   await act(async () => {
  //     fireEvent.mouseUp(page1);
  //   });

  //   //erasing the newly added highlightedNode
  //   await act(async () => {
  //     fireEvent.click(screen.getByTestId('highlightedNode-0'));
  //   });
  // });

  test('Testing Highlighting Pdf save', async () => {
    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererSaveBtn'));
    });

    await act(async () => {
      fireEvent.click(screen.getByTestId('pdfRendererCloseBtn'));
    });
  });

  test('Testing Add Note in pdf', async () => {
    expect(screen.getByTestId('highlightedNode-0')).toBeInTheDocument();
    const addNoteIcon = screen.getByTestId('add-note-btn-icon');
    fireEvent.click(addNoteIcon);

    await act(async () => {
      fireEvent.keyDown(screen.getByTestId('highlightedNode-0'), {
        key: 'Enter',
      });
    });

    // as add note icon gets click check if the form to add note render
    expect(screen.getByTestId('add-note-box')).toBeInTheDocument();

    // now add text to input
    const noteInput = screen.getByTestId('note-input-box');
    fireEvent.change(noteInput, {target: {value: 'testuser'}});

    // now call the add Note btn
    const addNoteBtn = screen
      .getByTestId('add-note-btn')
      .querySelector('button');

    if (addNoteBtn) {
      await act(() => {
        fireEvent.click(addNoteBtn);
      });
    }
  });

  test('on side bar icon click open sidebar', async () => {
    const sideBarTopIcon = screen.getByTestId('pdfSidebarRenderer');
    fireEvent.click(sideBarTopIcon);

    // check if sidebar opens
    const sidebarPanel = screen.getAllByTestId('sidebar-renderer');
    expect(sidebarPanel[0]).toBeInTheDocument();
  });

  test('Cancel Button while adding note', async () => {
    const highlightedArea = screen.getByTestId('highlightedNode-0');
    expect(highlightedArea).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('add-note-btn-icon'));
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
  });

  test('Click highlighted area', async () => {
    const highlightedArea = screen.getByTestId('highlightedNode-0');

    fireEvent.click(screen.getByTestId('pdfRendererEraseBtn'));

    expect(highlightedArea).toBeInTheDocument();
    fireEvent.click(highlightedArea);
  });

  test('Add a Note and click Highlighted Area', async () => {
    const addNoteIcon = screen.getByTestId('add-note-btn-icon');
    fireEvent.click(addNoteIcon);

    // now add text to input
    const noteInput = screen.getByTestId('note-input-box');
    fireEvent.change(noteInput, {target: {value: 'testuser'}});

    // now call the add Note btn
    const addNoteBtn = screen
      .getByTestId('add-note-btn')
      .querySelector('button');

    if (addNoteBtn) {
      await act(() => {
        fireEvent.click(addNoteBtn);
      });
    }

    fireEvent.click(screen.getByTestId('pdfRendererEraseBtn'));

    fireEvent.click(screen.getByTestId('highlightedNode-0'));
  });

  test('Add a Note and Delete it', async () => {
    // open sidebar

    const addNoteIcon = screen.getByTestId('add-note-btn-icon');
    fireEvent.click(addNoteIcon);

    // now add text to input
    const noteInput = screen.getByTestId('note-input-box');
    fireEvent.change(noteInput, {target: {value: 'testuser'}});

    // now call the add Note btn
    const addNoteBtn = screen
      .getByTestId('add-note-btn')
      .querySelector('button');

    if (addNoteBtn) {
      await act(() => {
        fireEvent.click(addNoteBtn);
      });
    }
    fireEvent.click(screen.getByTestId('pdfSidebarRenderer'));

    // jump to note
    expect(screen.getByTestId('highlightedNode-0')).toBeInTheDocument();

    const singleNote = screen.getByTestId('single-note');

    await act(async () => {
      fireEvent.click(singleNote);
    });

    const deleteIcon = screen.getByTestId('delete-icon');
    expect(deleteIcon).toBeInTheDocument();

    fireEvent.click(deleteIcon);
    fireEvent.click(screen.getByTestId('delete-note-yes-btn'));
  });
});

describe('Testing PdfHighlight for notes as metadata', () => {
  // selected File with notes as meta data
  test('Component Renders', async () => {
    await act(async () => {
      render(wrappedComponent({fileMockData: mockDocumentsNotes[0]}));
    });
  });
});

describe('testing sidebar', () => {
  test('if sidebar exists and notes has length & delete a note', async () => {
    render(
      <DataStoreContext.Provider
        value={{
          dataStore: {
            reportDetails: reportDetailsWithStatusOngoing,
            ownerPreferences: null,
          },
          setDataStore: jest.fn(),
          roleConfig: {
            user: vesselAccountantRoleConfig,
            ownerReporting: ownerReportingAllTrue,
          },
        }}
      >
        <SideBarRenderer
          notes={mockNotesData}
          trigger={HighlightTriggerState.NOTES}
          jumpToNote={jest.fn()}
          deleteSideBarNote={jest.fn()}
          setAllNotes={jest.fn()}
        />
      </DataStoreContext.Provider>,
    );
    // check if sidebar opens
    const sidebarPanel = screen.getByTestId('sidebar-renderer');
    expect(sidebarPanel).toBeInTheDocument();

    const notes = screen.getAllByTestId('notes');
    expect(notes.length).toBe(2);

    const allDeleteIcon = screen.getAllByTestId('delete-icon');
    const deleteIcon = allDeleteIcon[0];

    const singleNote = screen.getAllByTestId('single-note');
    const note = singleNote[0];

    await act(async () => {
      fireEvent.keyDown(deleteIcon);
    });

    await act(async () => {
      fireEvent.keyDown(note);
      fireEvent.click(note);
    });

    expect(deleteIcon).toBeInTheDocument();

    if (deleteIcon) {
      await act(() => {
        fireEvent.click(deleteIcon);
      });

      // open delete modal
      expect(screen.getByTestId('delete-note-modal')).toBeInTheDocument();

      // handle note delete
      const deleteButton = screen.getByTestId('delete-note-yes-btn');
      fireEvent.click(deleteButton);
    }
  });
});
