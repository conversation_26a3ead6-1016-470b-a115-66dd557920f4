import {ReportType, StorageProvider} from '../../src/enums';
import {vesselAccountantRoleConfig} from './userData';

export const mockDocumentsList = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 220,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-MAR-12',
    originalName: 'Original Name',
    size: 214148,
    mimeType: 'application/pdf',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.S3,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: null,
    uploadedFrom: 'USER',
    status: 'NON_SELECTED',
    createdBy: vesselAccountantRoleConfig.user_id,
    modifiedBy: null,
    documentMetas: [
      {
        id: 220,
        data: {
          user: {
            id: '3264789732789432r7e-234983uiuehrq-2398u',
            name: 'Test 1',
            email: '<EMAIL>',
          },
        },
        documentId: 220,
        dataType: 'USER_INFO',
      },
    ],
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 221,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'WEFADSFAS-5328268-9',
    originalName: 'Original Name',
    size: 214148,
    mimeType: 'application/x-excel',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.S3,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: null,
    uploadedFrom: 'EYESHARE',
    status: 'SELECTED',
    createdBy: '3264789732789432r7e-234983uiuehrq-2398u',
    modifiedBy: null,
    documentMetas: [
      {
        id: 221,
        data: {
          user: {
            id: '3264789732789432r7e-234983uiuehrq-2398u',
            name: 'Test 1',
            email: '<EMAIL>',
          },
        },
        documentId: 221,
        dataType: 'USER_INFO',
      },
    ],
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 222,
    reportId: 3194,
    reportType: ReportType.VA,
    fmlId: 'AEFGSSDFD-5322763-5',
    originalName: 'Original Name',
    size: 214148,
    mimeType: 'application/default',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.EYE_SHARE,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: null,
    uploadedFrom: 'EYESHARE',
    status: 'SELECTED',
    createdBy: '3264789732789432r7e-234983uiuehrq-2398u',
    modifiedBy: null,
    documentMetas: [
      {
        id: 222,
        data: {
          user: {
            id: '3264789732789432r7e-234983uiuehrq-2398u',
            name: 'Test 1',
            email: '<EMAIL>',
          },
        },
        documentId: 222,
        dataType: 'USER_INFO',
      },
    ],
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 223,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'AEFGSSDFD-5322763-6',
    originalName: 'Original Name',
    size: 214148,
    mimeType: 'application/pdf',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.EYE_SHARE,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: null,
    uploadedFrom: 'EYESHARE',
    status: 'SELECTED',
    createdBy: '3264789732789432r7e-234983uiuehrq-2398u',
    modifiedBy: null,
    documentMetas: [],
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 225,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-AUG-5',
    originalName: '2_edited_172976.pdf',
    size: 214148,
    mimeType: 'application/pdf',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.S3,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: 225,
    uploadedFrom: 'USER',
    status: 'SELECTED',
    createdBy: vesselAccountantRoleConfig.user_id,
    modifiedBy: null,
    documentMetas: [
      {
        id: 225,
        data: {
          remarks: 'remark 1',
        },
        documentId: 225,
        dataType: 'REMARKS',
      },
      {
        id: 220,
        data: {
          user: {
            id: '3264789732789432r7e-234983uiuehrq-2398u',
            name: 'Jimmy',
            email: '<EMAIL>',
          },
        },
        documentId: 220,
        dataType: 'USER_INFO',
      },
    ],
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 226,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-AUG-5',
    originalName: '2_edited_172976.pdf',
    size: 214148,
    mimeType: 'application/pdf',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.S3,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: 226,
    uploadedFrom: 'EYESHARE',
    status: 'SELECTED',
    createdBy: vesselAccountantRoleConfig.user_id,
    modifiedBy: null,
    documentMetas: [
      {
        id: 220,
        data: {
          user: {
            id: '3264789732789432r7e-234983uiuehrq-2398u',
            name: 'Jimmy',
            email: '<EMAIL>',
          },
        },
        documentId: 220,
        dataType: 'USER_INFO',
      },
    ],
  },
];

export const mockDocumentsNotes = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-06-27T06:25:43.588Z',
    modifiedOn: '2023-06-27T06:25:43.588Z',
    id: 220,
    reportId: 3194,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-MAR-12',
    originalName: 'Original Name',
    size: 214148,
    mimeType: 'application/pdf',
    referenceId:
      'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    storageProvider: StorageProvider.S3,
    path: 'docs/temp/d07df800-f4b5-414a-ae6f-c1e2a9f6f098/570b58b3-7ff2-4b33-9bec-3172575e93f2',
    thumbPath: null,
    parentId: null,
    uploadedFrom: 'USER',
    status: 'NON_SELECTED',
    createdBy: vesselAccountantRoleConfig.user_id,
    modifiedBy: null,
    documentMetas: [
      {
        id: 4543,
        data: {
          notes: [
            {
              text: 'one',
              node: {
                pageIndex: 0,
                left: '31.**************%',
                top: '6.***************%',
                width: '13.***************%',
                height: '17.***************%',
                id: '9450922d-d956-486c-8f06-4cfadcf2',
              },
              highlightedAreaId: 'a5efca71-52e1-4a79-9079-f7d3a6299c9b',
            },
            {
              text: 'tw',
              node: {
                pageIndex: 0,
                left: '65.**************%',
                top: '9.***************%',
                width: '22.***************%',
                height: '29.***************%',
                id: 'f91e14cb-98fc-4c9e-a462-886b5563b',
              },
              highlightedAreaId: 'f91e14cb-98fc--a462-255886b5563b',
            },
          ],
        },
        documentId: 4575,
        dataType: 'NOTES',
      },
    ],
  },
];
