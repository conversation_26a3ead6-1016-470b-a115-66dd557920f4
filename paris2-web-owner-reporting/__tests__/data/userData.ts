import {OWNER_REPORTING_ROLES} from '../../src/constants/roles';

export const vesselAccountantRoleConfig = {
  exp: **********,
  iat: **********,
  auth_time: **********,
  jti: '79e6a0d0-bbc3-495c-9a99-************',
  iss: 'https://auth-dev.fleetship.com/auth/realms/paris2',
  aud: 'account',
  sub: '505bf12f-57c7-46ac-8133-d12810e73db7',
  typ: 'Bearer',
  azp: 'paris2-app',
  nonce: '7825dbcd-2d55-478e-8be9-78cf595c27ab',
  session_state: '8811b97d-d7a7-460b-909c-f26dbc6622ae',
  acr: '0',
  'allowed-origins': [
    'https://paris2-dev.fleetship.com',
    'https://paris2-base.fl.digidev.co',
    'https://paris2-qa.fleetship.com',
    'http://localhost:9000',
  ],
  realm_access: {
    roles: [
      'vessel|financial|view',
      'vessel|financial|manage',
      'vessel|financial|upload-approved-budget',
      'vessel|financial|delete-document',
    ],
  },
  resource_access: {
    account: {
      roles: ['manage-account', 'manage-account-links', 'view-profile'],
    },
  },
  scope: 'openid profile email',
  user_name_hash: '8/tSKhrAPqWSoPSZvJKvrXXWcOcSKXAg10yYSI5rvuM=',
  email_verified: true,
  is_nova_onboarded: true,
  user_id: '505bf12f-57c7-46ac-8133-d12810e73db7',
  name: 'Testing ',
  tc_nova_version: 14,
  is_user_onboarded: true,
  preferred_username: '<EMAIL>',
  given_name: 'Test',
  family_name: '2',
  email: '<EMAIL>',
  group: [
    '/Department/IT/Admin',
    '/Department/IT/DevTeam',
    '/Fleet',
    '/Department/Fleet Personnel/Management',
    '/Department/Fleet Personnel/Travel',
  ],
};

export const userRoleConfig = {
  exp: **********,
  iat: **********,
  auth_time: **********,
  jti: '79e6a0d0-bbc3-495c-9a99-************',
  iss: 'https://auth-dev.fleetship.com/auth/realms/paris2',
  aud: 'account',
  sub: '505bf12f-57c7-46ac-8133-d12810e73db7',
  typ: 'Bearer',
  azp: 'paris2-app',
  nonce: '7825dbcd-2d55-478e-8be9-78cf595c27ab',
  session_state: '8811b97d-d7a7-460b-909c-f26dbc6622ae',
  acr: '0',
  'allowed-origins': [
    'https://paris2-dev.fleetship.com',
    'https://paris2-base.fl.digidev.co',
    'https://paris2-qa.fleetship.com',
    'http://localhost:9000',
  ],
  realm_access: {
    roles: [
      'vessel|financial|view',
      'vessel|financial|manage',
      OWNER_REPORTING_ROLES.CAN_COMMENT,
      OWNER_REPORTING_ROLES.CAN_DELETE_COMMENT,
      OWNER_REPORTING_ROLES.CAN_MODIFY_COMMENT,
      OWNER_REPORTING_ROLES.CAN_ACTIONABLE_LINE_ITEM,
      OWNER_REPORTING_ROLES.CAN_EXPORT,
    ],
  },
  resource_access: {
    account: {
      roles: ['manage-account', 'manage-account-links', 'view-profile'],
    },
  },
  scope: 'openid profile email',
  user_name_hash: '8/tSKhrAPqWSoPSZvJKvrXXWcOcSKXAg10yYSI5rvuM=',
  email_verified: true,
  is_nova_onboarded: true,
  user_id: '505bf12f-57c7-46ac-8133-d12810e73db7-12',
  name: 'Testing ',
  tc_nova_version: 14,
  is_user_onboarded: true,
  preferred_username: '<EMAIL>',
  given_name: 'Test',
  family_name: '2',
  email: '<EMAIL>',
  group: [
    '/Department/IT/Admin',
    '/Department/IT/DevTeam',
    '/Fleet',
    '/Department/Fleet Personnel/Management',
    '/Department/Fleet Personnel/Travel',
  ],
};

export const ownerRoleConfig = {
  exp: **********,
  iat: **********,
  auth_time: **********,
  jti: '79e6a0d0-bbc3-495c-9a99-************',
  iss: 'https://auth-dev.fleetship.com/auth/realms/paris2',
  aud: 'account',
  sub: '505bf12f-57c7-46ac-8133-d12810e73db7',
  typ: 'Bearer',
  azp: 'paris2-app',
  nonce: '7825dbcd-2d55-478e-8be9-78cf595c27ab',
  session_state: '8811b97d-d7a7-460b-909c-f26dbc6622ae',
  acr: '0',
  'allowed-origins': [
    'https://paris2-dev.fleetship.com',
    'https://paris2-base.fl.digidev.co',
    'https://paris2-qa.fleetship.com',
    'http://localhost:9000',
  ],
  realm_access: {
    roles: ['vessel|financial|view', 'vessel|financial|manage'],
  },
  resource_access: {
    account: {
      roles: ['manage-account', 'manage-account-links', 'view-profile'],
    },
  },
  scope: 'openid profile email',
  user_name_hash: '8/tSKhrAPqWSoPSZvJKvrXXWcOcSKXAg10yYSI5rvuM=',
  email_verified: true,
  is_nova_onboarded: true,
  user_id: '505bf12f-57c7-46ac-8133-d12810e73db7',
  name: 'Testing ',
  tc_nova_version: 14,
  is_user_onboarded: true,
  preferred_username: '<EMAIL>',
  given_name: 'Test',
  family_name: '2',
  email: '<EMAIL>',
  group: [
    '/Department/IT/Admin',
    '/Department/IT/DevTeam',
    '/Fleet',
    '/Department/Fleet Personnel/Management',
    '/Department/Fleet Personnel/Travel',
  ],
};

export const mockNotesData = [
  {
    text: 'ebebb',
    node: {
      pageIndex: 0,
      left: '39.***************%',
      top: '13.**************%',
      width: '11.***************%',
      height: '7.***************%',
      id: '97a63a73-ba9b-4b73-bf22-f8e0374929a7',
    },
    highlightedAreaId: '97a63a73-ba9b-4b73-bf22-f8e0374929a7',
  },
  {
    text: 'notes 2',
    node: {
      pageIndex: 0,
      left: '12.066472509255616%',
      top: '31.40712662339519%',
      width: '11.***************%',
      height: '7.***************%',
      id: '97a63a72-ba9b-4b73-bf22-f8e0374929a7',
    },
    highlightedAreaId: '97a63a72-ba9b-4b73-bf22-f8e0374929a7',
  },
];
