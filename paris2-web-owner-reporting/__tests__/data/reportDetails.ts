import moment from 'moment';
import {PermissionGroup, ReportDetailsStatusEnum} from '../../src/enums';
import {CFSFormat} from '../../src/enums/owner-preferences.enum';
import {IReportDetails} from '../../src/types';

export const reportDetailsWithStatusOngoing: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: '<PERSON>',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.ON_GOING,
  vesselCode: 2114,
  vesselName: 'Awasan <PERSON>',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.CURRENT_ACCOUNTANT},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
};
export const reportDetailsWithStatusApproved: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: moment().subtract(1, 'months').format('MMMM YYYY'),
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.APPROVED,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.CURRENT_ACCOUNTANT},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
  canCreateNextReport: true,
  vesselStatus: 'pending_handover',
  reportCfsFormat: 2,
};

export const reportDetailsOwnerWithStatusOngoing: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.ON_GOING,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.OWNER},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
  reportCfsFormat: 2,
};

export const reportDetailsWithAsInternalGroup: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.ON_GOING,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.INTERNAL},
  reportCfsFormat: 2,
  preferences: {'CFS:FORMAT': CFSFormat.ACCRUAL},
};

export const reportDetailsWithStatusClosed: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.VIEW_ONLY,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  reportCfsFormat: 2,
  permissions: {group: PermissionGroup.INTERNAL},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
};

export const reportDetailsWithVisibilty: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.ON_GOING,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 3,
  vesselId: 1001,
  reportCfsFormat: 2,
  permissions: {group: PermissionGroup.CURRENT_ACCOUNTANT},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
};

export const mockDisabledReportRowsWithCounts = {
  BS: 0,
  'CFS-FLEET': 0,
  'CFS-ACCRUAL': 0,
  OC: 0,
  TB: 0,
  VA: 0,
};

export const mockEnabledReportRowsWithCounts = {
  'CFS-FLEET': 0,
  'CFS-ACCRUAL': 3,
  BS: 1,
  OC: 4,
  TB: 1,
  VA: 1,
};

export const reportDetailsWithStatusFrozen: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: 'January 2023',
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.FROZEN,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.CURRENT_ACCOUNTANT},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
  reportCfsFormat: 2,
  frozenOn: '2023-01-31T00:00:00.000Z',
};

export const reportDetailsWithStatusUnderReview: IReportDetails = {
  accountantEmail: '<EMAIL>',
  accountantName: 'Jackie Chui',
  ownerName: null,
  createdBy: '0',
  createdOn: '2023-06-08T08:16:58.796Z',
  deleted: false,
  deletedBy: null,
  deletedOn: null,
  endDate: '2023-01-31T00:00:00.000Z',
  id: 2996,
  modifiedBy: null,
  modifiedOn: '2023-06-08T12:00:53.068Z',
  name: moment().subtract(1, 'months').format('MMMM'),
  period: 'Jan-23',
  registeredOwnerName: 'Eight River Shipping S.A.',
  startDate: '2023-01-07T18:30:00.000Z',
  status: ReportDetailsStatusEnum.UNDER_REVIEW,
  vesselCode: 2114,
  vesselName: 'Awasan Pioneer',
  visibility: 0,
  vesselId: 1001,
  permissions: {group: PermissionGroup.CURRENT_ACCOUNTANT},
  preferences: {'CFS:FORMAT': CFSFormat.FLEET},
  canCreateNextReport: true,
  reportCfsFormat: 2,
  vesselStatus: 'pending_handover',
};
export const mockReportHistoryBothApprovalStarted = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 1,
    reportId: 2996,
    status: 'UNDER_REVIEW',
    comment: 'test',
  },
];
export const mockReportHistoryBothApproved = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 1,
    reportId: 2996,
    status: 'UNDER_REVIEW',
    comment: 'test',
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 2,
    reportId: 2996,
    status: 'APPROVED_BY_TECH_TEAM',
    comment: 'test',
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 3,
    reportId: 2996,
    status: 'APPROVED_BY_MANNING_TEAM',
    comment: 'test',
  },
];
export const mockReportHistoryReportSubmitted = [
  ...mockReportHistoryBothApproved,
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 5,
    reportId: 2996,
    status: 'SUBMITTED',
    comment: 'test',
  },
];
export const mockReportHistoryOneRejected = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 1,
    reportId: 2996,
    status: 'UNDER_REVIEW',
    comment: 'test',
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 2,
    reportId: 2996,
    status: 'APPROVED_BY_TECH_TEAM',
    comment: 'test',
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2024-11-28T07:57:11.776Z',
    modifiedOn: '2024-11-28T07:57:11.776Z',
    createdBy: 'test',
    modifiedBy: 'test',
    id: 3,
    reportId: 2996,
    status: 'REJECTED_BY_MANNING_TEAM',
    comment: 'test',
  },
];
