export const keycloasOwnerDetails = [
  {
    id: '630e61e9-e499-452a-b6b5',
    email: '<EMAIL>',
    first_name: 'ac',
    last_name: 'ab',
    username: '<EMAIL>',
  },
  {
    id: '9e7e0aa3-14fd---ce3be3828428',
    email: '<EMAIL>',
    first_name: 'Owner',
    last_name: 'Mail',
    username: '<EMAIL>',
  },
];

export const keycloakOwnerAllAttribute = [
  {
    name: 'financialOwnerReportingAccess',
    value: 'deny',
    user_id: keycloasOwnerDetails[0].id,
    id: '3dcb846d-b1e7-431e-886b-237768814932',
  },
  {
    name: 'financialOwnerReportingAccess',
    value: 'allow',
    user_id: keycloasOwnerDetails[1].id,
    id: '9797eb3c-977b-4d8d-b815-87bf8e376983',
  },
  {
    name: 'financialOwnerReportingAccess',
    value: 'allow',
    user_id: '234j3l-randomm-id',
    id: '2-977b-4d8d-b815-87bf8e376983',
  },
];

export const keycloakUserDetails = [
  {
    id: '0d750eee-b440-274051a4127d',
    full_name: 'test Senior',
    email: null,
    username: 'test senior',
    first_name: 'test',
    last_name: 'Senior',
    attributes: {},
  },
  {
    id: '7930edb9-432c-884b-b1697ea4206c',
    full_name: 'Test 34',
    email: '<EMAIL>',
    username: 'test34',
    first_name: 'Test',
    last_name: '34',
    attributes: {},
  },
  {
    id: '5sdgfd-2352d',
    full_name: 'Test 12',
    email: '<EMAIL>',
    username: 'test.12',
    first_name: 'Test',
    last_name: '12',
    attributes: {},
  },
  {
    id: 'cd5fa49f-45bd-4e3a--1c981b226fe3',
    full_name: 'test.36 ',
    email: '<EMAIL>',
    username: 'test36',
    first_name: 'test36',
    last_name: null,
    attributes: {},
  },
  {
    id: 'dfa6b9a9-0',
    full_name: 'Test 14',
    email: '<EMAIL>',
    username: 'test14',
    first_name: 'test',
    last_name: '14',
    attributes: {},
  },
  {
    id: 'f5f7b28f-a813-767fc0f9aa71',
    full_name: 'Test Kumar',
    email: '<EMAIL>',
    username: 'testKumar',
    first_name: 'Test',
    last_name: 'Kumar',
    attributes: {},
  },
] as const;
