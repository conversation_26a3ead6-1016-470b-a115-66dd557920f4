import UserRoleController from '../../src/controller/user-role-controller';

export const ownerReportingAllTrue: ReturnType<
  UserRoleController['getConfig']
>['ownerReporting'] = {
  view: true,
  manage: true,
  uploadFileApprBdgt: true,
  canDeleteDocument: true,
  canSaveDoc: true,
  canUploadDoc: true,
  canComment: true,
  canDeleteComment: true,
  canModifyComment: true,
  canExport: true,
  canPublish: true,
  canSetOwnerPref: true,
  canRefresh: true,
  canEditDoc: true,
  canHighlightDoc: true,
};
