import {ChannelType} from '../../src/enums';
import {userRoleConfig} from './userData';

export const mockViewCommentList = [
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-07-20T13:01:39.850Z',
    modifiedOn: '2023-07-20T13:01:39.850Z',
    createdBy: userRoleConfig.user_id,
    modifiedBy: userRoleConfig.user_id,
    id: 2,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-MAR-12',
    subject: 'test',
    message: 'message content - 1',
    meta: {},
    channelType: ChannelType.INTERNAL,
    status: 0,
    fromUser: 0,
    parentCommentId: 0,
    rootCommentId: 0,
    path: '0.2',
    reportId: 20,
    replies: [
      {
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2023-07-20T13:01:59.644Z',
        modifiedOn: '2023-07-24T13:15:25.380Z',
        createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        id: 5,
        reportType: 'CFS-ACCRUAL',
        fmlId: 'ASFDHJJHF-1001-MAR-12',
        subject: 'test',
        message: 'reply thread message content - 4',
        meta: {},
        channelType: ChannelType.INTERNAL,
        status: 0,
        fromUser: 0,
        parentCommentId: 0,
        rootCommentId: 0,
        path: '0.2.5',
        reportId: 20,
        replies: [],
        repliesCount: 0,
      },
      {
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2023-07-24T14:28:10.366Z',
        modifiedOn: '2023-07-24T14:28:10.366Z',
        createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        id: 11,
        reportType: 'CFS-ACCRUAL',
        fmlId: 'ASFDHJJHF-1001-MAR-12',
        subject: 'string',
        message: 'reply thread string',
        meta: {},
        channelType: ChannelType.OWNER,
        status: 0,
        fromUser: 0,
        parentCommentId: 2,
        rootCommentId: 0,
        path: '0.2.11',
        reportId: 20,
        replies: [],
        repliesCount: 0,
      },
    ],
    repliesCount: 2,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-07-20T13:01:42.647Z',
    modifiedOn: '2023-07-20T13:01:42.648Z',
    createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
    id: 3,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-MAR-12',
    subject: 'test',
    message: 'message content - 2',
    meta: {},
    channelType: ChannelType.INTERNAL,
    status: 1,
    fromUser: 0,
    parentCommentId: 0,
    rootCommentId: 0,
    path: '0.3',
    reportId: 20,
    replies: [
      {
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2023-07-24T14:28:17.734Z',
        modifiedOn: '2023-07-24T14:28:17.734Z',
        createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        id: 12,
        reportType: 'CFS-ACCRUAL',
        fmlId: 'ASFDHJJHF-1001-MAR-12',
        subject: 'string',
        message: 'reply thread string',
        meta: {},
        channelType: ChannelType.OWNER,
        status: 0,
        fromUser: 0,
        parentCommentId: 3,
        rootCommentId: 0,
        path: '0.3.12',
        reportId: 20,
        replies: [],
        repliesCount: 0,
      },
      {
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2023-07-24T14:28:18.168Z',
        modifiedOn: '2023-07-24T14:28:18.169Z',
        createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        id: 13,
        reportType: 'CFS-ACCRUAL',
        fmlId: 'ASFDHJJHF-1001-MAR-12',
        subject: 'string',
        message: 'reply thread string',
        meta: {},
        channelType: ChannelType.OWNER,
        status: 0,
        fromUser: 0,
        parentCommentId: 3,
        rootCommentId: 0,
        path: '0.3.13',
        reportId: 20,
        replies: [],
        repliesCount: 0,
      },
      {
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2023-07-24T14:28:18.959Z',
        modifiedOn: '2023-07-24T14:28:18.959Z',
        createdBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        modifiedBy: '7930edb9-92c0-432c-884b-b1697ea4206c',
        id: 14,
        reportType: 'CFS-ACCRUAL',
        fmlId: 'ASFDHJJHF-1001-MAR-12',
        subject: 'string',
        message: 'reply thread string',
        meta: {},
        channelType: ChannelType.OWNER,
        status: 0,
        fromUser: 0,
        parentCommentId: 3,
        rootCommentId: 0,
        path: '0.3.14',
        reportId: 20,
        replies: [],
        repliesCount: 0,
      },
    ],
    repliesCount: 3,
  },
  {
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2023-07-31T09:37:48.902Z',
    modifiedOn: '2023-07-31T09:37:48.902Z',
    createdBy: userRoleConfig.user_id,
    modifiedBy: userRoleConfig.user_id,
    id: 4,
    reportType: 'CFS-ACCRUAL',
    fmlId: 'ASFDHJJHF-1001-MAR-12',
    subject: 'test',
    message:
      '@[Test User](32532-23-432c-1235312512fg-b1697ea4206c)' +
      Array.from({length: 3000}, () =>
        String.fromCodePoint(Math.floor(Math.random() * 26)),
      ).join(''),
    meta: {},
    channelType: ChannelType.OWNER,
    status: 0,
    fromUser: 0,
    parentCommentId: 0,
    rootCommentId: 0,
    path: '0.3',
    reportId: 20,
    replies: [],
    repliesCount: 0,
  },
];
