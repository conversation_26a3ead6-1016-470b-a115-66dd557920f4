const mockAxiosInstance = {
  get: jest.fn(() =>
    Promise.resolve({
      data: {},
      status: 300,
    }),
  ),
  post: jest.fn(),
  delete: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  interceptors: {
    request: {
      use: jest.fn(),
    },
    response: {use: jest.fn()},
  },
  getUri: jest.fn(({url}: {url: string}) => url),
};

module.exports = {
  ...mockAxiosInstance,
  create: jest.fn(() => mockAxiosInstance),
};
