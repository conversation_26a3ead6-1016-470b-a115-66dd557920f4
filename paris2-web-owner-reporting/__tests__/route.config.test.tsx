import routesConfig from '../src/routes/route.config';
import {FinancialReport} from '../src/pages';
import {UserRoleControllerConfig} from '../src/types';
describe('routesConfig', () => {
  let roleConfig: UserRoleControllerConfig;
  beforeEach(() => {
    // Define a base role configuration
    roleConfig = {
      ownerReporting: {
        hasPermision: false,
      },
      isOwner: false,
    };
  });
  it('should return the correct route path', () => {
    const routes = routesConfig(roleConfig);
    expect(routes[0].path).toBe('owner-reporting/:vesselId/reports/:reportId/');
  });
  it('should include the FinancialReport component for the route', () => {
    const routes = routesConfig(roleConfig);
    expect(routes[0].component).toBe(FinancialReport);
  });
  it('should set isPermission based on roleConfig.ownerReporting.hasPermision', () => {
    roleConfig.ownerReporting.hasPermision = true;
    const routes = routesConfig(roleConfig);
    expect(routes[0].isPermission).toBe(true);
  });
  it('should set isOwner based on roleConfig.isOwner', () => {
    roleConfig.isOwner = true;
    const routes = routesConfig(roleConfig);
    expect(routes[0].isOwner).toBe(true);
  });
  it('should handle when isPermission is false', () => {
    roleConfig.ownerReporting.hasPermision = false;
    const routes = routesConfig(roleConfig);
    expect(routes[0].isPermission).toBe(false);
  });
  it('should handle when isOwner is false', () => {
    roleConfig.isOwner = false;
    const routes = routesConfig(roleConfig);
    expect(routes[0].isOwner).toBe(false);
  });
});
