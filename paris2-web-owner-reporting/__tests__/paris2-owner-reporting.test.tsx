import React from 'react';
import {render} from '@testing-library/react';
import singleSpaReact from 'single-spa-react';
import {Mock} from 'jest-mock';
import * as ReactDOMClient from 'react-dom/client';
import {bootstrap, mount, unmount} from '../src/paris2-owner-reporting';
import Root from '../src/root.component';

jest.mock('single-spa-react', () => {
  const mockSingleSpaReact = jest.fn(() => ({
    errorBoundary: jest.fn((err, info, props) => (
      <div>Something went wrong!</div>
    )),
  }));
  return mockSingleSpaReact;
});

jest.mock('react-dom/client', () => ({
  createRoot: jest.fn(() => ({
    render: jest.fn(),
    unmount: jest.fn(),
  })),
}));
jest.mock('../src/root.component', () =>
  jest.fn(() => <div>Mock Root Component</div>),
);
jest.mock('../src/paris2-owner-reporting', () => ({
  bootstrap: jest.fn(),
  mount: jest.fn(),
  unmount: jest.fn(),
}));

describe('paris2-owner-reporting lifecycles', () => {
  it('should export bootstrap, mount, and unmount lifecycle functions', () => {
    expect(bootstrap).toBeDefined();
    expect(mount).toBeDefined();
    expect(unmount).toBeDefined();
  });
});
