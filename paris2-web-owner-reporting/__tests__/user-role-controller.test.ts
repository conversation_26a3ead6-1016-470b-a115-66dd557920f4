import UserRoleController from '../src/controller/user-role-controller';
import {ROLES} from '../src/constants';
import {JwtUser} from '../src/types/user';
describe('UserRoleController', () => {
  let userRoleController: UserRoleController;
  let kc: any;
  beforeEach(() => {
    userRoleController = new UserRoleController();
    kc = {
      realmAccess: {
        roles: [],
      },
      tokenParsed: {
        ship_party_id: null,
        financialOwnerReportingAccess: null,
        accessible_ship_party_ids: [],
      },
    };
  });
  it('should return correct config for owner role', () => {
    kc.realmAccess.roles.push(ROLES.OWNER_REPORTING_ROLES.VIEW);
    kc.tokenParsed.ship_party_id = '123';
    kc.tokenParsed.financialOwnerReportingAccess = 'allow';
    const config = userRoleController.getConfig(kc);
    expect(config.isOwner).toBe(true);
    expect(config.ownerReporting.hasPermision).toBe(true);
    expect(config.ownerReporting.view).toBe(true);
  });
  it('should return false for isOwner when ship_party_id is null', () => {
    const config = userRoleController.getConfig(kc);
    expect(config.isOwner).toBe(false);
    expect(config.ownerReporting.hasPermision).toBe(false);
    expect(config.ownerReporting.view).toBe(false);
  });
  it('should return correct config for non-owner user with view permission', () => {
    kc.realmAccess.roles.push(ROLES.OWNER_REPORTING_ROLES.VIEW);
    const config = userRoleController.getConfig(kc);
    expect(config.isOwner).toBe(false);
    expect(config.ownerReporting.view).toBe(true);
    expect(config.ownerReporting.hasPermision).toBe(true); // canView is true
  });
  it('should set isGBOUser correctly when accessible_ship_party_ids is not empty', () => {
    kc.tokenParsed.accessible_ship_party_ids = ['123'];
    kc.tokenParsed.ship_party_id = '123';
    const config = userRoleController.getConfig(kc);
    expect(config.isGBOUser).toBe('123');
  });
  it('should return null for isGBOUser when accessible_ship_party_ids is empty', () => {
    const config = userRoleController.getConfig(kc);
    expect(config.isGBOUser).toBe(null);
  });
  it('should return false for permissions when user does not have roles', () => {
    const config = userRoleController.getConfig(kc);
    expect(config.ownerReporting.view).toBe(false);
    expect(config.ownerReporting.manage).toBe(false);
    expect(config.ownerReporting.uploadFileApprBdgt).toBe(false);
    expect(config.ownerReporting.canDeleteDocument).toBe(false);
  });
  it('should return true for manage role when the user has the manage role', () => {
    kc.realmAccess.roles.push(ROLES.OWNER_REPORTING_ROLES.MANAGE);
    const config = userRoleController.getConfig(kc);
    expect(config.ownerReporting.manage).toBe(true);
  });
  it('should correctly determine hasVAAccess', () => {
    kc.realmAccess.roles.push(
      ROLES.OWNER_REPORTING_ROLES.HAS_ALL_VESSEL_ACCESS,
    );
    const config = userRoleController.getConfig(kc);
    expect(config.hasVAAccess).toBe(true);
  });
  it('should set permissions for document-related roles', () => {
    kc.realmAccess.roles.push(
      ROLES.OWNER_REPORTING_ROLES.CAN_UPLOAD_DOC,
      ROLES.OWNER_REPORTING_ROLES.CAN_EDIT_DOC,
      ROLES.OWNER_REPORTING_ROLES.CAN_COMMENT,
    );
    const config = userRoleController.getConfig(kc);
    expect(config.ownerReporting.canUploadDoc).toBe(true);
    expect(config.ownerReporting.canEditDoc).toBe(true);
    expect(config.ownerReporting.canComment).toBe(true);
  });
  it('should not set permissions if roles are not available', () => {
    const config = userRoleController.getConfig(kc);
    expect(config.ownerReporting.canUploadDoc).toBe(false);
    expect(config.ownerReporting.canEditDoc).toBe(false);
    expect(config.ownerReporting.canComment).toBe(false);
  });
});
