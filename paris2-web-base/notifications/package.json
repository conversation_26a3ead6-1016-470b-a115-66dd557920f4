{"name": "paris2-notifications", "repository": "*****************:fleetshipteam/paris2-web-base.git", "version": "1.0.0", "description": "notifications page of the paris 2.0 system", "main": "dist/paris2-notifications.js", "author": "Fleet Management Limited", "license": "ISC", "scripts": {"lint": "eslint src", "start": "webpack-dev-server --mode=development --port 9003 --server-type https", "test": "jest", "test:watch": "jest --watch", "build": "webpack --mode=production", "deploy": "./deploy.sh", "analyze": "webpack --mode=production --env.analyze=true", "prettier": "prettier --write './**'", "watch-tests": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"i18next": "^19.3.4", "i18next-browser-languagedetector": "^4.0.2", "moment": "^2.29.4", "single-spa-react": "^6.0.1"}, "peerDependencies": {"react": "18.x", "react-bootstrap": "1.x", "prop-types": "15.x", "react-dom": "18.x"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@babel/preset-react": "^7.9.1", "@babel/runtime": "^7.9.2", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "autoprefixer": "9.7.4", "babel-core": "6.26.3", "babel-eslint": "^11.0.0-beta.2", "babel-jest": "^25.5.1", "babel-loader": "^8.1.0", "babel-plugin-styled-components": "^1.10.7", "bootstrap": "^4.4.1", "clean-webpack-plugin": "3.0.0", "concurrently": "^5.1.0", "css-loader": "^3.5.3", "eslint": "^6.8.0", "eslint-plugin-react": "^7.20.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "kremling-loader": "^1.0.2", "postcss-loader": "3.0.0", "prettier": "^2.0.1", "pretty-quick": "^2.0.1", "react": "^18.2.0", "react-bootstrap": "^1.0.0", "react-dom": "^18.2.0", "react-i18next": "^11.3.4", "react-router-dom": "^6.22.0", "react-test-renderer": "^18.2.0", "sass": "^1.26.3", "sass-loader": "^8.0.2", "style-loader": "^1.1.3", "styled-components": "^5.1.0", "svg-url-loader": "^5.0.0", "systemjs-webpack-interop": "^2.0.0", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-config-single-spa-react": "4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}}