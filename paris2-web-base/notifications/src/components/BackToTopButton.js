import React, { useState, useEffect } from 'react';

import styleGuide from '../styleGuide';
const { Icon } = styleGuide;

const ScrollArrow = () => {
  const [showScroll, setShowScroll] = useState(false);

  const checkScrollTop = () => {
    if (!showScroll && window.pageYOffset > 400) {
      setShowScroll(true);
    } else if (showScroll && window.pageYOffset <= 400) {
      setShowScroll(false);
    }
  };
  const scrollTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  window.addEventListener('scroll', checkScrollTop);

  return (
    <div className="back-to-top">
      <Icon
        icon="up-filled"
        size={40}
        onClick={scrollTop}
        style={{ display: showScroll ? 'flex' : 'none' }}
      />
      <span 
      tabIndex={0}
        onClick={scrollTop}
        onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              scrollTop();
            }
          }} 
        style={{ display: showScroll ? 'flex' : 'none' }}>
        Back to top
      </span>
    </div>
  );
};

export default ScrollArrow;
