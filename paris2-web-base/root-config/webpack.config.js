/* eslint-disable no-undef */
const path = require('path');
const webpack = require('webpack');
const CopyPlugin = require('copy-webpack-plugin');

const { ENV = 'dev' } = process.env;
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });

const FaviconsWebpackPlugin = require('favicons-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { rules } = require('eslint-config-prettier');

const {
    BASE_URL,
    SCRIPT_BASE_URL,
    NPM_CDN_PATH,
    AUTH_SERVER_URL,
    GA_MEASUREMENT_ID,
    GA_CODE,
    UA_CODE,
    GTM_CODE,
} = process.env;

const baseCspRules = require('./csp-rules/base.json');
const envCspRules = require(`../csp.json`);

const cspRules = Object.entries(baseCspRules).reduce((m, [key, rules]) => {
    return {
        ...m,
        [key]: rules.concat(envCspRules[key] ? envCspRules[key] : []),
    };
}, {});

const CSP_RULES = Object.entries(cspRules).reduce((str, [key, rules], index) => {
    if (index === 0) return `${key} ${rules.join(' ')}`;
    return `${str};${key} ${rules.join(' ')}`;
}, '');

module.exports = env => ({
    entry: path.resolve(__dirname, 'src/root-config'),
    output: {
        filename: 'root-config.js',
        libraryTarget: 'system',
        path: path.resolve(__dirname, 'dist', ENV),
    },
    devtool: 'source-map',
    module: {
        rules: [
            { parser: { system: false } },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: [{ loader: 'babel-loader' }],
            },
        ],
    },
    devServer: {
        historyApiFallback: {
            index: 'index.html',
        },
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        allowedHosts: 'all',
    },
    plugins: [
        new HtmlWebpackPlugin({
            inject: false,
            template: 'src/index.ejs',
            favicon: 'src/assets/favicon/favicon.ico',
            templateParameters: {
                isLocal: env && env.isLocal === 'true',
                BASE_URL,
                SCRIPT_BASE_URL,
                NPM_CDN_PATH,
                CSP_RULES,
            },
        }),
        new FaviconsWebpackPlugin('./src/assets/favicon/apple-touch-icon.png'),
        new CopyPlugin({
            patterns: [
                { from: 'src/index.js', to: path.resolve(__dirname, 'dist', ENV) },
                { from: 'src/robots.txt', to: path.resolve(__dirname, 'dist', ENV) },
            ],
        }),
        new CleanWebpackPlugin(),
        new webpack.DefinePlugin({
            'process.env': {
                AUTH_SERVER_URL: JSON.stringify(AUTH_SERVER_URL || ''),
                GA_MEASUREMENT_ID: JSON.stringify(GA_MEASUREMENT_ID || ''),
                GA_CODE: JSON.stringify(GA_CODE || ''),
                UA_CODE: JSON.stringify(UA_CODE || ''),
                GTM_CODE: JSON.stringify(GTM_CODE || ''),
            },
        }),
    ],
    externals: [
        'single-spa',
        /^@paris2\/.+$/,
        // 'styled-component',
        // /^react-i18next\/?.*$/,
        // /^react-dom\/?.*$/,
        // /^react-bootstrap\/?.*$/,
        // /^react\/lib.*/,
        // /^axios\/?.*$/,
    ],
});
