{"name": "paris2-root-config", "scripts": {"start": "webpack-dev-server --mode=development --port 9000 --server-type https", "lint": "eslint src", "test": "jest", "prettier": "prettier --write './**'", "build": "webpack --mode=production"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.7.4", "@babel/runtime": "^7.8.7", "@types/react": "^18.2.55", "@types/systemjs": "^6.1.0", "babel-eslint": "^11.0.0-beta.2", "babel-loader": "^8.0.6", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "dotenv": "^8.6.0", "eslint": "^6.7.2", "eslint-config-important-stuff": "^1.1.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-prettier": "^3.1.1", "favicons-webpack-plugin": "^6.0.0", "ga-4-react": "^0.1.281", "html-webpack-plugin": "^5.5.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "react": "^18.2", "serve": "^11.2.0", "webpack": "^5.79.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.13.3"}, "dependencies": {"history": "^4.10.1", "single-spa": "^6.0.0"}}