
const { defaults } = require('jest-config');

module.exports = {
  rootDir: "src",

  modulePaths: ["<rootDir>"],
  moduleFileExtensions: [...defaults.moduleFileExtensions, 'ts', 'tsx'],
  transform: {
    '^.+\\.vue$': 'babel-jest',
    '.+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2|svg|gif)$': 'jest-transform-stub',
    '^.+\\.(js|jsx)?$': 'babel-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1'
  },

  transformIgnorePatterns: ['faq/node_modules/'],
  modulePaths: ["src", "test"],
  setupFilesAfterEnv: ["<rootDir>/setupTests.js"],

  "collectCoverageFrom": [
    "**/*.{js,jsx}",
    "!**/node_modules/**",
    "!**/vendor/**"
  ]
};