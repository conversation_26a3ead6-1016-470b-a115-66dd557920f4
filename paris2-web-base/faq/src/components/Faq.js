/* eslint-disable react/prop-types */
/* eslint-disable no-restricted-globals */
import React, { useEffect, useState } from "react";
import { Container } from "react-bootstrap";
import "../scss/faq.scss";
import { Divider } from "./Divider";
import styleGuide from "../styleGuide";
import { useHistory } from "react-router";
import parse from "html-react-parser";
import * as sharePointService from "../services/sharepoint-service";
import { parseHTML } from "../utils/sharepoint";
const { Icon } = styleGuide;

const { PARIS_TWO_HOST } = process.env;

const TITLE_MAP = {
  faq: "Registration Guide",
  "terms-and-conditions": "Terms and Conditions",
  "registration-guide-manning-agents": "Registration Guide for Manning Agents"
};

const Faq = () => {
  const pathname = window.location.pathname.substr(1);

  const history = useHistory();
  const [faqContent, setFaqContent] = useState(null);
  const handleGoBack = () => {
    if (history.action === "POP") {
      window.open(PARIS_TWO_HOST, "_parent");
      return;
    }
    window.open(window?.location?.href?.split("/faq")[0], "_self");
  };

  useEffect(() => {
    const getFaqContent = async pagePath => {
      const value = await sharePointService.getSharepointData(pagePath);
      const richText = parse(value.data.data.CanvasContent1);
      if (richText?.props?.children) {
        parseHTML(richText?.props?.children);
      }
      setFaqContent(richText);
    };

    getFaqContent(pathname);
  }, [setFaqContent]);

  return (
    <Container fluid style={{ paddingTop: "20px" }} className="faq-wrapper">
      <div className="faq-heading">
        <span className="faq-title">{TITLE_MAP[pathname]}</span>
        <div role="button" aria-hidden="true" onClick={handleGoBack}>
          <Icon style={{ cursor: "pointer" }} icon="close" size={20} />
        </div>
      </div>
      <Divider height="0.25" />
      <hr />
      {faqContent}
    </Container>
  );
};

export { Faq };
