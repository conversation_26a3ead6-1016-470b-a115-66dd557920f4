import parse from "html-react-parser";

function parseHTML(data) {
  if (data?.length) {
    data.forEach((v, i) => {
      const secondLevel = v.props.children;
      if (
        secondLevel?.props?.children &&
        secondLevel?.props?.children.length == 2 &&
        secondLevel.props["data-sp-webpartdata"]
      ) {
        const webpartData = JSON.parse(
          secondLevel.props["data-sp-webpartdata"]
        );
        if (
          webpartData &&
          webpartData?.title
            .toLocaleLowerCase()
            .trim()
            .includes("modern script editor")
        ) {
          const imageTag = parse(webpartData?.properties?.script);
          const updatedSecondLevel = {
            ...secondLevel.props.children[1],
            props: { "data-sp-htmlproperties": "", children: imageTag }
          };
          secondLevel.props.children[0] = null;
          secondLevel.props.children[1] = updatedSecondLevel;
        }
      }
    });
  }
}

export { parseHTML };
