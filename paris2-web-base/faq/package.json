{"name": "paris2-faqs", "repository": "*****************:fleetshipteam/paris2-web-base.git", "version": "1.0.0", "description": "faqs page of the paris 2.0 system", "main": "dist/paris2-faqs.js", "author": "Fleet Management Limited", "license": "ISC", "scripts": {"lint": "eslint src", "start": "webpack-dev-server --mode=development --port 9004 --server-type https", "test": "jest", "test:watch": "jest --watch", "build": "webpack --mode=production", "deploy": "./deploy.sh", "analyze": "webpack --mode=production --env.analyze=true", "prettier": "prettier --write './**'", "watch-tests": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^0.21.4", "history": "^4.9.0", "i18next": "^19.3.4", "i18next-browser-languagedetector": "^4.0.2", "single-spa-react": "^6.0.1"}, "peerDependencies": {"react": "18.x", "react-bootstrap": "1.x", "prop-types": "15.x", "react-dom": "18.x"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@babel/preset-react": "^7.9.1", "@babel/runtime": "^7.9.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "autoprefixer": "9.7.4", "babel-core": "6.26.3", "babel-eslint": "^11.0.0-beta.2", "babel-jest": "^25.5.1", "babel-loader": "^8.1.0", "babel-plugin-styled-components": "^1.10.7", "bootstrap": "^4.4.1", "css-loader": "^3.5.3", "dotenv": "^10.0.0", "eslint": "^6.8.0", "eslint-plugin-react": "^7.32.2", "html-react-parser": "^5.1.6", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-transform-stub": "^2.0.0", "kremling-loader": "^1.0.2", "postcss-loader": "3.0.0", "prettier": "^2.0.1", "pretty-quick": "^2.0.1", "react": "^18.2.0", "react-bootstrap": "^1.0.0", "react-dom": "^18.2.0", "react-i18next": "^11.3.4", "react-router-dom": "^5.1.2", "react-test-renderer": "^18.2.0", "sass": "^1.26.3", "sass-loader": "^8.0.2", "style-loader": "^1.1.3", "styled-components": "^5.1.0", "svg-url-loader": "^5.0.0", "systemjs-webpack-interop": "^2.0.0", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-config-single-spa-react": "4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0"}}