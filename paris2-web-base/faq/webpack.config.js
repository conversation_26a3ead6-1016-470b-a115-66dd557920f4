/* eslint-disable no-undef */
const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");
const path = require('path');
const webpack = require('webpack');

const { ENV = 'dev' } = process.env
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });

const ENV_MAP = Object.entries(process.env).reduce(
  (map, [key, value]) => ({
    ...map,
    [key]: JSON.stringify(value || ''),
  }),
  {},
);

module.exports = (webpackConfigEnv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "paris2",
    projectName: "faq",
    webpackConfigEnv,
  });

  const externals = {
    externals: [
      // @see: what about excluding packages from bundle files?
      // @see: https://webpack.js.org/configuration/externals/#regexp
      // 'single-spa', 
      // /^styled-components\/?.*$/,
      // /^@paris2\/.+$/, 
      /^react-i18next\/?.*$/,
      // Don't bundle react or react-dom
      // /^react-dom\/?.*$/,
      // /^react-bootstrap\/?.*$/,
      // /^react\/lib.*/,
      // /^axios\/?.*$/,
    ]
    // @see: what about excluding packages from bundle files?
    /* || {
        "react": {
            commonjs: "react",
            commonjs2: "react",
            amd: "React",
            root: "React"
        },
        "react-dom": {
            commonjs: "react-dom",
            commonjs2: "react-dom",
            amd: "ReactDOM",
            root: "ReactDOM"
        },
    } */
  };

  return merge(defaultConfig, externals, {
    module: {
      rules: [
        {
          test: /\.s[ac]ss$/i,
          use: [
            // Creates `style` nodes from JS strings
            {
              loader: 'style-loader',
              options: {
                insert: function insertStyle(element) {
                  var parent = document.querySelector('#paris2-inline-style');
                  if (parent) {
                    element.setAttribute('nonce', parent.getAttribute('nonce'));
                    parent.appendChild(element);
                  } else {
                    var head = document.querySelector('head');
                    head.appendChild(element);
                  }
                },
              }
            },
            // Translates CSS into CommonJS
            'css-loader',
            // Compiles Sass to CSS
            'sass-loader',
          ],
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: 'svg-url-loader',
              options: {
                limit: 10000,
              },
            },
          ],
        }
      ]
    }
  },
  {
    plugins: [
      new webpack.DefinePlugin({
        'process.env': ENV_MAP,
      }),
    ],
  },
  {
    devServer: {
      port: 9003,
    },
  });
};