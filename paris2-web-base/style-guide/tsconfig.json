{
    "compilerOptions": {
        "outDir": "./dist/",        // path to output directory
        "sourceMap": true,          // allow sourcemap support
        "strictNullChecks": true,   // enable strict null checks as a best practice
        "module": "es6",            // specify module code generation
        "jsx": "react",             // use typescript to transpile jsx to js
        "target": "es5",            // specify ECMAScript target version
        "allowJs": true,             // allow a partial TypeScript and JavaScript codebase
        "allowSyntheticDefaultImports": true,
        "moduleResolution": "node",
        "resolveJsonModule": true
    },
    "include": [
        "./src/"
    ]
}