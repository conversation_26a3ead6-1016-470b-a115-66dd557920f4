const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");

module.exports = (webpackConfigEnv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "paris2",
    projectName: "styleguide",
    webpackConfigEnv,
  });

  return merge(defaultConfig, {
    resolve: {
      // changed from extensions: [".js", ".jsx"]
      extensions: [".ts", ".tsx", ".js", ".jsx"]
    },
    module: {
      rules: [
        {
          test: /\.s[ac]ss$/i,
          use: [
            // Creates `style` nodes from JS strings
            {
              loader: 'style-loader',
              options: {
                insert: function insertStyle(element) {
                  var parent = document.querySelector('#paris2-inline-style');
                  if (parent) {
                    element.setAttribute('nonce', parent.getAttribute('nonce'));
                    parent.appendChild(element);
                  } else {
                    var head = document.querySelector('head');
                    head.appendChild(element);
                  }
                },
              }
            },
            // Translates CSS into CommonJS
            'css-loader',
            // Compiles Sass to CSS
            'sass-loader',
          ],
        },
        {
          test: /\.(ttf|eot|woff|woff2)$/,
          use: {
              loader: "file-loader",
              options: {
                  name: "[name].[ext]",
                  outputPath: 'fonts',
                  publicPath: '/styleguide/fonts'
              },
          },
        },
        {
          test: /\.(png|jpg|jpeg)/,
          loader: 'file-loader',
          options: {
            name: "[name].[ext]",
            outputPath: 'images',
            publicPath: '/styleguide/images'
          }
        },
        // changed from { test: /\.jsx?$/, use: { loader: 'babel-loader' }, exclude: /node_modules/ },
        { test: /\.(t|j)sx?$/, use: { loader: 'ts-loader' }, exclude: /node_modules/ },
        // addition - add source-map support
        { enforce: "pre", test: /\.js$/, exclude: /node_modules/, loader: "source-map-loader" },
      ],
    },
    // @see: what about excluding packages from bundle files?
    // @see: https://webpack.js.org/configuration/externals/#regexp
    // externals: {
    //   "react": "React",
    //   "react-dom": "ReactDOM",
    // },
    // addition - add source-map support
    devtool: "source-map"
  });
};
