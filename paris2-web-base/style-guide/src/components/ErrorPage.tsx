import React, { useEffect, useState } from 'react';
import '../error-page.scss';
import ErrorImage from '../assets/images/maintenance.png';
import { Col, Container, Image, Row } from 'react-bootstrap';
import userService from '../user-service';

// Hardcoded the content for now it also provides consistency across all microservices
const parseError = (httpStatusCode: number) => {
  switch (httpStatusCode) {
    case 404:
      return {
        code: 404,
        title: 'Page not found',
        caption: 'The page you are trying to find is not available.',
        color: '#0091B8',
      }
    case 403:
      return {
        code: 403,
        title: 'Access is denied',
        caption:
          'You do not have permission to view this page.',
        color: '#D41B56',
          
      }
    default:
      return {
        code: 500,
        title: 'Something went wrong.',
        caption: 'Please contact the maintainer',
      }
  }
}

interface Props {
  errorCode: number;
  customMessage?: string | null;
}

const ErrorPage = ({ errorCode, customMessage }: Props) => {
  const [isAzureAdUpn, setAzureAdUpn] = useState(null);

  useEffect(() => {
    (async () => {
      try {
        const init = await userService.init();
        setAzureAdUpn(init?.keycloak?.tokenParsed?.azure_ad_upn)
      } catch (error) {
        console.log('Oops, something went wrong. Please try again.');
      }
    })();
  }, []);
  
  // We can pass props statusCode as props or as route params then let the errorMapping handle the data or info
  const { code, title, caption, color } = parseError(errorCode);
  return (
    <Container>
      <div className="mt-5" style={{ paddingTop: '2.5%' }} />
      <Row className="justify-content-md-center" style={{ margin: 'auto' }}>
        <Col md={5}>
          <p className="error-title error-msg" style={{ color }}>{code}</p>
          <p className="error-description error-msg" style={{ color }}>{title}</p>
          <p className="error-caption">{customMessage ?? caption}</p>
          {isAzureAdUpn && errorCode === 403 &&
            <div className="link-access-wrapper">
              <p className="wrong-caption">Think this is wrong?</p>
              <a target="_blank" href="https://fleetship.sharepoint.com/sites/PARIS2/SitePages/PARIS-2.0---Access-Rights---FAQ.aspx" className="btn btn-primary btn-sm" role="button" aria-disabled="true">Click Here To Learn More About How To Request Access</a>
            </div>}
        </Col>
        <Col md={5}>
          <div className="mt-5" style={{ paddingTop: '2.5%' }} />
          <Image src={ErrorImage} alt="Fleet-Maintenance-Error" width={590} height={480} />
        </Col>
      </Row>
    </Container>
  );
};

export default ErrorPage;
