import { useCallback } from "react";

function useYupValidationResolver(schema: any) {
  return useCallback(
    async (data) => {
      try {
        const values = await schema.validate(data, {
          abortEarly: false,
        });

        return {
          values,
          errors: {},
        };
      } catch (errors) {
        return {
          values: {},
          errors: errors?.inner?.reduce(
            (
              allErrors: Record<string, unknown>,
              currentError: { path: string; type: string; message: string }
            ) => ({
              ...allErrors,
              [currentError.path]: {
                type: currentError.type ?? "validation",
                message: currentError.message,
              },
            }),
            {}
          ),
        };
      }
    },
    [schema]
  );
}

export { useYupValidationResolver };
