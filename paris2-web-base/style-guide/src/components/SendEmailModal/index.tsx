import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Spinner } from "react-bootstrap";
import { Controller, useForm } from "react-hook-form";
import * as yup from 'yup';
import { useYupValidationResolver } from "./useYupValidationResolver";
import { Option } from "react-bootstrap-typeahead/types/types";
import "./style.scss";
import { DropdownSearchControl } from "../DropdownSearch";

export interface SendEmailPayload {
    selectedRecipients: string[];
    cc: string[] | null;
    message: string;
    subject: string;
}

interface Props {
    show: boolean;
    onClose?: () => void;
    onSubmit: (values: SendEmailPayload) => Promise<void>;
    recipientsList: Option[];
    initialValues?: Partial<SendEmailPayload>;
    generateEmailContent: (recipient: string[]) => string;
}
const validationSchema = yup.object().shape({
    selectedRecipients: yup.array().of(yup.string()).required(),
    message: yup.string().required(),
    cc: yup.array().of(yup.string()).nullable(),
    subject: yup.string().required(),
});

const SendEmailModal = ({
    show,
    onClose,
    onSubmit,
    recipientsList,
    initialValues,
    generateEmailContent
}: Props) => {
    const resolver = useYupValidationResolver(validationSchema);
    const recipientsListLoading = !recipientsList?.length;
    const [isSending, setIsSending] = useState<boolean>(false);
    const {
        control,
        register,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { errors },
    } = useForm<SendEmailPayload>({
        resolver,
        defaultValues: initialValues
    });
    const recipients = watch('selectedRecipients') as unknown as string[] || [];

    useEffect(() => {
        if (recipients && recipients.length > 0) {
            const message = generateEmailContent(recipients);
            setValue('message', message); 
        }
    }, [recipients, setValue]);

    const handleSendEmail = async (values: SendEmailPayload) => {
        try {
            setIsSending(true);
            await onSubmit?.(values)
        } catch (error) {
            console.trace('failed to send email', error);
        }  finally {
            setIsSending(false);
        } 
    }
    return (
        <Modal
            show={show}
            onHide={onClose}
            centered
            dialogClassName="send-email-modal"
        >
            <Form onSubmit={handleSubmit(handleSendEmail)}>
                <Modal.Header className="send-email-header">
                    <Modal.Title className="send-email-title">Send Email</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="email-form">
                        <div className="left-column">
                            <div>
                                <Form.Label className="form-label">Subject *</Form.Label>
                                
                                <Form.Control
                                    type="text"
                                    {...register("subject")}
                                    isInvalid={!!errors.subject}
                                />
                                <Form.Control.Feedback type="invalid">
                                    Please enter subject.
                                </Form.Control.Feedback>
                            </div>
                            <div>
                                <Form.Label className="form-label mt-3">Send To *</Form.Label>
                                <Controller
                                    control={control}
                                    name='selectedRecipients'
                                    render={({ field }) => (
                                        <DropdownSearchControl
                                            options={recipientsList}
                                            isInvalid={!!errors.selectedRecipients}
                                            id='user-list-dropdown'
                                            placeholder='Please select'
                                            multiple
                                            isLoading={recipientsListLoading}
                                            {...field}
                                        />
                                    )}
                                />
                                <Form.Control.Feedback type="invalid">
                                    Please enter recipients.
                                </Form.Control.Feedback>
                            </div>
                            <div>
                                <Form.Label className="form-label mt-3">Cc</Form.Label>
                                <Controller
                                    control={control}
                                    name='cc'
                                    render={({ field }) => (
                                        <DropdownSearchControl
                                            options={recipientsList}
                                            isInvalid={!!errors.cc}
                                            id='cc-user-list-dropdown'
                                            placeholder='Please select'
                                            multiple
                                            isLoading={recipientsListLoading}
                                            {...field}
                                        />
                                    )}
                                />
                            </div>
                        </div>
                        <div className="right-column">
                            <Form.Label className="form-label">Message Content *</Form.Label>
                            <Form.Control
                                type="text"
                                as="textarea"
                                className="message-input"
                                {...register("message")}
                                isInvalid={!!errors.message}
                            />
                            <Form.Control.Feedback type="invalid">
                                Please enter message content.
                            </Form.Control.Feedback>
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        style={{ width: "7.5em" }}
                        variant="primary"
                        onClick={onClose}
                    >
                        Cancel
                    </Button>
                    <Button
                        disabled={isSending}
                        style={{ width: "7.5em" }}
                        variant="secondary"
                        type="submit"
                    >
                        {isSending ? <Spinner animation="border" role="status" size="sm" /> : "Send"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
    );
};

export default SendEmailModal;
