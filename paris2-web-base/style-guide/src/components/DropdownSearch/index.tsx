/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable react/prop-types */
import React, { memo, useMemo } from 'react';
import { Typeahead, TypeaheadComponentProps } from 'react-bootstrap-typeahead';
import { Option } from 'react-bootstrap-typeahead/types/types';
import './style.scss';
interface Props {
    options: Option[];
    id: string;
    placeholder: string;
    onChange: (newValue: string | number | null | (string | number)[]) => void;
    value: string | number | null | string[];
    isInvalid: boolean;
    className?: string;
    disabled?: boolean;
    onInputChange?: () => void;
    multiple?: boolean;
    isLoading?: boolean;
}

const getSelectedValue = (value: number | string | string[], options: Option[]) => {
    const result = options.filter((option) => {
        const optionValue = option as { id: string | number } ;
        return (Array.isArray(value)) ? value?.includes(optionValue?.id as string) : (optionValue.id === value);
    });
    return result;
};

const DropdownSearchControl = memo(
    ({ options, id, placeholder, onChange, value, isInvalid, className, disabled = false, onInputChange, ...restProps }: Props) => {
        const handleChange = (newValue: Option[]) => {
            if (newValue.length > 0) {
                if (restProps.multiple) {
                    onChange(newValue.map((value) => {
                        const optionValue = value as { id: string | number };
                        return optionValue.id;
                    }));
                } else {
                    const optionValue = newValue[0] as { id: string | number };
                    onChange(optionValue.id);
                }
            } else {
                onChange(null);
            }
        };
        const selected = useMemo(() => getSelectedValue(value!, options), [value, options]);
        console.log(selected, value)
        return (
            <Typeahead
                id={id}
                maxResults={10}
                className={className}
                // @ts-ignore
                inputProps={{ 'data-testid': id }}
                selected={selected as unknown as Option[]}
                onChange={handleChange}
                options={options as unknown as Option[]}
                labelKey='value'
                isInvalid={isInvalid}
                disabled={disabled}
                placeholder={placeholder}
                onInputChange={onInputChange}
                {...restProps}
                
            />
        );
    }
);

export { DropdownSearchControl };
