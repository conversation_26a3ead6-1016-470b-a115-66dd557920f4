@import "global";

.error-msg {
    color: map-get($theme-colors, "danger");
    letter-spacing: 0px;
    opacity: 1;
}

.error-title {
    font-size: 64px;
    margin: 0;
}

.error-description {
    font-size: 32px;
    margin: 0;
}

.error-caption {
    color: map-get($theme-colors, "primary");
    font-size: 13px;
}

.link-access-wrapper {
    background: #F8F9FA 0% 0% no-repeat padding-box;
    border: 1px solid #EFEFEF;
    border-radius: 5px;
    padding: 15px;
    margin: 30px 0px;
    opacity: 1;
    width: 91%;
}

.wrong-caption{
    color: #1F4A70;
    font-size: 32px;
}