// setup the run steps here
node('paris2') {
  echo sh(returnStdout: true, script: 'env')
      
  stage('Checkout') {
    echo "My branch is: ${env.BRAN<PERSON>}"
    checkout([
      $class: 'GitSCM',
      branches: [[name: "${env.<PERSON><PERSON><PERSON>}"]],
      doGenerateSubmoduleConfigurations: false,
      extensions: [], submoduleCfg: [],
      userRemoteConfigs: [[
          name: 'github',
          credentialsId: 'fleet_devops',
          url: "https://<EMAIL>/fleetshipteam/paris2-web-base.git"
      ]]
    ])
  }

  stage('Build') {
    dir('assets') {
        sh "npm ci"
        sh "ENV=${env.stageName} ./build.sh"
    }
  }

  stage('Deploy') {
    dir('assets') {
        sh "./deploy.sh ${env.stageName}"
    }
  }
}
