import React, { useState } from "react";
import { <PERSON><PERSON>, Col, Accordion } from "react-bootstrap";
import { Link } from "react-router-dom";
import styleGuide from "../styleGuide";

const { Icon } = styleGuide;
const getNavLinkClass = (currentPath, element, links = []) => {
  const currentPathArray = currentPath?.split("/") || [];
  const pathArray = element.href?.split("/") || element?.split("/");
  const splitLength = pathArray.length > 2 ? pathArray.length - 1 : 2;
  const linksArray = links.map(link => link.href.split("/")[splitLength]);
  let active = false;
  if (pathArray.length > 2) {
    active = currentPathArray[splitLength] === pathArray[splitLength];
  } else if (
    !currentPathArray[splitLength] ||
    !linksArray.includes(currentPathArray[splitLength])
  ) {
    active = currentPathArray[1] === pathArray[1];
  }
  return active ? "active-link" : "";
};

const AccordionMenu = ({
  ParentIcon,
  title,
  links,
  currentPath,
  onClick,
  id
}) => {
  const [isExpanded, setExpanded] = useState(
    links.some(link => currentPath.startsWith(`/${link.href?.split("/")[1]}`))
  );
  const handleExpand = () => setExpanded(!isExpanded);

  return (
    <Col xs={12} xl={12} className={"sidenav-menu-item px-0"}>
      <Accordion defaultActiveKey={isExpanded ? "0" : ""}>
        <div key={id} className="p-6" data-testid={"sidebar-navigation-" + id}>
          <Accordion.Toggle
            as={Button}
            eventKey="0"
            className="sidenav_btn"
            onClick={handleExpand}
            block
            data-testid={`${id}-sidebar-module`}
          >
            <div>
              {ParentIcon}
              {title}
            </div>
            <Icon icon={isExpanded ? "arrow-up" : "arrow-down"} size={24} className="sidebar_navigation__icon text-primary" />
          </Accordion.Toggle>
          <Accordion.Collapse eventKey="0">
            <>
              {links.map(element => {
                const activeClassName = getNavLinkClass(
                  currentPath,
                  element,
                  links
                );
                return (
                  <Col
                    xs={12}
                    xl={12}
                    key={element.href}
                    className={`sidebar-sub-menu ${activeClassName}`}
                  >
                    <Link
                      key={element.href}
                      className="p-6"
                      to={element.href}
                      role="link"
                      aria-current={activeClassName ? "page" : false}
                      onClick={onClick}
                    >
                      <Button
                        className="sidenav_module"
                        block
                        data-testid={`${id}-${element.href}`}
                      >
                        {element.title}
                      </Button>
                    </Link>
                  </Col>
                );
              })}
            </>
          </Accordion.Collapse>
        </div>
      </Accordion>
    </Col>
  );
};

export { AccordionMenu };
