import React, { Suspense } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import NavBar from "./components/NavBar.js";
import { I18nextProvider } from "react-i18next";
import "./scss/navbar.scss";
import * as localization from "@paris2/localization";
import {
  TIME_SPENT_TRACKING,
  PAGE_CATEGORY
} from "./constants/ga_time_spent_tracker.js";
import moment from "moment";

class Root extends React.Component {
  state = {
    hasError: false,
    i18n: null,
    languages: [],
    currentLanguage: null,
    prevPath: "",
    currentPath: "",
    startTime: moment().valueOf()
  };

  constructor(props) {
    super(props);
    if (props.history) {
      props.history.listen(location => {
        this.setChangedPathName();
      });
    }
  }

  ga4EventTrigger = (action, category, label) => {
    try {
      this.props.ga4react?.event(action, label, category, false);
    } catch (error) {
      console.log(error);
    }
  };

  setChangedPathName = () => {
    if (
      Object.keys(TIME_SPENT_TRACKING).includes(this.state.currentPath) &&
      this.state.prevPath !== "" &&
      this.state.currentPath !== this.state.prevPath
    ) {
      const trackerDetails = TIME_SPENT_TRACKING[this.state.currentPath];
      const endTime = moment().valueOf();
      const spentTime = moment
        .duration(moment.utc(endTime - this.state.startTime).format("HH:mm:ss"))
        .asSeconds();
      this.ga4EventTrigger(
        "Time Spent",
        `${trackerDetails.category} - ${spentTime}s`,
        trackerDetails.label
      );
    }
    if (location.pathname !== this.state.currentPath) {
      const domainPath = location.pathname.split("/")[1];
      this.ga4EventTrigger(
        "custom_page_view",
        `${PAGE_CATEGORY[domainPath]}`,
        location.pathname
      );
    }
    let updatedStartTime;
    if (Object.keys(TIME_SPENT_TRACKING).includes(location.pathname)) {
      updatedStartTime = moment().valueOf();
    }
    this.setState((prevState) => ({
      prevPath: prevState.currentPath,
      currentPath: location.pathname,
      startTime: updatedStartTime ?? prevState.startTime
    }));
  };

  componentDidCatch(err) {
    console.error(err);
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, currentLanguage }) => {
    this.setState((prevState) => ({
        ...prevState,
        i18n,
        currentLanguage,
      }));
  };

  componentDidUnmount = async () => {
    localization.removeCallback(this.onLanguageChanged);
  };

  componentDidMount = async () => {
    this.setChangedPathName();
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
  };

  render() {
    const { hasError, i18n, currentLanguage, currentPath } = this.state;
    const {
      kc,
      isNovaActive,
      history,
      ga4react,
      notificationEvents
    } = this.props;
    const isReady = !hasError && i18n && currentLanguage;
    return (
      <div>
        {!!isReady ? (
          <I18nextProvider i18n={i18n}>
            <BrowserRouter>
              <Suspense fallback={<div></div>}>
                <NavBar
                  languages={localization.getSupportedLanguages()}
                  currentLanguage={currentLanguage}
                  kc={kc}
                  currentPath={currentPath}
                  isGradientBackground={isNovaActive(location)}
                  ga4react={ga4react}
                  notificationEvents={notificationEvents}
                />
              </Suspense>
            </BrowserRouter>
          </I18nextProvider>
        ) : null}
      </div>
    );
  }
}

export default Root;
