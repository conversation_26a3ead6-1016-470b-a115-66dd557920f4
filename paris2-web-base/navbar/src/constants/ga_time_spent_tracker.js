const TIME_SPENT_TRACKING = {
  "/home": {
    label: "Landing",
    category: "Landing",
  },
  "/nova": {
    label: "NOVA Home",
    category: "NOVA Home",
  },
  "/nova/vessels/dashboard": {
    label: "NOVA Dashboard",
    category: "Vessel Dashboard",
  },
  "/nova/dashboard/details/fuelConsumption": {
    label: "NOVA Dashboard",
    category: "Fuel Consumption Dashboard",
  },
  "/nova/dashboard/details/gaseousEmission": {
    label: "NOVA Dashboard",
    category: "Gaseous Emissions Dashboard",
  },
  "/nova/dashboard/details/vesselWaste": {
    label: "NOVA Dashboard",
    category: "Vessel Waste Dashboard",
  },
  "/nova/dashboard/details/vesselFinance": {
    label: "NOVA Dashboard",
    category: "Vessel Finance Dashboard",
  },
  "/dashboard/owner-finance": {
    label: "NOVA Dashboard",
    category: "Owner Finance Dashboard",
  },
};

const PAGE_CATEGORY = {
    vessel: "Vessel",
    home: "Landing Page",
    "seafarer-reports": "Seafarer Reports",
    seafarer: "Seafarer",
    survey: "Survey",
    qhse: "QHSE",
    "inspections-audits": "Inspection/Audit Report",
    deficiency: "Deficiencies",
    "portage-bill": "Finance",
    "item-master": "Item Master",
    "ship-party": "Ship Party",
    "file-transfer": "File Transfer",
    nova: "NOVA",
    dashboard: "NOVA Dashboard",
    reference: "Reference",
    "pms": "PMS"
}
export { TIME_SPENT_TRACKING, PAGE_CATEGORY };
