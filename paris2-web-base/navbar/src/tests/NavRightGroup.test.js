import React from "react";
import NavRightGroup from "../components/NavRightGroup.js";
import { fireEvent, render, screen } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";

let wrapper;
let kcMock;

jest.mock("../styleGuide");

beforeEach(() => {
  kcMock = {
    tokenParsed: {
      name: "<PERSON>",
      rank: "Software Engineer",
      department: "IT Development"
    },
    logout: jest.fn()
  };
  render(
    <MemoryRouter>
      <NavRightGroup kc={kcMock} notifications={[]} />
    </MemoryRouter>
  );
});

describe("<NavRightGroup/>", () => {
  it("should render notification button", () => {
    expect(screen.getByTestId("test__link-notification")).toBeInTheDocument();
  });

  it("should render question icon with link to open in new tab", () => {
    const questionIcon = screen.getByTestId("faq-card");
    expect(questionIcon).toBeInTheDocument();
    // expect(questionIcon.prop('href')).toBe('https://fleetship.sharepoint.com/sites/Paris20')
    expect(questionIcon.getAttribute("target")).toBe("_blank");
  });

  describe("user menu", () => {
    it("should have user details", () => {
      const avatarDropDown = screen.getByRole("button", {
        expanded: false
      });
      fireEvent.click(avatarDropDown);
      expect(avatarDropDown.textContent).toBe("K");
      expect(screen.getByText("Kent Beck")).toBeInTheDocument();
      expect(screen.getByText("Software Engineer")).toBeInTheDocument();
      expect(screen.getByText("IT Development")).toBeInTheDocument();
      expect(screen.getByText("Account Details")).toBeInTheDocument();
    });

    it("should signout on click of Sign out button", () => {
      const avatarDropDown = screen.getByRole("button", {
        expanded: false
      });
      fireEvent.click(avatarDropDown);
      fireEvent.click(screen.getByTestId("sign-out-button"));
      expect(kcMock.logout).toHaveBeenCalledTimes(1);
    });
  });
});
