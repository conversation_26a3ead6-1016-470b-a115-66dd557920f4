const { defaults } = require("jest-config");

module.exports = {
  testEnvironment: "jsdom",
  rootDir: "src",
  moduleFileExtensions: [...defaults.moduleFileExtensions, "ts", "tsx"],
  transform: {
    "^.+\\.vue$": "babel-jest",
    ".+\\.(css|styl|less|sass|scss|png|jpg|ttf|woff|woff2|svg)$":
      "jest-transform-stub",
    "^.+\\.(js|jsx)?$": "babel-jest"
  },
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1"
  },

  transformIgnorePatterns: ["navbar/node_modules/"],
  modulePaths: ["src", "test"],
  setupFilesAfterEnv: ["<rootDir>/setupTests.js"]
};
