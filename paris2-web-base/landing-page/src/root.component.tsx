/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-access-state-in-setstate */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/state-in-constructor */
/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable react/prop-types */
/* eslint-disable no-console */
import React from 'react';
import { BrowserRouter, Route, Switch } from 'react-router-dom';
import { I18nextProvider, I18nextProviderProps } from 'react-i18next';
import GA4React from 'ga-4-react';
import _ from 'lodash';
// @ts-ignore
import * as localization from '@paris2/localization';
import { AppLoader } from './components/common';
import { init } from './services/user-service';
import { Dashboard } from './pages/Dashboard';
import { FAQ } from './pages/FAQ';
import { Keycloak } from './types/keycloak';
import { ErrorPage } from './StyleGuide';
import './styles/base.scss';
import * as vesselService from './services/vessel-service';
import { hasAccessToVessel } from 'src/utils/roles';
import { GlobalContext, GlobalContextState } from './context/dashboard-context';
import { getActiveOwnershipVessels } from './utils/vessel';
import { WorldMap } from './pages/WorldMap';

interface Languages {
  key: string;
  name: string;
}

interface State extends GlobalContextState {
  hasError: boolean;
  i18n: I18nextProviderProps | null;
  languages: Languages[];
  currentLanguage: string | null;
  roleConfig: unknown;
}

export interface RootProps {
  kc: Keycloak;
  ga4react?: GA4React;
}

class Root extends React.Component<RootProps, State> {
  state: State = {
    hasError: false,
    i18n: null,
    languages: [],
    currentLanguage: null,
    roleConfig: null,
    vesselCurrentlyOwned: [],
    isVesselLoading: true,
  };

  componentDidCatch(error: any, info: any) {
    this.setState({ hasError: true });
  }

  onLanguageChanged = async ({ i18n, languages, currentLanguage }: State) => {
    this.setState({
      languages,
      currentLanguage,
      i18n,
    });
  };

  componentDidMount = async () => {
    await init();
    localization.addCallback(this.onLanguageChanged);
    await localization.initialize();
    await this.handleGetVesselBaseOnUserRole();
  };

  componentDidUnmount = async () => {
    localization.removeCallback(this.onLanguageChanged);
  };

  componentDidUpdate = async () => {
    await init();
  };

  ga4EventTrigger = (category: string, label: string, action: string = 'click') => {
    try {
      const { ga4react } = this.props;
      ga4react?.event(action, _.toString(label), category, false);
    } catch (error) {
      console.log('error on gtag:', error);
    }
  };

  ga4PageView = (path: string, location: string, title: string) => {
    try {
      const { ga4react } = this.props;
      ga4react?.pageview(path, location, title);
    } catch (error) {
      console.log('error on gtag:', error);
    }
  };

  handleGetVesselBaseOnUserRole = async () => {
    if (this.props.kc.tokenParsed.ship_party_id) {
      await this.getVesselCurrentlyOwned();
      return;
    }
    if (hasAccessToVessel(this.props.kc)) {
      await this.getVesselsForFleetStaff();
    }
  };

  getVesselCurrentlyOwned = async () => {
    try {
      this.setState({ isVesselLoading: true });
      const { kc } = this.props;
      const { tokenParsed } = kc;
      const { ship_party_id } = tokenParsed;
      const { data } = await vesselService.getVesselsCurrentlyOwned(ship_party_id as number, 1);
      const activeOwnershipVessel = getActiveOwnershipVessels(data);
      const vesselOwned = activeOwnershipVessel.map(ele => ({
        id: ele.id,
        name: ele.name,
        ref_id: ele.vessel.ref_id,
        vessel_id: ele.vessel.id,
      }));
      this.setState({ vesselCurrentlyOwned: vesselOwned });
    } catch (error) {
      console.log(
        `Something went wrong on loading vessel currently owned. Here is the full error ${error}`,
      );
    } finally {
      this.setState({ isVesselLoading: false });
    }
  };

  getVesselsForFleetStaff = async () => {
    try {
      this.setState({ isVesselLoading: true });
      const { data } = await vesselService.getOwnerships();
      const vesselOwned = data.results.map(ele => ({
        id: ele.id,
        name: ele.name,
        vessel_id: ele.vessel_id,
        vessel_type: ele?.vessel_type?.type,
      }));
      this.setState({ vesselCurrentlyOwned: vesselOwned });
    } catch (error) {
      console.log(
        `Something went wrong on loading active vessels with techgroup. Here is the full error ${error}`,
      );
    } finally {
      this.setState({ isVesselLoading: false });
    }
  };

  render() {
    const { hasError, i18n, currentLanguage, vesselCurrentlyOwned, isVesselLoading } = this.state;
    const hasInternationalization = i18n && currentLanguage;

    if (!hasInternationalization) {
      return <AppLoader hasError={hasError} />;
    }
    return (
      <I18nextProvider i18n={i18n}>
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned,
            isVesselLoading,
            ga4EventTrigger: this.ga4EventTrigger,
            ga4PageView: this.ga4PageView,
          }}
        >
          <BrowserRouter>
            <Switch>
              <Route exact path="/home">
                <Dashboard keycloak={this.props.kc as Keycloak} />
              </Route>
              <Route path="/home/<USER>">
                <FAQ />
              </Route>
              <Route path="/worldmap">
                <WorldMap containerClassName="pt-7 pb-3" keycloak={this.props.kc as Keycloak} />
              </Route>
              <Route>
                <ErrorPage errorCode={404} />
              </Route>
            </Switch>
          </BrowserRouter>
        </GlobalContext.Provider>
      </I18nextProvider>
    );
  }
}

export { Root };
