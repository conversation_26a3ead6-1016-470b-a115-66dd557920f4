import React from 'react';
import { render, screen } from '@testing-library/react';
import { CustomOverlay } from '../common/CustomOverlay';
import Joyride from 'react-joyride';

jest.mock('react-joyride', () =>
  jest.fn(() => <div data-testid="onboarding-tutorial-wrapper">Joyride</div>),
);

describe('CustomOverlay Component', () => {
  const defaultProps = {
    run: true,
    steps: [{ target: '#element', content: 'Step 1' }],
    continuous: true,
    handleOverlayCallback: jest.fn(),
  };

  it('renders Joyride component with given props', () => {
    render(<CustomOverlay {...defaultProps} />);
    expect(screen.getByTestId('onboarding-tutorial-wrapper')).toBeInTheDocument();
  });

  it('calls handleOverlayCallback when Joyride callback is triggered', () => {
    render(<CustomOverlay {...defaultProps} />);
    expect(Joyride).toHaveBeenCalledWith(
      expect.objectContaining({
        run: defaultProps.run,
        steps: defaultProps.steps,
        continuous: defaultProps.continuous,
        callback: defaultProps.handleOverlayCallback,
        showSkipButton: true,
        showProgress: true,
      }),
      expect.anything(),
    );
  });
});
