import React from 'react';
import { render, screen } from '@testing-library/react';
import { Loader } from '../common/Loader';

jest.mock('react-bootstrap', () => ({
  Spinner: jest.fn(() => <div data-testid="spinner">Spinner</div>),
}));

describe('Loader Component', () => {
  it('renders with default title and without full-screen class', () => {
    render(<Loader />);
    const loader = screen.getByTestId('full-screen-loader');
    expect(loader).toBeInTheDocument();
    expect(loader).not.toHaveClass('full-screen-overlay');
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('renders with full-screen class when fullScreen is true', () => {
    render(<Loader fullScreen={true} />);
    expect(screen.getByTestId('full-screen-loader')).toHaveClass('full-screen-overlay');
  });

  it('applies custom container styles', () => {
    const customStyle = { backgroundColor: 'red' };
    render(<Loader containerStyle={customStyle} />);
    expect(screen.getByTestId('full-screen-loader')).toHaveStyle('background-color: red');
  });
});
