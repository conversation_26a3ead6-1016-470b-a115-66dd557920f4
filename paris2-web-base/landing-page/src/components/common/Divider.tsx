import React, { memo } from 'react';
import './styles/separator.scss';
interface Props {
  height?: string;
  dashed?: boolean;
}

// border-top: 1px dashed #1F4A70;

const Divider = memo(({ height = '1', dashed }: Props) => {
  const dashedLineClass = dashed ? 'dashed' : '';
  return (
    <div
      data-testid="divider-test-id"
      className={`separator ${dashedLineClass}`}
      style={{ paddingTop: `${height}%`, paddingBottom: `${height}%` }}
    />
  );
});

export { Divider };
