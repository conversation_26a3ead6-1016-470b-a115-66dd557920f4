import React, { memo } from 'react';
import Joyride, {
  CallBackProps, Step, FloaterProps, StoreHelpers,
} from 'react-joyride';
import "./styles/overlay.scss";

interface Props {
  target?: React.MutableRefObject<null> | { current: HTMLElement },
  steps: Step[],
  run: boolean,
  styles?: any,
  floaterProps?: FloaterProps
  continuous: boolean
  getHelpers?: StoreHelpers
  handleOverlayCallback?: (text: CallBackProps) => void
}

export const CustomOverlay = memo(({
  run, steps, styles, continuous, handleOverlayCallback,
}: Props) => (
  <Joyride
    data-testid="onboarding-tutorial-wrapper"
    run={run}
    steps={steps}
    styles={styles}
    continuous={continuous}
    floaterProps={{ disableAnimation: true }}
    callback={handleOverlayCallback}
    scrollToFirstStep={false}
    disableScrollParentFix
    scrollOffset={100}
    showSkipButton
    showProgress
  />
));
