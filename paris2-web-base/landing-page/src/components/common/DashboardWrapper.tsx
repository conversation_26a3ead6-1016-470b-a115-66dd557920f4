/* eslint-disable @typescript-eslint/ban-ts-comment */
import React from "react";
// @ts-ignore
import TableauReport from 'tableau-react';
import { FiAlertCircle } from "react-icons/fi";
import { getTableauOptions } from "../../utils/dashboard";
import AlertGuide from "../dashboard/AlertGuide";

const ERROR_STATE_TABLEAU_TOKEN = 'error';

interface DashboardFilterParams {
  [key: string]: string | number | boolean;
}

interface Props {
  token: string | null;
  query: string;
  filters: DashboardFilterParams;
  parameters?: DashboardFilterParams;
  url: string | null;
}

const DashboardWrapper = ({
  token, query, filters, url, parameters,
}: Props) => {
  if (token === ERROR_STATE_TABLEAU_TOKEN) return <AlertGuide caption="Unable to Load" icon={<FiAlertCircle size={52} color="#1F4A70" />} />;
  return (
    <TableauReport
      url={url}
      options={getTableauOptions()}
      token={token}
      query={query}
      filters={filters}
      parameters={parameters}
    />
  );
};

export { DashboardWrapper };
