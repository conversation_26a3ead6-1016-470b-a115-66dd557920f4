.custom-overlay {
  border-radius: 4px;
  border: 1px solid #0091b8;
  background-color: #0091b8;
  padding: 7px 7px;
  display: flex;
  color: white;
  opacity: 1;
}
.col-md-auto.step-content {
  padding-right: 40px;
}
.custom-overlay-line {
  width: 3px;
  height: 30px;
  background: #0091b8 0% 0% no-repeat padding-box;
  margin: auto;
}

.remove-icon {
  height: 30px;
  width: 30px;
}

.custom-overlay-circle {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  margin: auto;
  background: #0091b8 0% 0% no-repeat padding-box;
}
.react-joyride__spotlight {
  background-color: #f5f5f5 !important;
  opacity: 0.4 !important;
}
.close-button {
  border: 2px solid transparent;
  background-color: #0091b8;
  color: white;
  padding-right: 2px;
  &:hover,
  &:active,
  &:focus {
    border: none;
    background-color: #0091b8;
    outline: none !important;
  }
}

.react-joyride__tooltip {
  [aria-label='Last'] {
    background-color: #0091b8 !important;
  }

  [aria-label='Next'] {
    background-color: #0091b8 !important;
  }

  [aria-label='Back'] {
    color: #0091b8 !important;
  }
}
