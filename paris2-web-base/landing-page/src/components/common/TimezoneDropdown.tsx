import React from 'react';
import { Form } from "react-bootstrap";
import { Typeahead } from 'react-bootstrap-typeahead';
import { TimezoneDropdownType } from '../../types/widgets';

interface Props {
  optionsList: TimezoneDropdownType[],
  label: string,
  onChange: (arg0: TimezoneDropdownType) => void,
}

export const TimezoneDropdown = ({
  optionsList, label, onChange,
}: Props) => {
  const onCountryChange = (selectedTimezoneValue: TimezoneDropdownType[]) => {
    if (selectedTimezoneValue && selectedTimezoneValue.length > 0) {
      onChange(selectedTimezoneValue[0]);
    }
  };

  return (
    <Form.Group>
      <Form.Label style={{ fontWeight: 'bold' }}>{label}</Form.Label>
      <Typeahead
        placeholder="Please select city"
        id="timezoneDropdown_id"
        labelKey={(e) => e.city === 'Hong Kong' ? `${e.city} SAR` : e.city}
        onChange={onCountryChange}
        options={optionsList}
        disabled={false}
        isInvalid={false}
      />
    </Form.Group>
  );
};
