import React, { useState, useEffect } from 'react';
import { Button } from "react-bootstrap";
import Clock from 'react-clock';
import 'react-clock/dist/Clock.css';
import './styles/analog-clock.scss';
import * as moment from 'moment-timezone';

const WEEKDAY = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

interface Props {
  city: string;
  timezone: string;
  ind: number;
  labelClicked: (ind: number) => void;
}
export const AnalogClock = ({
  city, timezone, ind, labelClicked,
}: Props) => {
  const [timer, setTimer] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(
      () => setTimer(new Date()),
      1000 * 10,
    );
    return () => {
      clearInterval(interval);
    };
  }, []);
  const momentTimezone = moment.tz(timezone);
  const day = momentTimezone.day();
  const hh = momentTimezone.hours();
  const mm = momentTimezone.minutes();

  const date = new Date();
  const utc = date.getTime() + date.getTimezoneOffset() * 60 * 1000;
  const value = utc + momentTimezone.utcOffset() * 60 * 1000;

  const cityName = city === 'Hong Kong' ? `${city} SAR` : city;

  return (
    <>
      <Clock
        value={new Date(value)}
        size={90}
        className="ml-auto mr-auto analog-clock"
        minuteHandLength={60}
        hourMarksLength={5}
        hourMarksWidth={2}
        minuteMarksLength={3}
        minuteMarksWidth={1}
        renderNumbers
        renderSecondHand={false}
      />
      <div className="analog-clock-font mt-1 text-center">
        <div>
          {hh.toString().padStart(2, '0')}
          :
          {mm.toString().padStart(2, '0')}
          {WEEKDAY[day].padStart(4, ' ')}
        </div>
        <Button
          onClick={() => { labelClicked(ind); }}
          variant="link p-0 analog-clock-city mb-2"
        >
          {cityName}
        </Button>
      </div>
    </>
  );
};
