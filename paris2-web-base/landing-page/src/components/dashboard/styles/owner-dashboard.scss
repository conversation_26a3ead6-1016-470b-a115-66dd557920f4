body {
  margin-top: 0px; 
}
.world-map-control {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
  margin-right: 10px;
  position: absolute !important;
  top: 0px !important;
  right: 50px !important;
}

.weather-button {
  padding: 10px;
  border-radius: 2px;
  border: none;
  background: #fff;
}

.owner-report-container {
  &.card {
    box-shadow: 0px 0px 2px #00000026;
    border-radius: .375rem;
    border: 0;
    margin-bottom: 1.875rem;
  }

  .card-body {
    padding: 1rem;
  }

  .owner-title {
    align-self: center;

    .report-title {
      font: normal normal normal 18px/32px Inter,sans-serif;
      text-align: left;
      margin: 0;
      color: #343a40;
    }
  }

  .vessel-table-wrapper {
    width: 100%;
    padding: 1.25rem 0 .375rem;
    &.vessel-container {
        position: relative;
        overflow: hidden;
    }
  }

  .vessel-table {
    max-height: 12.69rem;
    overflow-y: auto;
    &.itineraries-table {
      max-height: 9.565rem;
      .vessel-title {
        padding-left: 0;
      }
    }
    .table {
      padding-top: 0;
    }
    .noResultsFound {
      margin-top: 0;
    }
    .coming-soon-text {
      margin-bottom: 0;
    }
    .text-center {
      padding: 2rem 0;
    }
  }

  .vessel-row,
  .vessel-table tr {
    box-shadow: 0px 1px 3px #0000001A;
    border-radius: .25rem;
  }
  .vessel-row {
    margin: 0 0 .5rem;
  }
  .vessel-cell {
    flex: 1;
  }
  .vessel-title,
  .vessel-link-row {
    color: #1F4A70;
    height: 2.625rem;
    padding: 0 0 0 .75rem;
    font-weight: normal;
    line-height: 2.625rem;
    cursor: pointer;
  }
  .basic-column-text {
    text-align: right;
    color: #343A40;
    padding: 0 .75rem 0 0;
    line-height: 2.625rem;
  }

  .itinerary-row {
    display: flex;
    flex-wrap: nowrap;

    .itinerary-title {
      flex: 1;
    }
    .itineraries-eta {
      white-space: nowrap;
    }
  }

  .itineraries-detail {
    color: #343A40;
    padding-left: 0;
    text-align: right;
    white-space: nowrap;
    cursor: default;
  }
  .itinerary-thead {
      padding-right: 1.5rem;
      text-align: right;
      line-height: 2rem;
  }
}

.owner-landing-page-container {
  margin-top: 64px;
  padding: 0 1.5rem;
  .col-xxl, .col-xxl-auto, .col-xxl-12, .col-xxl-11, .col-xxl-10, .col-xxl-9, .col-xxl-8, .col-xxl-7, .col-xxl-6, .col-xxl-5, .col-xxl-4, .col-xxl-3, .col-xxl-2, .col-xxl-1, .col-xl, .col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg, .col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md, .col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm, .col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col, .col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1 {
    padding-left: 12px;
    padding-right: 12px;
  }
    
  .widget-padding-top {
    padding: 1.5rem 0;
  }

  .table-header-container {
    display: none;
  }

  .table-responsive {
    .table {
      border-collapse: separate;
      border-spacing: 0 .5rem;
      padding: 0 .75rem;
    }
  }
}

.marker {
  padding-top: 10px;
}

.greeting-container {
  .card-body {
    padding: 0;
  }

  .pr-0 {
    padding-right: 0;
  }

  .pl-0 {
    padding-left: 0;
  }

  .no-wrap {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .owner-greeting {
    padding: 1.25rem 0 1.25rem 1.25rem;
  }

  .greeting-title {
    font: normal normal 500 25px Inter,sans-serif;
    color: #343a40;
    padding: 0 0 1.25rem;
  }

  .user-time-temp {
    font: normal normal normal 28px/32px Inter,sans-serif;
    padding-right: .25rem;
    @media screen and (max-width: 1330px) and (min-width: 992px){
        font: normal normal normal 22px/32px Inter,sans-serif;
    }
  }

  .utc-time-temp {
    font: normal normal normal 28px/32px Inter,sans-serif;
    margin-left: 24px;
    padding-right: .25rem;
    @media screen and (max-width: 1330px) and (min-width: 992px){
        font: normal normal normal 22px/32px Inter,sans-serif;
        margin-left: 10px;
    }
  }

  .time-zone {
    font: normal normal 500 16px/32px Inter,sans-serif;
  }

  .location {
    font-weight:500;
    font-size: 16px;
  }

  .weather-temp-container {
    padding: .375rem;
    padding-right: 10px;
    height: 100%;
  }

  .weather-temp-wrap {
    height: 100%;
    border-radius: .375rem;
    background: #3C9EF51A;
    padding: 1rem;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
  }

  .temperature-wrap {
    margin: 0;

    .weather-wind {
      font: normal normal normal 14px/20px Inter,sans-serif;
      color: #333;
      padding-bottom: 4px;
    }

    .weather-temp {
      display: flex;
      margin: 0;
    }

    .weather-temp-val {
      flex: 1;
      font: normal normal 500 16px/20px Inter,sans-serif;
      color: #000;
      @media screen and (max-width: 1330px) and (min-width: 990px){
        font: normal normal 500 10px Inter,sans-serif;
      }
    }
  }
}
