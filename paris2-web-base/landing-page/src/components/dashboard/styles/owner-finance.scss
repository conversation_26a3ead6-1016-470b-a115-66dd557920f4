 @import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600&display=swap');

.dashboard-spinner {
  justify-content: center;
  align-items: center;
  display: flex;
  padding-top: 15%;
}

.tableau_dashboard_owner_finance{
  width: 100%;
  min-height: 25rem ;
  border: 1px solid rgba(0,0,0,.125);
  border-radius: .25rem;
  padding: 1.25rem;
  background-color: #fff;
  height: auto;
  display: grid;
  @media screen and (max-width: 728px){
    padding: 0.25rem;
  }

  .tableau_dashboard_owner_finance-container{
    display: grid;
    position: relative;

    .to-nova-button {
      padding: 8px;
      overflow: visible;
      position: absolute;
      float: right;
      font-size: small;
      right: 0;
      top: 0;
      @media screen and (max-width: 728px){
        padding: 6px;
        font-size: x-small;
      }
    }
  } 
}

  
iframe {
  width: 100% !important;
  height: 25rem !important;
  font-family: 'Inter', sans-serif !important; 
}
