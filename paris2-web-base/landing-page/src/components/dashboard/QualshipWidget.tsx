import React, { useContext } from 'react';
import { FiAlertCircle } from 'react-icons/fi';
import { Table } from 'react-bootstrap';
import { VesselSubscription } from '../../types/ship-party';
import { goToParis1Qualship, goToParisShipDetails } from '../../utils/paris-link';
import { GlobalContext, VesselQualship } from '../../context/dashboard-context';
import AlertGuide from './AlertGuide';
import { downloadFile } from '../../services/vessel-service';
import './styles/vessel-qualship-widget.scss';
import _ from 'lodash';

interface Props {
  vessel: VesselQualship;
  onVesselClick: (vessel: VesselQualship) => void;
  onCertificateDownload: (path: string, name: string) => void;
}

const QualshipRowDetails = ({ vessel, onVesselClick, onCertificateDownload }: Props) => {
  const handleVesselClick = () => onVesselClick(vessel);
  const handleDownload = () => onCertificateDownload(vessel.certificate_url, vessel.vessel_name);
  return (
    <tr key={vessel.vessel_id}>
      <td>
        <div className="qualship-vessel-text" data-testid={`vessel-qualship-${vessel.vessel_id}`}>
          <span onClick={handleVesselClick} role="button" tabIndex={0} aria-hidden="true">
            {vessel.vessel_name ?? '- - -'}
          </span>
        </div>
      </td>
      <td>
        <div className="e-zero-text">{vessel.is_zero ? 'Yes' : 'No'}</div>
      </td>
      <td>
        <div
          className="view-certificate-text"
          data-testid={`vessel-qualship-certificate-link-${vessel.vessel_id}`}
        >
          <span onClick={handleDownload} role="button" tabIndex={0} aria-hidden="true">
            View
          </span>
        </div>
      </td>
    </tr>
  );
};

const QualshipWidget = ({
  shipPartyID,
  shipPartyType,
}: {
  shipPartyID: number;
  shipPartyType: string;
}) => {
  const {
    vesselWithQualshipDetails,
    keycloak,
    vesselCurrentlyOwned,
    ga4EventTrigger,
    ga4PageView,
  } = useContext(GlobalContext);
  if (vesselWithQualshipDetails.length === 0) {
    return (
      <AlertGuide caption="No Records Found" icon={<FiAlertCircle size={52} color="#1F4A70" />} />
    );
  }
  const handleVesselLinkClick = (vessel: VesselQualship) => {
    if (ga4EventTrigger)
      ga4EventTrigger('Qualship 21 Certificate: Vessel', _.toString(vessel.vessel_name));
    const vesselOwned = vesselCurrentlyOwned.find(data => data.vessel_id === vessel.vessel_id);
    goToParisShipDetails(
      {
        ship_party_id: shipPartyID,
        ref_id: vessel.paris1_ref_id,
        vessel_id: vessel.vessel_id,
        id: vesselOwned?.id,
        ship_party_type: shipPartyType,
      } as VesselSubscription,
      shipPartyID as number,
      shipPartyType as string,
    );
  };

  const onMoreInfoClick = () => {
    goToParis1Qualship(shipPartyID, shipPartyType);
  };

  const handleDownloadCertificate = async (path: string, name: string) => {
    if (ga4EventTrigger) ga4EventTrigger('Qualship 21 Certificate: Certificate', _.toString(name));
    try {
      if (!path) {
        console.log('Certificate url does not exist');
        return;
      }
      const response = await downloadFile(path);
      if (response.data) {
        window.open(response.data.results[0].url, '_blank');
      }
    } catch (error) {
      console.log('Something went wrong on downloading qualship certificate:', error);
    }
  };

  return (
    <div className="qualship-small-table">
      <Table responsive borderless size="sm" striped>
        <thead>
          <tr className="table-header-container">
            <th className="column-header vessel-column-header">Vessel Name</th>
            <th className="column-header e-zero-col" style={{ textAlign: 'center' }}>
              E-Zero
            </th>
            <th className="column-header certificate-column-header">Certificate</th>
          </tr>
        </thead>
        <tbody>
          {vesselWithQualshipDetails.map(vessel => (
            <QualshipRowDetails
              vessel={vessel}
              key={vessel.vessel_id}
              onVesselClick={handleVesselLinkClick}
              onCertificateDownload={handleDownloadCertificate}
            />
          ))}
        </tbody>
      </Table>
      {vesselWithQualshipDetails.length > 10 && (
        <span
          className="more-info-block"
          role="button"
          tabIndex={0}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              onMoreInfoClick();
            }
          }}
          onClick={onMoreInfoClick}
        >
          For more go, to PARIS 1.0
        </span>
      )}
    </div>
  );
};

export { QualshipWidget };
