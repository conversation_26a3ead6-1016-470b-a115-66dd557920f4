/* eslint-disable max-len */
/* eslint-disable no-underscore-dangle */
import React, { useMemo, useContext } from 'react';
import { Spinner, Row, Col, Card } from 'react-bootstrap';
import { useHistory } from 'react-router';
import { Keycloak } from '../../types/keycloak';
import { ItineraryColumns } from '../../constants/table-widgets-metadata';
import OwnerVesselItinerariesTable from './OwnerVesselItinerariesTable';
import { GlobalContext } from '../../context/dashboard-context';

interface Props {
  vesselItineraries: any[];
  keycloak: Keycloak;
  isVesselItinerariesLoading: boolean;
  shipPartyId: number | null | undefined;
}

const OwnerVesselItineraries = ({
  vesselItineraries,
  keycloak,
  isVesselItinerariesLoading = false,
  shipPartyId,
}: Props) => {
  const { tokenParsed } = keycloak;
  const ship_party_id = tokenParsed?.ship_party_id ?? 0;
  const itinerariesColumns = useMemo(() => ItineraryColumns(ship_party_id), [ship_party_id]);
  const { PARIS_ONE_HOST } = process.env;
  const history = useHistory();
  const { ga4EventTrigger } = useContext(GlobalContext);
  const showItinerariesHeader: boolean =
    vesselItineraries && vesselItineraries.length > 0 ? true : false;

  return (
    <>
      <Card body className="widget-border owner-report-container">
        <Row>
          <Col xs={12} md={5} className="owner-title">
            <p className="report-title">Itineraries</p>
          </Col>
          <Col xs={12} md={5}>
            <div className="itinerary-thead">{showItinerariesHeader && 'Port'}</div>
          </Col>
          <Col xs={12} md={2}>
            <div className="itinerary-thead">{showItinerariesHeader && 'ETA'}</div>
          </Col>
        </Row>
        <Row>
          {isVesselItinerariesLoading ? (
            <div className="vessel-loading-wrapper">
              <Spinner animation="border" role="status" />
            </div>
          ) : (
            <OwnerVesselItinerariesTable
              vesselItineraries={vesselItineraries}
              columns={itinerariesColumns}
            />
          )}
        </Row>
      </Card>
    </>
  );
};

export default OwnerVesselItineraries;
