import React, { useState, useRef, useEffect, useContext } from "react";
import {
  Col, Container, Row, Card, Modal, Form, Button,
} from "react-bootstrap";
import moment from 'moment';
import _ from "lodash";
// @ts-ignore
import ReactHtmlParser from "react-html-parser";
import { CallBackProps } from 'react-joyride';
import { getVesselV2List, getVesselItineraries, getVesselListStratumData, VesselMarker, getVesselPositionReportLatLon, OwnershipDetails, VesselStratumResponse } from 'src/services/vessel-service';
import OwnerVesselList from "./OwnerVesselList";
import { CustomOverlay, GuardWrapper } from "../common/index";
import { KeycloakProps } from "../../types/keycloak";
import "../../pages/styles/dashboard.scss";
import { updateUserAttribute } from "../../services/user-service";
import * as spService from "../../services/sharepoint-service";
import { GlobalContext } from '../../context/dashboard-context';
import NoAccess from "./NoAccess";
import { hasAccessToVessel, hasNovaView, hasOfrAccess } from "../../utils/roles";
import WorldMapWidget from "./WorldMapWidget";
import { OwnerFinanceWidget } from "./OwnerFinanceWidget";
import { OwnerGreetingWidget } from "./OwnerGreetingWidget";
import "./styles/owner-dashboard.scss";
import OwnerVesselItineraries from "./OwnerVesselItineraries";
import { getOwnershipVesselMap, getMissingOwnershipIds } from "../../utils/world-map";

function OwnerDashboard({ keycloak }: KeycloakProps) {
  const { ship_party_id, ship_party_type } = keycloak.tokenParsed;
  const [run, setRun] = useState(!keycloak.tokenParsed.is_owner_onboarded);
  const vesselListRef = useRef(null);
  const fleetStoriesRef = useRef(null);
  const itinerariesRef = useRef(null);
  const [tcData, setTCData] = useState<any>();
  const [checkTC, setCheckTC] = useState<boolean>(false);
  const [isTcLoading, setTcLoading] = useState<boolean>(true);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [vesselList, setVesselList] = useState([]);
  const [vesselListRawData, setVesselListRawData] = useState([]);
  const [vesselListStratum, setVesselListStratum] = useState({});
  const [worldMapMarkerData, setWorldMapMarkerData] = useState<VesselMarker[]>([]);
  const [isLoadingVessel, setIsLoadingVessel] = useState<boolean>(true);
  const [isLoadingVesselStratumData, setIsLoadingVesselStratumData] = useState<boolean>(true);
  const [vesselItineraries, setVesselItineraries] = useState([]);
  const [vesselItinerariesRawData, setVesselItinerariesRawData] = useState([]);
  const [isVesselItinerariesLoading, setIsVesselItinerariesLoading] = useState<boolean>(true);
  const { vesselCurrentlyOwned, isVesselLoading, ga4EventTrigger } = useContext(GlobalContext);

  const sidebarMenuIcon = document.getElementById("sidebar-menu-icon");

  const hasNovaAccess = hasNovaView(keycloak);

  const isTcDone = !modalShow && !isTcLoading;

  const isTutorialReadyToStartWithNova = run && sidebarMenuIcon && isTcDone;

  const isTutorialReadyToStart = hasNovaAccess ? isTutorialReadyToStartWithNova : (run && isTcDone);
  const getVesselData = async () => {
    try {
      setIsLoadingVessel(true);
      const response = await getVesselV2List();
      setVesselListRawData(response?.data?.results);
      setIsLoadingVessel(false);
    } catch (err) {
      console.error('Unable to fetch vessel');
      setVesselListRawData([]);
      setIsLoadingVessel(false);
    }
  };

  const getOwnerVesselItineraries = async () => {
    try {
      setIsVesselItinerariesLoading(true);
      const { data } = await getVesselItineraries();
      setVesselItinerariesRawData(data.results);
    } catch (error) {
      console.log(`Something went wrong on loading vessel itineraries. Here is the full error ${error}`);
      setIsVesselItinerariesLoading(false);
      setVesselItineraries([]);
      setVesselItinerariesRawData([]);
    }
  };

  const getAllVesselsStratumData = async () => {
    try {
      setIsLoadingVesselStratumData(true);
      const endTime = moment().utc().valueOf();
      const startTime = moment(endTime).subtract(7, 'day').valueOf();
      const { data } = await getVesselListStratumData(startTime, endTime);
      setVesselListStratum(data);
      setIsLoadingVesselStratumData(false);
    } catch (err) {
      console.error('Unable to fetch vessel stratum positions');
      setVesselListStratum([]);
      setIsLoadingVesselStratumData(false);
    }
  };

  const getVesselsPositionReportsData = async (vesselOwnershipIds: string) => {
    try {
      const { data } = await getVesselPositionReportLatLon(vesselOwnershipIds);
      return data;
    } catch (err) {
      console.error('Unable to fetch vessel position reports', err);
    }
  };

  const handleItineraryBasedOnVesselOwned = (vesselOwned, vesselItineraries) => {
    if (vesselOwned.length > 0) {
      const itinerariesData = vesselItineraries.map((itinerary) => {
        const vesselOwn = vesselOwned.find((vesselOwned) => vesselOwned.vessel_id === itinerary.vessel_id);
        return { ...itinerary, ownership_id: vesselOwn.id };
      });
      setVesselItineraries(itinerariesData);
      setIsVesselItinerariesLoading(false);
    } else if (vesselOwned.length === 0 && !isVesselLoading) {
      setIsVesselItinerariesLoading(false);
    }
  };

  useEffect(() => {
    if (!keycloak.tokenParsed.is_owner_onboarded) {
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 1000);
    }

    getTermAndCondition();
    getVesselData();
    getOwnerVesselItineraries();
    getAllVesselsStratumData();
  }, []);

  useEffect(() => {
    async function handleWorldMapMarkerData(vessels: OwnershipDetails[], positions: VesselStratumResponse[]) {
      const ownershipVesselMap = getOwnershipVesselMap(vessels);
      const missingOwnershipIds = getMissingOwnershipIds(vessels, positions);
      const vesselList = vessels.map((vessel) => {
        return {
          ...vessel,
          is_from_stratumFive: !missingOwnershipIds.includes(vessel.id) ? true : false,
        }
      });
      setVesselList(vesselList);
      let vesselPositions = {...positions};
      if (missingOwnershipIds.length) {
        const positionReportsData = await getVesselsPositionReportsData(missingOwnershipIds.join(','));
        const positionReportsDataWithVesselIds = {};
        if (positionReportsData) {
          for (const [ownershipId, value] of Object.entries(positionReportsData)) {
            const vesselId = ownershipVesselMap[ownershipId];
            positionReportsDataWithVesselIds[vesselId] = value;
          }
        }
        vesselPositions = {...positions, ...positionReportsDataWithVesselIds};
      }
      const markerData: VesselMarker[] = vessels.map((vessel) => {
        const vesselId = vessel.vessel.id;
        const vesselPosition = vesselPositions[vesselId];
        const vesselType = vessel.vessel_type;
        if (vesselPosition) {
          return {
            vessel_ownership_id: vessel.id,
            vessel_id: vesselId,
            vessel_short_code: vessel.vessel_short_code,
            vessel_type: vesselType.type.toLowerCase() === "tanker" ? vesselType.type : vesselType.value,
            lat: _.toNumber(vesselPosition.lat),
            lng: _.toNumber(vesselPosition.lon),
            cog: vesselPosition?.cog ? _.toNumber(vesselPosition.cog) : 0, // angle
            isParking: (_.toNumber(vesselPosition?.sog) > 0 || missingOwnershipIds.includes(vessel.id)) ? false : true, // speed
          }
        }
      }).filter(Boolean);
      setWorldMapMarkerData(markerData);
    };
    if (!isLoadingVessel && !isLoadingVesselStratumData) {
      handleWorldMapMarkerData(vesselListRawData, vesselListStratum);
    }
  }, [isLoadingVessel, isLoadingVesselStratumData]);

  useEffect(() => {
    handleItineraryBasedOnVesselOwned(vesselCurrentlyOwned, vesselItinerariesRawData);
  }, [vesselCurrentlyOwned, vesselItinerariesRawData, isVesselLoading]);

  useEffect(() => {
    if (!keycloak.tokenParsed.ship_party_id) {
      return;
    }
    if (
      tcData
      && (!keycloak.tokenParsed.tc_version
        || keycloak.tokenParsed.tc_version
        != parseInt(tcData.OData__UIVersionString, 10))
    ) {
      setModalShow(true);
    } else {
      setModalShow(false);
    }
  }, [keycloak.tokenParsed.tc_version, tcData, keycloak.tokenParsed.ship_party_id]);

  const updateTCVersionAttribute = (conditionVersion) => {
    try {
      setModalShow(false);
      ga4EventTrigger && ga4EventTrigger("Landing T&C Wizard – Menu", "Landing Wizard", "Confirm T&C");
      (async function () {
        await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
          attributes: {
            tc_version: parseInt(conditionVersion, 10),
          },
        });
        if (keycloak.tokenParsed.is_owner_onboarded) window.location.reload(false);
      }());
    } catch (error) {
      console.log(error);
    }
  };

  const getTermAndCondition = async () => {
    try {
      const value = await spService.getTermCondition();
      setTCData(value.data.data);
      setTcLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  const checkedItem = (event) => {
    setCheckTC(event.target.checked);
  };

  const updateIsUserOnboardedAttribute = async () => {
    try {
      await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
        attributes: {
          is_owner_onboarded: true,
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleOverlayCallback = async (props: CallBackProps) => {
    const { index, status, action, lifecycle } = props;
    let actionString = "";
    if (lifecycle === 'complete' && ga4EventTrigger) {
      if (action === "prev") {
        actionString = "Confirm Back";
      } else if (action === "skip") {
        actionString = "Confirm Skip";
      }
      switch (index) {
        case 0:
          ga4EventTrigger("Landing Welcome Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm Welcome" : "Confirm Skip");
          break;
        case 1:
          if (hasOfrAccess(keycloak)) {
            ga4EventTrigger("Landing Financials Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm Financials" : actionString);
          } else {
            ga4EventTrigger("Landing CII Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm CII" : actionString);
          }
          break;
        case 2:
          ga4EventTrigger("Landing Itineraries Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm Itineraries" : actionString);
          break;
        case 3:
          ga4EventTrigger("Landing Notifications Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm Notifications" : actionString);
          break;
        default:
          ga4EventTrigger("Landing FAQ Wizard – Menu", "Landing Wizard", action === 'next' ? "Confirm FAQ" : "Confirm Back");
          break;
      }
    }
    if (status === 'finished' || status === 'skipped') {
      setRun(false);
      window.scrollTo(0, 0);
      await updateIsUserOnboardedAttribute();
    }
  };

  const getOverlays = () => {
    const overlaySteps = [
      {
          target: '#main-home-dashboard-container',
          content: <span>Welcome to the PARIS 2.0 Homepage! <br/><br/> This page provides you with a comprehensive overview of your vessels.<br/><br/> Click 'Next' to explore the features and learn more.</span>,
          disableBeacon: true,
          spotlightPadding: 0,
          placement: 'center' as const,
      },
      {
          target: '#landing_page_owner_finance',
          content: hasOfrAccess(keycloak) ? <span>View a snapshot of your financials<br/><br/>
            Monitor key financial information including monthly budget and actual performance of your vessels.</span> :
            <span>View your vessel's operational efficiency<br/><br/>
            Uncover the Carbon Intensity Indicator (CII) ratings of your vessel(s). Click into this dashboard for our ‘What-If’ machine learning feature,
            predicting your future CII rating.</span>,
          disableBeacon: true,
          spotlightPadding: 0,
          placement: 'bottom' as const,
      },
      {
          target: '#itineraries-card',
          content: <span>Stay updated with your vessels' Itineraries<br/><br/>
          View the estimated arrival dates and ports for each of your vessels. This helps you keep track of your vessels' movements and plan logistics accordingly.</span>,
          disableBeacon: true,
          spotlightPadding: 0,
          placement: 'bottom' as const,
      },
      {
          target: '#notification-card',
          content: <span>Notifications:<br/><br/>
          This feature keeps you informed about important updates and events related to your vessels. You'll receive notifications about monthly financial reports, crew changes, and other relevant information.</span>,
          disableBeacon: true,
          spotlightPadding: 0,
          placement: 'top' as const,
      },
      {
          target: '#faq-card',
          content: <span>Frequently Asked Questions (FAQs):<br/><br/>
          Here, you can find answers to FAQs about PARIS 2.0.<br/><br/>
          If you have any additional questions or need assistance, feel free to reach out to your Fleet contact.</span>,
          disableBeacon: true,
          spotlightPadding: 0,
          placement: 'top' as const,
      }
    ]

    return (
      <>
          <CustomOverlay
            run={run}
            steps={overlaySteps}
            floaterProps={{ hideArrow: true }}
            continuous
            handleOverlayCallback={handleOverlayCallback}
            styles={{
              options: {
                zIndex: 10000,
                width: 500,
              },
            }}
          />
        {/* <Backdrop show>
          <div />
        </Backdrop> */}
      </>
    );
  };

  return (
    <>
      <Modal
        show={modalShow}
        data-testid="term-condition-area"
        onHide={() => setModalShow(false)}
        aria-labelledby="confirmation-modal"
        dialogClassName="terms-condition-modal"
        centered
        backdrop="static"
        keyboard={false}
        scrollable
      >
        <Modal.Header>
          <Modal.Title id="confirmation-modal" style={{ borderBottom: "0" }}>
            PARIS 2.0 - Terms and Conditions
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {tcData && ReactHtmlParser(tcData.CanvasContent1)}
        </Modal.Body>
        <Modal.Footer style={{ borderTop: "0" }}>
          <Form.Check style={{ cursor: 'pointer' }}>
            <Form.Check.Input
              id="terms-condition-checkbox"
              type="checkbox"
              checked={checkTC}
              onChange={checkedItem}
            />
            <Form.Check.Label for="terms-condition-checkbox">Accept the conditions</Form.Check.Label>
          </Form.Check>
          <Button
            variant="secondary"
            disabled={!checkTC}
            onClick={() => {
              updateTCVersionAttribute(tcData.OData__UIVersionString);
            }}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
      <Container fluid className="landing-page-container owner-landing-page-container" id="main-home-dashboard-container">
        {isTutorialReadyToStart && getOverlays()}
        <Row className="justify-content-md-center tableau-widget widget-padding-top" id="tableau-widget" style={{ backgroundColor: 'transparent' }}>
             <Col className="d-block d-md-block d-lg-none">
                <OwnerGreetingWidget keycloak={keycloak} />
            </Col>
            <Col md={12} lg={8} className="world-map-container">
              <div className="world-map-container">
                <WorldMapWidget vesselList={vesselList} worldMapMarkerData={worldMapMarkerData} isLoadingVessel={isLoadingVessel} vesselItineraries={vesselItineraries} />
              </div>
              <div>
                <OwnerFinanceWidget
                  keycloak={keycloak}
                  vesselList={vesselList}
                  isLoadingVessel={isLoadingVessel}
                />
              </div>
            </Col>
            <Col md={12} lg={4} className="py-3">
                <div className="d-none d-lg-block d-xl-block">
                    <OwnerGreetingWidget keycloak={keycloak} />
                </div>
              <div ref={vesselListRef}>
                <GuardWrapper
                  hasAccess={hasAccessToVessel(keycloak)}
                  fallback={(
                    <Card body className="report-container widget-border">
                      <p className="report-title" ref={fleetStoriesRef}>
                        My Vessels
                      </p>
                      <NoAccess />
                    </Card>
                  )}
                >
                  <OwnerVesselList
                    vesselList={vesselList}
                    isLoadingVessel={isLoadingVessel}
                    shipPartyId={ship_party_id}
                    shipPartyType={ship_party_type}
                    keycloak={keycloak} />
                </GuardWrapper>
              </div>
              <div id='itineraries-card' ref={itinerariesRef}>
                <GuardWrapper
                  hasAccess={hasAccessToVessel(keycloak)}
                  fallback={(
                    <Card body className="report-container widget-border">
                      <p className="report-title">
                      Itineraries
                      </p>
                      <NoAccess />
                    </Card>
                  )}
                >
                  <OwnerVesselItineraries
                    keycloak={keycloak}
                    vesselItineraries={vesselItineraries}
                    isVesselItinerariesLoading={isVesselItinerariesLoading}
                    shipPartyId={ship_party_id} />
                </GuardWrapper>
              </div>
              {/* We are going to take this up in upcoming release and not the initial one, thus commenting it out for now
               <Card body className="widget-border owner-report-container">
                <Row>
                  <Col md={12} lg={6} className="owner-title">
                    <p className="report-title">
                      External Inspections
                    </p>
                  </Col>
                </Row>
              </Card> */}
            </Col>
        </Row>
      </Container>
    </>
  );
}

export { OwnerDashboard };
