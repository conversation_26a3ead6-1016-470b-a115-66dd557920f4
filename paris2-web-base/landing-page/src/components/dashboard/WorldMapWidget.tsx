import React, { useState, useEffect, useContext } from 'react';
import {
  GoogleMap, useJs<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow,
} from '@react-google-maps/api';
import { Tooltip, OverlayTrigger } from "react-bootstrap";
import moment from 'moment';
import _ from "lodash";
import { CloudSun } from 'react-bootstrap-icons';
import { Typeahead } from 'react-bootstrap-typeahead';
import VesselInfoWindow from './WorldMap/VesselInfoWindow';
import VesselTrackPolyline from './WorldMap/Polyline';
import CustomMapControl from '../common/CustomMapControl';
import {
  MARKER as ICON,
  WEATHER_MAP,
  MAP_CENTER,
  MAP_ZOOM,
} from '../../constants/world-map';
import * as vesselService from '../../services/vessel-service';
import { GlobalContext } from '../../context/dashboard-context';
import { getOwnershipVesselMap } from '../../utils/world-map';

const { GOOGLE_API_KEY } = process.env;

function WorldMapWidget({ vesselList = [], worldMapMarkerData = [], vesselItineraries = [], isLoadingVessel = false, isWorldMapPage = false, mapZoom = MAP_ZOOM , containerStyle={} }: { vesselList: vesselService.OwnershipDetails[], worldMapMarkerData: vesselService.VesselMarker[], vesselItineraries: any[], isLoadingVessel?: boolean, isWorldMapPage?: boolean, mapZoom?: number, containerStyle?:any }) {
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: GOOGLE_API_KEY as string,
  });

  const [map, setMap] = React.useState(null);
  const [selectedVesselIndex, setSelectedVesselIndex] = React.useState<number | null>(null);
  const [isShowInfo, setShowInfo] = React.useState(false);
  const [positionReport, setPositionReport] = React.useState<vesselService.VesselPositionReport | null>(null);
  const [itineraryData, setItineraryData] = React.useState<any>(null);
  const [isPositionLoading, setPositionLoading] = React.useState<boolean>(true);
  const { ga4EventTrigger } = useContext(GlobalContext);
  const [vesselTracks, setVesselTracks] = useState<vesselService.VesselTrack[] | null>(null);
  const [isVesselTrackLoading, setIsVesselTrackLoading] = React.useState<boolean>(false);
  const [mapFirstLoad, setMapFirstLoad] = useState(true);
  const [isToggleTrigerred, setIsToggleTrigerred] = useState(false);

  const ownershipVesselMap = getOwnershipVesselMap(vesselList);

  const MAP_CONTAINER_STYLE = {
    width: '100%',
    height: isWorldMapPage ? `calc(100vh - 6rem)` : '25rem',
    borderRadius: '4px',
    ...containerStyle
  };

  const ga4LinkEvent = (action: string) => ga4EventTrigger?.("Landing World Map – Link", "Landing World Map", action);
  /* #region Google Map */
  const onLoad = React.useCallback((map) => {
    map.setMapTypeId(google.maps.MapTypeId.HYBRID);
    setMap(map);
  }, []);

  const onUnmount = React.useCallback(() => {
    setMap(null);
  }, []);
  /* #endregion */

  useEffect(() => {
    if(worldMapMarkerData.length > 0) {
        setShowInfo(true);
    }
  }, [worldMapMarkerData])

  useEffect(() => {
      async function getVesselPositionReport() {
        const vessel = vesselList[selectedVesselIndex];
        if (vessel) {
          const { id: vesselOwnershipId } = vessel;
          const positionReportResult = await vesselService.getVesselPositionReport(vesselOwnershipId);
          setItineraryData(vesselItineraries.find((item) => item.vessel_id === vessel.vessel.id));setPositionReport(positionReportResult?.data?.results?.[0] ?? {});
          setPositionLoading(false);
        }
      }

      async function getVesselTrack() {
        const vessel = vesselList[selectedVesselIndex];
        if (vessel) {
          const { id: vesselOwnershipId, vessel: {id: vesselId} } = vessel;
          if (vessel.is_from_stratumFive) {
            const endTime = moment().utc().valueOf();
            const startTime = moment(endTime).subtract(7, 'day').valueOf();
            const { data } = await vesselService.getVesselStratumData(vesselId, startTime, endTime);
            const vesselTrackPerDay: vesselService.VesselTrack[] = _.isEmpty(data) ? null : filterVesselTrackByDay(data[vesselId]);
            setVesselTracks(vesselTrackPerDay);
            setIsVesselTrackLoading(false);
          } else {
            const endDate = moment().utc().valueOf();
            const startDate = moment(endDate).subtract(7, 'day').valueOf();
            const { data } = await vesselService.getVesselPositionReportLatLon(vesselOwnershipId, startDate, endDate);
            const vesselTrackPerDay = _.isEmpty(data) ? null : filterVesselTrackWithPositionReports(data[vesselOwnershipId]);
            setVesselTracks(vesselTrackPerDay);
            setIsVesselTrackLoading(false);
          }
        }
      }

      getVesselPositionReport();
      getVesselTrack();
    }, [selectedVesselIndex, vesselList]); // Or [] if effect doesn't need props or state

  const getColorByVesselType = (vesselType: string): {fillColor: string, strokeColor: string} => {
    switch (vesselType.toLowerCase()) {
      case 'tanker': // blue
        return {
          fillColor: '#32FBFA',
          strokeColor: '#64D9E3',
        };
      case 'bulk carrier':  // orange
        return {
          fillColor: '#FC9351',
          strokeColor: '#FC632A',
        };
      case 'container vessel': // green
        return {
          fillColor: '#64F58C',
          strokeColor: '#69CD85',
        };
      default:  // yellow
        return {
          fillColor: '#FDFA73',
          strokeColor: '#F7F215',
        };
    }
  };

  const filterVesselTrackByDay = (positions: vesselService.VesselStratum[]): vesselService.VesselTrack[] => {
    const vesselTrackPositions = positions.slice(0, positions.length -1).filter((position: vesselService.VesselStratum, index: number) => {
      const currentPositionDay = moment(_.toNumber(position.gpsTimestampEpoch));
      const nextPositionDay = moment(_.toNumber(positions[index + 1].gpsTimestampEpoch));
      const isSameDay = currentPositionDay.isSame(nextPositionDay, 'day');
      return !isSameDay;
    }).map((position: vesselService.VesselStratum) => ({
      vessel_stratum_id: position.id,
      vessel_id: position.vessel_id,
      lat: _.toNumber(position.lat),
      lng: _.toNumber(position.lon),
    }));
    return vesselTrackPositions;
  };

  const filterVesselTrackWithPositionReports = (positions): vesselService.VesselTrack[] => {
    const vesselTrackPositions = positions.map((position) => ({
      vessel_stratum_id: position.id,
      vessel_id: ownershipVesselMap[position.vessel_ownership_id],
      lat: _.toNumber(position.lat),
      lng: _.toNumber(position.lon),
    }));
    return vesselTrackPositions;
  };

  const handleVesselSelect = async (vessel) => {
    if (vessel.length > 0) {
      ga4EventTrigger?.("Landing World Map – List", "Landing World Map", "Filter of Vessel");
      const selectedVesselId = vessel[0].id;
      const selectedIndex = vesselList.findIndex(vessel => vessel.id === selectedVesselId);

      onClickVesselMarker({ vessel_id: selectedVesselId })
      setSelectedVesselIndex(selectedIndex);
      if (map) {
        map.setZoom(mapZoom);
      }
    } else {
      setSelectedVesselIndex(null);
      setPositionReport(null);
      setPositionLoading(true);
      setIsVesselTrackLoading(false);
      setVesselTracks(null);
    }
  };

  const onClickVesselMarker = (marker: vesselService.VesselMarker, triggerEvent: boolean = false) => {
    triggerEvent && ga4LinkEvent("Vessel Marker");
    const selectedVesselId = marker.vessel_id;
    const vesselIndex = vesselList.findIndex(vessel => vessel.vessel.id === selectedVesselId);
    if (vesselIndex === selectedVesselIndex) {
      return;
    }
    setSelectedVesselIndex(vesselIndex);
    setVesselTracks(null);
    setIsVesselTrackLoading(true);
  };

  const renderTooltip = (
    <Tooltip id="weather-tooltip">
      Weather
    </Tooltip>
  );

  return isLoaded ? (
      <GoogleMap
        mapContainerStyle={MAP_CONTAINER_STYLE}
        center={MAP_CENTER}
        zoom={mapZoom}

        onLoad={onLoad}
        onUnmount={onUnmount}
        options={{
          scaleControl: true,
          streetViewControl: false,
          zoomControl: true,
          zoomControlOptions: {
            position: google.maps.ControlPosition.LEFT_BOTTOM,
          },
          mapTypeControl: true,
          mapTypeControlOptions: {
            style: google.maps.MapTypeControlStyle.DEFAULT,
            mapTypeIds: [
              google.maps.MapTypeId.SATELLITE,
              google.maps.MapTypeId.HYBRID,
              google.maps.MapTypeId.ROADMAP,
              google.maps.MapTypeId.TERRAIN,
            ],
          },
          fullscreenControl: true,
          rotateControl: false,
        }}
        onTilesLoaded={() => {
            if(mapFirstLoad){
              setMapFirstLoad(!mapFirstLoad);
            } else if (isToggleTrigerred) {
              setIsToggleTrigerred(false);
            } else {
              ga4LinkEvent("Click Full Screen");
            }
          }
        }
        onMapTypeIdChanged={() => {
            if(!mapFirstLoad){
                ga4LinkEvent("Toggle");
                setIsToggleTrigerred(true);
            }
          }
        }
      >
        <CustomMapControl position="RIGHT_TOP">
          <div className="world-map-vessel-list">
            <Typeahead
              placeholder="Filter by Vessel Name"
              labelKey={(e: { name: string }) => (e.name ?? '')}
              id="vessel_id"
              emptyLabel={isLoadingVessel ? 'Loading...' : 'No matches found.'}
              options={vesselList}
              disabled={false}
              isInvalid={false}
              size="lg"
              onChange={(e) => handleVesselSelect(e)}
              selected={selectedVesselIndex !== null ? [vesselList[selectedVesselIndex]] : []}
            />
          </div>
          <OverlayTrigger placement="bottom" overlay={renderTooltip}>
            <div className={`text-center`}>
              <button
                type="button"
                className="weather-button"
                data-testid="testid-weather-button"
                onClick={() => { ga4LinkEvent("Click Weather"); window.open(WEATHER_MAP, '_blank');}}
              >
                <CloudSun size={20} />
              </button>
            </div>
          </OverlayTrigger>
        </CustomMapControl>
        {/* marker  */}
        {worldMapMarkerData.length > 0
          && worldMapMarkerData.map((m: vesselService.VesselMarker, i:number) => (
            <Marker
              position={{ lat: m.lat, lng: m.lng }}
              key={m.vessel_ownership_id}
              onClick={() => onClickVesselMarker(m, true)}
              label={{
                text: m.vessel_short_code,
                className: 'marker',
                color: 'white',
                fontSize: '12px',
              }}
              icon={{
                path: m.isParking ? ICON.pathAnchored : ICON.normalPath,
                scale: 0.3,
                strokeColor: getColorByVesselType(m.vessel_type).strokeColor,
                fillColor: getColorByVesselType(m.vessel_type).fillColor,
                fillOpacity: 5,
                rotation: m.cog,
              }}
            >
              {(isShowInfo && selectedVesselIndex !== null && m.vessel_id === vesselList[selectedVesselIndex].vessel.id) ? (
                <InfoWindow onCloseClick={() => {
                  setSelectedVesselIndex(null);
                  setPositionReport(null);
                  setPositionLoading(true);
                }}
                options={{ pixelOffset: new window.google.maps.Size(0, -40)}}
                >
                  <VesselInfoWindow
                    vesselData={vesselList[selectedVesselIndex]}
                    positionData={positionReport}
                    isPositionLoading={isPositionLoading}
                    itineraryData={itineraryData}
                  />
                </InfoWindow>
              ) : null}
              {(selectedVesselIndex !== null && !isVesselTrackLoading && vesselTracks && m.vessel_id === vesselList[selectedVesselIndex].vessel.id ) ? (
                <VesselTrackPolyline vesselTracksData={vesselTracks} />
              ): null}
            </Marker>
          ))}
      </GoogleMap>
  ) : (
    <></>
  );
}

export default WorldMapWidget;
