import React, { useState, useMemo, useCallback } from 'react';
import { RotateCw } from 'lucide-react';
import classNames from 'classnames';
import { VesselModuleProps, Vessel, MultiVesselSelectConfig } from '../../../types/types';
import VesselTable from './VesselTable';
import VesselGrid from './VesselGrid';
import { VesselModuleHeader } from './VesselModuleHeader';
import { VesselSelectors } from './VesselSelectors';
import { ModuleModal } from './ModuleModal';
import '../styles/vessel-widget-scss/VesselModule.scss';

// Define a type for our sorting configuration
type SortConfig = {
  key: string;
  direction: 'ascending' | 'descending';
} | null;

// Helper function to handle filtering logic
const filterVessels = (
  vesselsToFilter: Vessel[],
  selectStates: string[][],
  multiVesselSelects: MultiVesselSelectConfig[],
) => {
  let filteredData = vesselsToFilter;

  // Filter 1: Vessel Name
  const vesselNameSelections = selectStates[0];
  if (vesselNameSelections?.length > 0) {
    const allVesselOptions = multiVesselSelects[0]?.groups.flatMap((g) => g.vessels) || [];
    const selectedVesselIdentifiers = new Set(
      allVesselOptions
        .filter((opt) => vesselNameSelections.includes(opt.name))
        .map((opt) => `${opt.vessel_id}-${opt.vessel_ownership_id}`),
    );
    if (selectedVesselIdentifiers.size > 0) {
      filteredData = filteredData.filter((vessel) =>
        selectedVesselIdentifiers.has(`${vessel.vessel_id}-${vessel.vessel_ownership_id}`),
      );
    }
  }

  // Filter 2: Level RA
  const levelRaSelections = selectStates[1];
  if (levelRaSelections?.length > 0) {
    filteredData = filteredData.filter((vessel: Vessel) => {
      const vesselLevelOfRa = vessel.vesselData[1];
      return levelRaSelections.includes(vesselLevelOfRa as string);
    });
  }

  return filteredData;
};

// Helper function to handle sorting logic
const sortVessels = (vesselsToSort: Vessel[], sortConfig: SortConfig, tableHeaders: string[]) => {
  if (!sortConfig) {
    return vesselsToSort;
  }

  const headerIndex = tableHeaders.indexOf(sortConfig.key);
  if (headerIndex === -1) {
    return vesselsToSort;
  }

  const sortedData = [...vesselsToSort].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    if (headerIndex === 0) {
      aValue = a.name;
      bValue = b.name;
    } else {
      aValue = a.vesselData[headerIndex - 1];
      bValue = b.vesselData[headerIndex - 1];
    }

    if (aValue < bValue) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  return sortedData;
};

export default function VesselModule({
  title,
  vessels, // The vessels array from API
  tabs,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  IsVesselSelectVisible,
  IsAllTabVisible,
  multiVesselSelects = [],
  vesselSelectPosition = 'before',
  containerSize,
  modalSize,
  tableHeaders,
  badgeColors,
  onRefresh,
  gridComponent,
  defaultComponent = 'list',
  cellStyleType,
  ...displayProps
}: Readonly<VesselModuleProps>) {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>(defaultComponent);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [selectStates, setSelectStates] = useState<string[][]>(() =>
    multiVesselSelects.map(() => []),
  );

  const containerStyle = {
    '--container-width': containerSize?.width,
    '--container-height': containerSize?.height,
  };

  const [sortConfig, setSortConfig] = useState<SortConfig>(null);

  const processedVessels = useMemo(() => {
    let data = vessels || [];
    data = filterVessels(data, selectStates, multiVesselSelects);
    data = sortVessels(data, sortConfig, tableHeaders);
    return data;
  }, [vessels, selectStates, multiVesselSelects, sortConfig, tableHeaders]);

  const handleRefresh = useCallback(() => {
    onRefresh();
    setLastUpdated(new Date());
  }, [onRefresh]);

  const handleSelectChange = useCallback((index: number, newSelected: string[]) => {
    setSelectStates((prevStates) => {
      const newStates = [...prevStates];
      newStates[index] = newSelected;
      return newStates;
    });
  }, []);

  const handleSort = useCallback((key: string) => {
    setSortConfig((currentSortConfig) => {
      let direction: 'ascending' | 'descending' = 'ascending';
      if (
        currentSortConfig &&
        currentSortConfig.key === key &&
        currentSortConfig.direction === 'ascending'
      ) {
        direction = 'descending';
      }
      return { key, direction };
    });
  }, []);

  // Determine if vessels data is available
  const hasVesselsData = vessels && vessels.length > 0;

  // Conditionally set IsiconRenderVisible and IsenLargeIconVisible
  const finalIsiconRenderVisible = hasVesselsData ? IsiconRenderVisible : false;
  const finalIsenLargeIconVisible = hasVesselsData ? IsenLargeIconVisible : false;

  const renderViewContent = (isModal: boolean) =>
    viewMode === 'list' ? (
      <VesselTable
        vessels={processedVessels}
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        cellStyleType={cellStyleType}
        sortConfig={sortConfig}
        onSort={handleSort}
        {...displayProps}
      />
    ) : (
      <VesselGrid
        vessels={processedVessels}
        tableHeaders={tableHeaders}
        badgeColors={badgeColors}
        isModal={isModal}
        gridComponent={gridComponent}
        {...displayProps}
      />
    );

  const renderModuleCore = (isModal: boolean) => (
    <>
      <VesselModuleHeader
        title={title}
        viewMode={viewMode}
        isModal={isModal}
        IsiconRenderVisible={finalIsiconRenderVisible} // Use the conditionally set value
        IsenLargeIconVisible={finalIsenLargeIconVisible} // Use the conditionally set value
        onViewModeChange={setViewMode}
        onToggleModal={() => setIsModalOpen(!isModalOpen)}
      />

      <div className="ra-last-updated-container">
        <p className="ra-last-updated-text">
          Last Updated:{' '}
          {`${lastUpdated.toLocaleDateString(undefined, {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })} ${lastUpdated.toLocaleTimeString(undefined, {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          })}`}
          <RotateCw onClick={handleRefresh} className="ra-refresh-icon" />
        </p>
      </div>

      {/* Render VesselSelectors only if there is vessel data or if it's explicitly visible */}
      {IsVesselSelectVisible && vesselSelectPosition === 'before' && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}

      {IsVesselSelectVisible && vesselSelectPosition === 'after' && (
        <VesselSelectors
          multiVesselSelects={multiVesselSelects}
          selectStates={selectStates}
          onSelectChange={handleSelectChange}
        />
      )}

      <div
        className={classNames('ra-content-container', {
          'ra-content-container-modal': isModal,
          'ra-content-container-non-modal': !isModal,
        })}
      >
        {renderViewContent(isModal)}
      </div>
    </>
  );

  return (
    <>
      <div className="ra-vessel-module-container" style={containerStyle}>
        {renderModuleCore(false)}
      </div>
      <ModuleModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} size={modalSize}>
        {renderModuleCore(true)}
      </ModuleModal>
    </>
  );
}