import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ModuleModal } from '../ModuleModal';

describe('ModuleModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    size: {
      width: '800px',
      height: '600px',
    },
    children: <div>Modal Content</div>,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render when isOpen is true', () => {
    render(<ModuleModal {...defaultProps} />);
    
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Close modal' })).toBeInTheDocument();
  });

  it('should not render when isOpen is false', () => {
    render(<ModuleModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: 'Close modal' })).not.toBeInTheDocument();
  });

  it('should call onClose when overlay is clicked', () => {
    const onClose = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const overlay = screen.getByRole('button', { name: 'Close modal' });
    fireEvent.click(overlay);
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('should not call onClose when modal content is clicked', () => {
    const onClose = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const modalContent = screen.getByText('Modal Content');
    fireEvent.click(modalContent);
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('should apply custom size styles', () => {
    const customSize = {
      width: '1000px',
      height: '800px',
    };
    
    render(<ModuleModal {...defaultProps} size={customSize} />);
    
    const modalContent = screen.getByText('Modal Content').parentElement;
    expect(modalContent).toHaveStyle({
      '--modal-width': '1000px',
      '--modal-height': '800px',
    });
  });

  it('should render children correctly', () => {
    const children = (
      <div>
        <h1>Test Title</h1>
        <p>Test paragraph</p>
        <button>Test Button</button>
      </div>
    );
    
    render(<ModuleModal {...defaultProps}>{children}</ModuleModal>);
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test paragraph')).toBeInTheDocument();
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  it('should have correct CSS classes', () => {
    render(<ModuleModal {...defaultProps} />);
    
    const overlay = screen.getByRole('button', { name: 'Close modal' });
    expect(overlay).toHaveClass('ra-modal-overlay');
    
    const modalContent = screen.getByText('Modal Content').parentElement;
    expect(modalContent).toHaveClass('ra-modal-content');
  });

  it('should handle undefined size gracefully', () => {
    const propsWithUndefinedSize = {
      ...defaultProps,
      size: undefined as any,
    };
    
    render(<ModuleModal {...propsWithUndefinedSize} />);
    
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('should handle partial size object', () => {
    const partialSize = {
      width: '500px',
      height: undefined as any,
    };
    
    render(<ModuleModal {...defaultProps} size={partialSize} />);
    
    const modalContent = screen.getByText('Modal Content').parentElement;
    expect(modalContent).toHaveStyle({
      '--modal-width': '500px',
    });
  });

  it('should stop propagation on modal content click', () => {
    const onClose = jest.fn();
    render(<ModuleModal {...defaultProps} onClose={onClose} />);
    
    const modalContent = screen.getByText('Modal Content').parentElement;
    const clickEvent = new MouseEvent('click', { bubbles: true });
    const stopPropagationSpy = jest.spyOn(clickEvent, 'stopPropagation');
    
    fireEvent(modalContent!, clickEvent);
    
    expect(stopPropagationSpy).toHaveBeenCalled();
    expect(onClose).not.toHaveBeenCalled();
  });

  it('should handle keyboard accessibility', () => {
    render(<ModuleModal {...defaultProps} />);
    
    const overlay = screen.getByRole('button', { name: 'Close modal' });
    expect(overlay).toHaveAttribute('type', 'button');
    expect(overlay).toHaveAttribute('aria-label', 'Close modal');
  });

  it('should render with complex children', () => {
    const complexChildren = (
      <div>
        <header>Header</header>
        <main>
          <section>Section 1</section>
          <section>Section 2</section>
        </main>
        <footer>Footer</footer>
      </div>
    );
    
    render(<ModuleModal {...defaultProps}>{complexChildren}</ModuleModal>);
    
    expect(screen.getByText('Header')).toBeInTheDocument();
    expect(screen.getByText('Section 1')).toBeInTheDocument();
    expect(screen.getByText('Section 2')).toBeInTheDocument();
    expect(screen.getByText('Footer')).toBeInTheDocument();
  });

  it('should handle multiple modal instances', () => {
    const onClose1 = jest.fn();
    const onClose2 = jest.fn();
    
    render(
      <div>
        <ModuleModal
          isOpen={true}
          onClose={onClose1}
          size={{ width: '400px', height: '300px' }}
        >
          <div>Modal 1</div>
        </ModuleModal>
        <ModuleModal
          isOpen={true}
          onClose={onClose2}
          size={{ width: '600px', height: '500px' }}
        >
          <div>Modal 2</div>
        </ModuleModal>
      </div>
    );
    
    expect(screen.getByText('Modal 1')).toBeInTheDocument();
    expect(screen.getByText('Modal 2')).toBeInTheDocument();
    
    const overlays = screen.getAllByRole('button', { name: 'Close modal' });
    expect(overlays).toHaveLength(2);
  });
});
