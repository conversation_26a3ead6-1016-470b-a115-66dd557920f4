import React, { useEffect, useState } from 'react';
import VesselModule from './VesselModule';
import useInfiniteQuery from '../../../hooks/useInfiniteQuery';
import {
  Vessel,
  MultiVesselSelectConfig,
  VesselModuleContainerProps,
} from '../../../types/types';
import { fetchVesselOwnerships } from '../../../services/risk-widget-service';
import { vesselGroups2 } from 'src/services/__mocks__/vessel-module-config';

export default function VesselModuleContainer({
  title,
  fetchFn,
  tabs,
  gridComponent,
  defaultComponent,
  cellStyleType,
  ...rest
}: Readonly<VesselModuleContainerProps>) {
  const { data, isLoading, isFetchingNextPage, fetchNextPage, refetch } = useInfiniteQuery(
    fetchFn,
    { limit: 20 },
  );

  const [multiVesselSelectsSet1, setMultiVesselSelectsSet1] = useState<
    MultiVesselSelectConfig[]
  >([]);

  useEffect(() => {
    async function loadVessels() {
      const groups = await fetchVesselOwnerships();

      setMultiVesselSelectsSet1((prev) => {
        const newState = [
          {
            placeholder: 'All Vessels',
            width: '300px',
            groups: groups,
            isSearchBoxVisible: true,
            isSelectAllVisible: true,
          },
          {
            placeholder: 'Level of R.A.',
            width: '300px',
            groups: vesselGroups2,
            isSearchBoxVisible: false,
            isSelectAllVisible: false,
          },
        ];

        if (JSON.stringify(prev) !== JSON.stringify(newState)) {
          return newState;
        }
        return prev;
      });
    }
    loadVessels();
  }, []);

  const handleSendEmail = (vessel: Vessel) => {
    if (!vessel.vessel_ownership_id) return;
    const url = `https://paris2-dev2.fleetship.com/risk-assessment/approval/${vessel.risk_id}`;
    window.open(url, '_blank');
  };

  const handleVesselClick = (vessel: Vessel) => {
    if (!vessel.vessel_ownership_id) return;
    const url = `https://paris2-dev2.fleetship.com/vessel/ownership/details/${vessel.vessel_ownership_id}`;
    window.open(url, '_blank');
  };

  const vessels = data?.data ?? [];
  const pagination = data?.pagination ?? {
    totalItems: 0,
    totalPages: 0,
    page: 0,
    pageSize: 0,
  };

  const IsAllTabVisible = tabs.length === 0;

  return (
    <VesselModule
      title={title}
      vessels={vessels}
      tabs={tabs}
      multiVesselSelects={multiVesselSelectsSet1}
      IsAllTabVisible={IsAllTabVisible}
      pagination={pagination}
      isLoading={isLoading}
      isFetchingNextPage={isFetchingNextPage}
      fetchNextPage={fetchNextPage}
      onRefresh={refetch}
      onSendEmail={handleSendEmail}
      onVesselClick={handleVesselClick}
      gridComponent={gridComponent}
      defaultComponent={defaultComponent}
      cellStyleType={cellStyleType}
      {...rest}
    />
  );
}