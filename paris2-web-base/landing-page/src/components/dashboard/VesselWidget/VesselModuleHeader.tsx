import React from 'react';
import { LayoutGrid, List, Minimize2 } from 'lucide-react';
import classNames from 'classnames';
import { EnlargeIcon } from './svgIcons';

interface VesselModuleHeaderProps {
  title: string;
  viewMode: 'list' | 'grid';
  isModal: boolean;
  IsiconRenderVisible?: boolean;
  IsenLargeIconVisible?: boolean;
  onViewModeChange: (mode: 'list' | 'grid') => void;
  onToggleModal: () => void;
}

export const VesselModuleHeader: React.FC<VesselModuleHeaderProps> = ({
  title,
  viewMode,
  isModal,
  IsiconRenderVisible,
  IsenLargeIconVisible,
  onViewModeChange,
  onToggleModal,
}) => {
  return (
    <div className="ra-vessel-module-header">
      <h2 className="ra-vessel-module-title">{title}</h2>
      <div className="ra-vessel-module-controls">
        {IsiconRenderVisible && (
          <div className="ra-view-toggle-container">
            <button
              onClick={() => onViewModeChange('grid')}
              className={classNames('ra-view-toggle-button', {
                active: viewMode === 'grid',
              })}
              aria-label="Grid view"
            >
              <LayoutGrid className="ra-view-toggle-icon" />
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={classNames('ra-view-toggle-button', {
                active: viewMode === 'list',
              })}
              aria-label="List view"
            >
              <List className="ra-view-toggle-icon" />
            </button>
          </div>
        )}

        {IsenLargeIconVisible &&
          (isModal ? (
            <Minimize2
              className="ra-enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ) : (
            <EnlargeIcon
              className="ra-enlarge-icon"
              onClick={onToggleModal}
              aria-label="Minimize view"
            />
          ))}
      </div>
    </div>
  );
};