import React from 'react';
import { VesselSelectGroup } from './VesselSelectGroup';
import { MultiVesselSelectConfig } from '../../../types/types';

interface VesselSelectorsProps {
  multiVesselSelects: MultiVesselSelectConfig[];
  selectStates: string[][];
  onSelectChange: (selectIndex: number, newSelection: string[]) => void;
}

/**
 * This component is the CONTAINER for all the dropdowns.
 * It maps over the configurations and renders a VesselSelectGroup for each.
 */
export const VesselSelectors: React.FC<VesselSelectorsProps> = ({
  multiVesselSelects,
  selectStates,
  onSelectChange,
}) => {
  if (!Array.isArray(multiVesselSelects)) {
    return null;
  }

  return (
    <div className="ra-vessel-selects-container">
      {multiVesselSelects.map((select, originalIndex) => {
       
        if (!select?.groups) {
          return null;
        }
        return (
          <VesselSelectGroup
            // Use a unique ID from the data if available.
            // If not, a composite key or a unique string is better than just the index.
            // Assuming 'select.placeholder' is unique, or you have another unique property.
            key={select.placeholder || originalIndex}
            index={originalIndex}
            config={{
              placeholder: select.placeholder,
              width: select.width,
            }}
            selectedVessels={selectStates[originalIndex] || []}
            groups={select.groups}
            onChange={onSelectChange}
            isSearchBoxVisible={select.isSearchBoxVisible}
            isSelectAllVisible={select.isSelectAllVisible}
          />
        );
      })}
    </div>
  );
};