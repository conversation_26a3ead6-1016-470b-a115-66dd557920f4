import React from 'react';
import classNames from 'classnames';
import '../styles/vessel-widget-scss/VesselModule.scss';

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  size: {
    width: string;
    height: string;
  };
  children: React.ReactNode;
}

/**
 * A generic, reusable modal component.
 */
export const ModuleModal: React.FC<ModuleModalProps> = ({ isOpen, onClose, size, children }) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Close the modal only if the click is on the overlay itself, not its children
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <button
      type="button"
      className="ra-modal-overlay"
      onClick={handleOverlayClick}
      aria-label="Close modal"
    >
      <div
        className={classNames('ra-modal-content')}
        style={
          {
            ['--modal-width' as any]: size?.width,
            ['--modal-height' as any]: size?.height,
          } as React.CSSProperties
        }
        onClick={(e) => e.stopPropagation()} // Prevent click from bubbling to overlay
        onKeyDown={(e) => {
          // Prevent keyboard event from bubbling to overlay
          e.stopPropagation();
        }}
        tabIndex={0}
      >
        {children}
      </div>
    </button>
  );
};
