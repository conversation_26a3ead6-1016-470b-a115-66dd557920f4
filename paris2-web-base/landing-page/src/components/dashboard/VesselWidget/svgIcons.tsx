import React from 'react';

export const EnlargeIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props} // Spread all passed props like className, onClick, aria-label, etc.
    >
      <path
        d="M17.6929 3.94936V8.23187H19V1.7327H12.5008V3.03977H16.7833L2.30707 17.516V13.2335H1V19.7327H7.49918V18.4256H3.21667L17.6929 3.94936Z"
        fill="#1F4A70"
      />
    </svg>
  );
};

export const ExternalLinkIcon = (props: React.SVGAttributes<SVGElement>) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props} // Spread all props for flexibility
  >
    <path
      d="M15.3337 17.7987H4.66699C4.13656 17.7987 3.62785 17.588 3.25278 17.213C2.87771 16.8379 2.66699 16.3292 2.66699 15.7987V5.13208C2.66699 4.60165 2.87771 4.09294 3.25278 3.71787C3.62785 3.34279 4.13656 3.13208 4.66699 3.13208H8.66699C8.8438 3.13208 9.01337 3.20232 9.1384 3.32734C9.26342 3.45237 9.33366 3.62194 9.33366 3.79875C9.33366 3.97556 9.26342 4.14513 9.1384 4.27015C9.01337 4.39518 8.8438 4.46541 8.66699 4.46541H4.66699C4.49018 4.46541 4.32061 4.53565 4.19559 4.66068C4.07056 4.7857 4.00033 4.95527 4.00033 5.13208V15.7987C4.00033 15.9756 4.07056 16.1451 4.19559 16.2702C4.32061 16.3952 4.49018 16.4654 4.66699 16.4654H15.3337C15.5105 16.4654 15.68 16.3952 15.8051 16.2702C15.9301 16.1451 16.0003 15.9756 16.0003 15.7987V11.7987C16.0003 11.6219 16.0706 11.4524 16.1956 11.3273C16.3206 11.2023 16.4902 11.1321 16.667 11.1321C16.8438 11.1321 17.0134 11.2023 17.1384 11.3273C17.2634 11.4524 17.3337 11.6219 17.3337 11.7987V15.7987C17.3337 16.3292 17.1229 16.8379 16.7479 17.213C16.3728 17.588 15.8641 17.7987 15.3337 17.7987ZM8.66699 12.4654C8.57925 12.4659 8.49228 12.4491 8.41105 12.4159C8.32983 12.3828 8.25595 12.3339 8.19366 12.2721C8.13117 12.2101 8.08158 12.1364 8.04773 12.0551C8.01389 11.9739 7.99646 11.8868 7.99646 11.7987C7.99646 11.7107 8.01389 11.6236 8.04773 11.5424C8.08158 11.4611 8.13117 11.3874 8.19366 11.3254L15.0603 4.46541H12.667C12.4902 4.46541 12.3206 4.39518 12.1956 4.27015C12.0706 4.14513 12.0003 3.97556 12.0003 3.79875C12.0003 3.62194 12.0706 3.45237 12.1956 3.32734C12.3206 3.20232 12.4902 3.13208 12.667 3.13208H16.667C16.8438 3.13208 17.0134 3.20232 17.1384 3.32734C17.2634 3.45237 17.3337 3.62194 17.3337 3.79875V7.79875C17.3337 7.97556 17.2634 8.14513 17.1384 8.27015C17.0134 8.39518 16.8438 8.46541 16.667 8.46541C16.4902 8.46541 16.3206 8.39518 16.1956 8.27015C16.0706 8.14513 16.0003 7.97556 16.0003 7.79875V5.40541L9.14033 12.2721C9.07803 12.3339 9.00415 12.3828 8.92293 12.4159C8.8417 12.4491 8.75473 12.4659 8.66699 12.4654Z"
      fill="#1F4A70"
    />
  </svg>
);
