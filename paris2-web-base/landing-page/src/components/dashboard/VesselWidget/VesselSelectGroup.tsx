import React from 'react';
import { VesselSelectGroupProps } from '../../../types/types';
import { VesselDropdown } from './VesselDropdown';
import styles from '../styles/vessel-widget-scss/VesselSelectGroup.module.scss';

/**
 * A simple wrapper component for the VesselDropdown.
 * Wrapped with React.memo because it's a pure component.
 * It will only re-render if its props change.
 */
export const VesselSelectGroup: React.FC<VesselSelectGroupProps> = React.memo(
  ({
    index,
    config,
    selectedVessels,
    groups,
    onChange,
    isSearchBoxVisible,
    isSelectAllVisible,
  }) => {
    // Move the hook to the top, before any conditionals
    const handleSelectionChange = React.useCallback(
      (newSelected: readonly string[]) => {
        onChange(index, newSelected as string[]);
      },
      [index, onChange],
    );

    if (!groups) {
      console.error('VesselSelectGroup: `groups` prop is missing.');
      return null;
    }

    return (
      <div className={styles.raSelectWrapper}>
        <VesselDropdown
          groups={groups}
          selectedVessels={selectedVessels}
          onSelectionChange={handleSelectionChange}
          placeholder={config.placeholder}
          width={config.width ?? '200px'}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      </div>
    );
  },
);