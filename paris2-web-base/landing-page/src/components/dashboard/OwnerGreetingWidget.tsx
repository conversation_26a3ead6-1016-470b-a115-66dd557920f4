/* eslint-disable max-len */
/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect } from 'react';
import { Col, Row, Card, Spinner } from 'react-bootstrap';
import './styles/owner-finance.scss';
import { Keycloak } from '../../types/keycloak';
import _ from 'lodash';
import {
  getCurrentTimeZone,
  getLocalDateStr,
  getLocalGreetingsString,
  getUtcDateStr,
} from '../../utils/datetime-utils';
import { getCapitalizedName } from '../../utils/dashboard';
import { getCurrentWeather } from 'src/utils/weather-utils';
import { getShipPartyDetails } from 'src/services/ship-party-service';
import { getCountries } from 'src/services/reference-service';
import { DEFAULT_WEATHER_COUNTRY } from 'src/constants/widgets';

interface Props {
  keycloak: Keycloak;
}
type LoadingType = 'initial' | 'loading' | 'loaded';
type WeatherData = {
  location: { name: string };
  current: { temp_c: number; condition: { text: string } };
  forecast: {
    forecastday: { day: { maxtemp_c: number; mintemp_c: number } }[];
  };
};
const OwnerGreetingWidget = ({ keycloak }: Props) => {
  const { given_name, family_name, ip_address ,ship_party_id} = keycloak.tokenParsed;
  const ownerName: string = given_name || family_name || '';
  const capitalizedOwnerName: string = getCapitalizedName(ownerName);
  const currentUtcTime: string = getUtcDateStr(new Date(), 'HH:mm');
  const currentLocalTime: string = getLocalDateStr(new Date(), 'HH:mm');
  const currentLocalTimeZone: string = getCurrentTimeZone();
  const [utcTime, setUtcTime] = useState(currentUtcTime);
  const [localTime, setLocalTime] = useState(currentLocalTime);
  const [weather, setWeather] = useState<{ loadingState: LoadingType; data: WeatherData }>({
    loadingState: 'initial',
    data: {},
  });
  const setWeatherHandler = (val: { loadingState?: LoadingType; data?: WeatherData }) => {
    setWeather((prevState) => ({ ...prevState, ...val }));
  };
  const getWeatherInfo = async () => {
    let query = '';
    try {
      setWeatherHandler({ loadingState: 'loading' });

      if (ip_address) {
        query = ip_address;
      } else if (ship_party_id) {
        const shipPartyDetails = await getShipPartyDetails(ship_party_id);
        const countryCode = shipPartyDetails?.data?.result?.iso_country_code || null;

        if (countryCode) {
          const countriesData = await getCountries();
          const countryName =
            countriesData.data.countries.find((c) => c.alpha2_code === countryCode)?.value || null;

          query = countryName ?? DEFAULT_WEATHER_COUNTRY;
        } else {
          query = DEFAULT_WEATHER_COUNTRY;
        }
      } else {
        query = DEFAULT_WEATHER_COUNTRY;
      }

      const weatherData = await getCurrentWeather(query);
      setWeatherHandler({ data: weatherData });
    } catch (e) {
    } finally {
      setWeatherHandler({ loadingState: 'loaded' });
    }
  };
  
  useEffect(() => {
    getWeatherInfo()
  }, [ip_address]);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setUtcTime(getUtcDateStr(new Date(), 'HH:mm'));
      setLocalTime(getLocalDateStr(new Date(), 'HH:mm'));
    }, 1000);
    return () => {
      clearInterval(interval);
    };
  }, []);
  const getWeatherWidget = () => {
    if (weather.loadingState === 'loading') {
      return <Spinner animation="border" role="status" />;
    } else {
      const weatherData = weather.data;
      const currentTemp = _.round(weatherData?.current?.temp_c);
      let highTemp = _.round(weatherData?.forecast?.forecastday?.[0]?.day?.maxtemp_c) ?? 'N/A';
      let lowTemp = _.round(weatherData?.forecast?.forecastday?.[0]?.day?.mintemp_c) ?? 'N/A';
      if (currentTemp > highTemp) {
        highTemp = currentTemp;
      }
      if (currentTemp < lowTemp) {
        lowTemp = currentTemp;
      }
      const weatherInfo = {
        location: weatherData?.location?.name ?? 'N/A',
        temp: currentTemp ?? 'N/A',
        highTemp: highTemp ?? 'N/A',
        lowTemp: lowTemp ?? 'N/A',
        type: weatherData?.current?.condition?.text,
      };
      return (
        <div className="weather-temp-container">
          <div className="weather-temp-wrap">
            <div>
              <div className="location no-wrap">{weatherInfo.location}</div>
              <div className="user-time-temp">{weatherInfo.temp}&deg;C</div>
            </div>
            <dl className="temperature-wrap">
              <dt className="weather-wind no-wrap">{weatherInfo.type}</dt>
              <dd className="weather-temp">
                <div className="weather-temp-val">H: {weatherInfo.highTemp}&deg;C</div>
                <div className="weather-temp-val">L: {weatherInfo.lowTemp}&deg;C</div>
              </dd>
            </dl>
          </div>
        </div>
      );
    }
  };
  return (
    <>
      <Card body className="widget-border owner-report-container greeting-container">
        <Row>
          <Col sm={10} md={9} lg={7} xl={8} className="pr-0">
            <div className="owner-greeting">
              <div className="greeting-title">
                <div>
                  {getLocalGreetingsString()}
                  {capitalizedOwnerName.length > 0 ? ',' : ''}
                </div>
                <div className="no-wrap">{capitalizedOwnerName}</div>
              </div>
              <Row>
                <Col>
                  <span className="user-time-temp">{localTime}</span>
                  <span className="time-zone">{currentLocalTimeZone}</span>
                  <span className="utc-time-temp">{utcTime}</span>
                  <span className="time-zone">UTC</span>
                </Col>
              </Row>
            </div>
          </Col>
          <Col sm={2} md={3} lg={5} xl={4} className="pl-0">
            {getWeatherWidget()}
          </Col>
        </Row>
      </Card>
    </>
  );
};

export { OwnerGreetingWidget };
