/* eslint-disable max-len */
/* eslint-disable no-underscore-dangle */
import React, { useRef, useContext } from "react";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import "./styles/owner-finance.scss";
import { DashboardWrapper } from "../common/DashboardWrapper";
import { DefaultDashboardFilters, TableauLandingWidgets } from "../../constants/widgets";
import { useTableauTokens } from "../../hooks/useTableauToken";
import { useDashboardLoader } from "../../hooks/useDashboardLoader";
import { Keycloak } from "../../types/keycloak";
import { isOwnerOrRegisteredOwner, hasOfrAccess } from "../../utils/roles";
import { GlobalContext } from '../../context/dashboard-context';

interface Props {
  vesselList: any[];
  keycloak: Keycloak;
  isLoadingVessel: boolean;
}

const DEFAULT_NUMBER_OF_DASHBOARDS = 1;

const { PARIS_TWO_HOST } = process.env;

const OwnerFinanceWidget = ({
  vesselList, keycloak, isLoadingVessel = false,
}: Props) => {
  const vesselNames = vesselList.length === 0 ? 'null' : vesselList?.map((vessel: any) => vessel.name).join(',') ?? '';
  const vesselNamesFilter = isOwnerOrRegisteredOwner(keycloak) ?
    (hasOfrAccess(keycloak) ? { owner_finance_vessel: vesselNames } : { vessel_name_cii: vesselNames }) :
    {};
  const ownerFinanceRef = useRef(null);
  const { ga4EventTrigger } = useContext(GlobalContext);

  const tableauTokens = useTableauTokens({
    nums: DEFAULT_NUMBER_OF_DASHBOARDS,
    hasTableauAccess: true,
  });
  const isDashboardLoading = useDashboardLoader({ hasAtleastOneSubscription: true, tableauTokens });

  return (
    <div className="tableau_dashboard_owner_finance" data-testid="landing_page_owner_finance" id="landing_page_owner_finance" ref={ownerFinanceRef}>
      {
        isDashboardLoading || isLoadingVessel ? (
          <div className="dashboard-spinner">
            <Spinner animation="border" role="status" />
          </div>
        ) : (
          <div className="tableau_dashboard_owner_finance-container">
            <DashboardWrapper
              url={hasOfrAccess(keycloak) ? TableauLandingWidgets.owner_finance.tableauLink : TableauLandingWidgets.cii_rating.tableauLink}
              token={tableauTokens[0]}
              query={hasOfrAccess(keycloak) ? TableauLandingWidgets.owner_finance.query : TableauLandingWidgets.cii_rating.query }
              filters={{ ...DefaultDashboardFilters, ...vesselNamesFilter }}
              parameters={vesselNamesFilter}
            />
            {hasOfrAccess(keycloak) && <Button
              type="button"
              variant="primary"
              className="to-nova-button"
              onClick={() => {
                ga4EventTrigger && ga4EventTrigger('Landing Financial – Link', 'Landing Financial', 'Link full dashboard');
                window.open(`${PARIS_TWO_HOST}/dashboard/owner-finance`, '_self');
              }}
            >
              View Dashboard
            </Button>}
          </div>
        )
      }
    </div>
  );
};

export { OwnerFinanceWidget };
