/* eslint-disable jsx-a11y/img-redundant-alt */
import React from 'react';
import {
  Marker, Polyline,
} from '@react-google-maps/api';
import * as vesselService from '../../../services/vessel-service';

interface VesselTrackPolylineProps {
  vesselTracksData: vesselService.VesselTrack[];
}

const VesselTrackPolyline = (props: VesselTrackPolylineProps) => {
  const { vesselTracksData } = props;
  return (
    <div data-testid='track-polyline'>
      <Polyline
        path={vesselTracksData}
        options={{
          geodesic: true,
          strokeColor: '#FF0000',
          strokeOpacity: 1.0,
          strokeWeight: 2,
        }}
      />
      {vesselTracksData.map((track) => (
        <div 
          data-testid='track-marker'
          key={track.vessel_stratum_id}>
          <Marker
            position={{ lat: track.lat, lng: track.lng }}
            icon={{
              path: google.maps.SymbolPath.CIRCLE,
              fillOpacity: 1,
              strokeOpacity: 1,
              strokeWeight: 2,
              fillColor: 'white',
              strokeColor: '#FF0000',
              scale: 5,
            }}
          />
        </div>
      ))}
    </div>
  );
};

export default VesselTrackPolyline;
