/* eslint-disable jsx-a11y/img-redundant-alt */
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import * as vesselService from '../../../services/vessel-service';
import { dateOrDash } from 'src/utils/date-utils';

interface VesselInfoWindowProps {
  vesselData: any;
  positionData: vesselService.VesselPositionReport | null;
  isPositionLoading: boolean;
  itineraryData: any;
}

const DASH = '--';
const LOADING = 'loading...';

const VesselInfoWindow = (props: VesselInfoWindowProps) => {
  const { vesselData, positionData, isPositionLoading, itineraryData } = props;

  const displayText = (text: string): string => {
    if (isPositionLoading) {
      return LOADING;
    }
    return text ?? DASH;
  };

  const { name } = vesselData;
  const positionResult = positionData?.report_json;
  const images = vesselData.images ?? { images: [] }; // temporary for handling API missing field
  const firstImage = images[0];
  const vessel_image = firstImage ? firstImage.path : '/ui2014/1417703255_CIMG2417.JPG'; // temporary for testing
  const vesselType = vesselData.vessel_type.value;
  const positionSource = vesselData.is_from_stratumFive ? 'StratumFive' : 'Position Reports';
  const builtYear = (vesselData.vessel?.date_of_delivery ? moment(vesselData.vessel?.date_of_delivery).format('YYYY') : moment(vesselData.vessel?.year_of_delivery).format('YYYY')) ?? DASH;
  const length = vesselData.vessel?.length_oa ? `${vesselData.vessel.length_oa.toString().slice(0,10)} m` : DASH;
  const { dwt } = vesselData.vessel;
  const owner = vesselData.owner.value;
  const nextPort = displayText(itineraryData?.port);
  const speed = displayText(positionResult?.general?.average_speed?.toFixed(2));
  const mehfoConsumption = displayText(positionResult?.consumption?.main_engine?.hfo?.toFixed(2));
  const aehfoConsumption = displayText(positionResult?.consumption?.diesel?.hfo?.toFixed(2));
  const aemgoConsumption = displayText(positionResult?.consumption?.diesel?.mgo?.toFixed(2));
  const windForce = displayText(positionResult?.weather?.current?.wind_force);
  const windDirection = displayText(positionResult?.weather?.current?.wind_direction);
  const vesselDetailUrl = `/vessel/ownership/details/${vesselData.id}`;
  const [imgUrl, setImgUrl] = useState('');

  useEffect(() => {
    (async () => {
      try {
        if (vessel_image) {
          const photoUrlRes = await vesselService.downloadFile(vessel_image);
          const { url } = photoUrlRes.data.results[0];
          const doc = await vesselService.getPresignedDocument(url);
          const objUrl = window.URL.createObjectURL(
            new Blob([doc.data], {
              type: photoUrlRes.headers['content-type'],
            }),
          );
          setImgUrl(objUrl);
        }
      } catch (error) {
        console.error(`Get vessel photos failed. Error: ${error}`);
      }
    })();
  }, [vessel_image]);

  return (
    <div>
      <h6><a href={vesselDetailUrl}>{name}</a></h6>
      <hr />
      <div className="card-content">
        <img
          className={`d-block ${
            imgUrl ? 'w-70' : 'mx-auto'
          } rounded`}
          height="100"
          style={{ objectFit: 'cover' }}
          src={imgUrl}
          alt="Vessel-Photo"
        />
        <ul>
          <li>{`Vessel Type: ${vesselType}`}</li>
          <li>{`Year Built: ${dateOrDash(builtYear, 'YYYY')}`}</li>
          <li>{`Length: ${length}`}</li>
          <li>{`DWT: ${dwt} MT`}</li>
          <li>{`Owner: ${owner}`}</li>
          <li>{`Position Source: ${positionSource}`}</li>
          <li>{`Next Port: ${nextPort}`}</li>
          <li>{`Speed: ${speed}`}</li>
          <li>{`M/E HFO Consumption: ${mehfoConsumption}`}</li>
          <li>{`A/E HFO Consumption: ${aehfoConsumption}`}</li>
          <li>{`A/E MGO Consumption: ${aemgoConsumption}`}</li>
          <li>{`Wind Force: ${windForce}`}</li>
          <li>{`Wind Direction: ${windDirection}`}</li>
        </ul>
      </div>
    </div>
  );
};

export default VesselInfoWindow;
