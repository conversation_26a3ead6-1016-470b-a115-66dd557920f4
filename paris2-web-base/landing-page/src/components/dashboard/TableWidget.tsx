/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-lone-blocks */
/* eslint-disable react/jsx-props-no-spreading */
import React, { memo, useState, useContext } from "react";
import _ from "lodash";
import { Table, OverlayTrigger, Popover } from "react-bootstrap";
import { Column, useTable } from 'react-table';
import { FiAlertCircle } from "react-icons/fi";
import AlertGuide from './AlertGuide';
import "./styles/vessel-table-widget.scss";
import { GlobalContext } from "../../context/dashboard-context";
import { Icon } from '../../StyleGuide';
import { VesselOwned } from '../../types/vessel';

const { PARIS_TWO_HOST } = process.env;

interface Props {
  columns: Array<Column<VesselOwned >>;
  data: Array<VesselOwned >;
  isOfrEnabled: boolean;
  fallbackTitle?: string | null;
  noIcon?: boolean;
  from?: string | null;
  containerRef: React.RefObject<HTMLElement>;
}

const TableWidget = memo(({
  columns, data, isOfrEnabled, fallbackTitle, noIcon, from, containerRef,
}: Props) => {
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
  } = useTable({ columns, data });
  
  const [isShow, setIsShow] = useState(false);
  const [selectedPopoverId, setSelectedPopoverId] = useState('');
  const { ga4EventTrigger } = useContext(GlobalContext);
  const ga4LinkEvent = (vesselName: string, action: string) => ga4EventTrigger?.("Landing Vessels – Menu", vesselName, action);

  if (data.length === 0) {
    return (
      <AlertGuide
        caption={fallbackTitle ?? "No Records Found"}
        icon={!noIcon && <FiAlertCircle size={52} color="#1F4A70" />}
      />
    );
  }

  const routeToCrewList = (vesselId: number, vesselName: string) => {
    setIsShow(false);
    setSelectedPopoverId('')
    if (vesselId) {
      ga4LinkEvent(vesselName, 'Link to Crew List');
      window.open(`${PARIS_TWO_HOST}/seafarer/crew-list/vessel/${vesselId}`, '_blank');
    }
  };

  const routeToFinancialReport = (ownershipId: number, vesselName: string) => {
    setIsShow(false);
    setSelectedPopoverId('')
    if (ownershipId) {
      ga4LinkEvent(vesselName, 'Link to Financial Reports');
      window.open(`${PARIS_TWO_HOST}/vessel/ownership/details/${ownershipId}/monthlyFinancialReports`, '_blank');
    }
  };

  const selectPopover = (id: number) => {
    if (!selectedPopoverId || (selectedPopoverId && _.toNumber(selectedPopoverId) !== id)) {
      setIsShow(true);
      setSelectedPopoverId(id);
    } else {
      setIsShow(false);
      setSelectedPopoverId('')
    } 
  }

  return (
    <Table responsive borderless size="sm" {...getTableProps()}>
      <thead>
        {headerGroups.map((headerGroup) => (
          <tr className="table-header-container" {...headerGroup.getHeaderGroupProps()}>
            {headerGroup.headers.map((column) => (
              <th className={`column-header ${column.customHeaderClass ?? ''}`} {...column.getHeaderProps()}>
                {column.render('Header')}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody {...getTableBodyProps()}>
        {rows.map((row) => {
          prepareRow(row);
          return (
            <tr {...row.getRowProps()}>
              {row.cells.map((cell) => (
                <>
                <td className={`${cell.column.customRowClass ?? ''}`} {...cell.getCellProps()}>
                  {cell.render('Cell')}
                </td>
                {from === 'VesselList' && 
                <td className="ellipsis-menu">
                  <OverlayTrigger
                    rootClose
                    container={containerRef}
                    trigger="click"
                    key="left"
                    placement="left"
                    show={isShow && cell?.row?.original?.id === selectedPopoverId}
                    overlay={
                      <Popover id="action" className="vessels-popover">
                        <Popover.Content className="text-primary">
                          <ul className="popover-menu-wrap">
                            <li className="popover-menu-item" 
                            onClick={() => routeToCrewList(cell?.row?.original?.vessel?.id, cell?.row?.original?.name)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                  routeToCrewList(cell?.row?.original?.vessel?.id, cell?.row?.original?.name);
                                }
                              }}>
                              Crew List
                            </li>
                            {isOfrEnabled && <li className="popover-menu-item" 
                                tabIndex={0}
                                onClick={() => routeToFinancialReport(cell?.row?.original?.id, cell?.row?.original?.name)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                    routeToFinancialReport(cell?.row?.original?.id, cell?.row?.original?.name);
                                    }
                                }}
                            >
                              Financial Reports
                            </li>}
                          </ul>
                        </Popover.Content>
                      </Popover>
                    }
                  >
                    <div className="ellipsis-wrap">
                      <Icon icon="more" size={20} className="default" style={{ cursor: "pointer" }} onClick={() => selectPopover(cell?.row?.original?.id)}/>
                    </div>
                  </OverlayTrigger>
                </td>}
                </>
              ))}
            </tr>
          );
        })}
      </tbody>
    </Table>
  );
});

export { TableWidget };
