import React, { useContext } from "react";
import { GlobalContext } from "../../context/dashboard-context";
import { goToParisShipDetails } from "../../utils/paris-link";

interface Props {
  name: string;
  paris1RefID?: number | null;
  vesselID: number;
  shipPartyID?: number | null;
  ownershipID?: number | null;
  category: string;
}

const RenderVesselRow = ({
  name, paris1RefID, vesselID, shipPartyID, ownershipID, category,
}: Props) => {
  const {
    ga4EventTrigger,
  } = useContext(GlobalContext);
  const handleClick = () => goToParisShipDetails({
    ref_id: paris1RefID,
    vessel_id: vesselID,
    ownership_id: ownershipID,
    vessel_name: name
  }, shipPartyID, ga4EventTrigger, category);
  return <span aria-hidden="true" onClick={handleClick}>{name ?? '- - -'}</span>;
};

export { RenderVesselRow };
