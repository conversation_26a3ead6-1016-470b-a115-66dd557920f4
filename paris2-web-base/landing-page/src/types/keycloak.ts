export interface KeycloakProps {
  keycloak: Keycloak;
}

interface TokenParsed {
  email: string;
  rank: string;
  name: string;
  given_name?: string;
  family_name?: string;
  ship_party_id?: number | undefined | null;
  ship_party_type?: string | undefined;
  user_id: string;
  is_user_onboarded?: boolean;
  is_owner_onboarded?: boolean;
  preferred_username: string;
  group: string[];
  tc_version?:number;
  zoneinfo?: string;
  ip_address:string;
  financialOwnerReportingAccess?: string;
}

export interface Keycloak {
  realmAccess: RealmAccess;
  tokenParsed: TokenParsed;
  idTokenParsed: TokenParsed;
}

interface RealmAccess {
  roles: string[];
}
