import { createContext } from 'react';
import { VesselOwned } from 'src/types/vessel';
import { Keycloak } from 'src/types/keycloak';

export interface VesselQualship {
  id: number;
  vessel_id: number;
  paris1_ref_id: number;
  vessel_name: string;
  is_zero: boolean;
  certificate_url: string;
  full_certificate_url: string;
}

export interface GlobalContextState {
  vesselCurrentlyOwned: VesselOwned[];
  isVesselLoading: boolean;
  keycloak?: Keycloak | null;
  ga4EventTrigger?: (category: string, label: string, action?: string) => void;
  ga4PageView?: (path: string, location: string, title: string) => void;
}
export interface VesselItinerary {
  id: number;
  vessel_id: number;
  vessel_name: string;
  country: string;
  port: string;
  estimated_arrival: string | Date;
  ownership_id?: number | null;
}
export interface VesselItineraryState {
  isVesselItinerariesLoading: boolean;
  vesselItineraries: VesselItinerary[];
}

export const vesselItineraryDefaultState: VesselItineraryState = {
  isVesselItinerariesLoading: false,
  vesselItineraries: [],
};

const GlobalContext = createContext<GlobalContextState>({
  vesselCurrentlyOwned: [],
  isVesselLoading: true,
  ...vesselItineraryDefaultState,
  keycloak: null,
  ga4EventTrigger: () => {},
  ga4PageView: () => {},
});

export { GlobalContext };
