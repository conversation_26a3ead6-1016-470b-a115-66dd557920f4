import React from 'react';
import { KeycloakProps } from '../types/keycloak';
import './styles/dashboard.scss';
import { isOwnerOrRegisteredOwner } from '../utils/roles';
import { DefaultDashboard } from '../components/dashboard/DefaultDashboard';
import { OwnerDashboard } from '../components/dashboard/OwnerDashboard';

function Dashboard({ keycloak }: KeycloakProps) {
  if (isOwnerOrRegisteredOwner(keycloak)) {
    return <OwnerDashboard keycloak={keycloak} />;
  }
  return <DefaultDashboard keycloak={keycloak} />;
}

export { Dashboard };
