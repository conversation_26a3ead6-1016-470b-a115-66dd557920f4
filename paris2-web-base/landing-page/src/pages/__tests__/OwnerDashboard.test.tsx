import React from 'react';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { OWNER_GROUP_PREFIX } from '../../constants/roles';
import { WEATHER_MAP } from '../../constants/world-map';
import createGoogleMapsMock from '../../components/__test__/createGoogleMapsMock';
import WorldMapWidget from 'src/components/dashboard/WorldMapWidget';
import { vesselOwnershipDetailsList, mapMarkerData } from 'src/services/__mocks__/vessel-service';

jest.mock('../../StyleGuide.tsx');
jest.mock('../../services/user-service');
jest.mock('../../services/vessel-service');
jest.mock('../../services/tableau-proxy');
jest.mock('../../services/rss-service');
jest.mock('../../services/sharepoint-service');

jest.mock('@react-google-maps/api', () => ({
  useJsApiLoader: jest.fn(() => ({
    isLoaded: true,
  })),
  useGoogleMap: jest.fn(),
  GoogleMap: jest.fn(({ children, ...props }) => <div {...props}>{children}</div>),
  Marker: jest.fn(({ children, ...props }) => <div {...props}>{children}</div>),
}));

beforeAll(() => {
  window.google = {
    maps: createGoogleMapsMock(),
  };
});

afterAll(() => {
  delete window.google;
});

const keycloak = {
  tokenParsed: {
    email: '<EMAIL>',
    rank: 'test.user',
    name: 'test.user',
    ship_party_id: 10,
    user_id: 'test.user',
    is_user_onboarded: true,
    preferred_username: '',
    group: OWNER_GROUP_PREFIX,
    tc_version: 12,
    financialOwnerReportingAccess: 'allow',
  },
  realmAccess: {
    roles: ['vessel|view'],
  },
};

describe('should render owner dashboard', () => {
  test('should display weather icon and open new tab on click weather icon', async () => {
    act(() => {
      render(
        <WorldMapWidget
          vesselList={vesselOwnershipDetailsList}
          worldMapMarkerData={mapMarkerData}
          isLoadingVessel={false}
        />,
      );
    });
    await waitFor(() => {
      screen.logTestingPlaygroundURL();
      const weatherButton = screen.getByTestId('testid-weather-button');
      expect(weatherButton).toBeInTheDocument();
    });
  });
  test('should open new tab on click weather icon', async () => {
    const originalOpen = window.open;
    window.open = jest.fn();
    act(() => {
      render(
        <WorldMapWidget
          vesselList={vesselOwnershipDetailsList}
          worldMapMarkerData={mapMarkerData}
          isLoadingVessel={false}
        />,
      );
    });
    await waitFor(() => {
      const weatherButton = screen.getByTestId('testid-weather-button');
      fireEvent.click(weatherButton);
      expect(window.open).toHaveBeenCalledWith(WEATHER_MAP, '_blank');
      window.open = originalOpen;
    });
  });
});
