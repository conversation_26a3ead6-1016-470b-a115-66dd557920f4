import React from 'react';
import { act, cleanup, render, screen, waitFor } from '@testing-library/react';
import { Dashboard } from '../Dashboard';
import { createMemoryHistory } from 'history';
import { hasVesselViewRole } from '../../utils/roles';
import { renderWithRoute } from '../../utils/test-utils';
import { GlobalContext } from '../../context/dashboard-context';

jest.mock('../../StyleGuide.tsx');
jest.mock('../../services/user-service');
jest.mock('../../services/vessel-service');
jest.mock('../../services/tableau-proxy');
jest.mock('../../services/rss-service');
jest.mock('../../services/risk-widget-service');
jest.mock('../../services/sharepoint-service');

const keycloak = {
  tokenParsed: {
    email: '<EMAIL>',
    rank: 'test.user',
    name: 'test.user',
    ship_party_id: 10,
    user_id: 'test.user',
    is_user_onboarded: true,
    preferred_username: '',
    group: ['/Tech Group/CY Tech D3'],
    tc_version: 12,
  },
  realmAccess: {
    roles: ['vessel|view'],
  },
};

const keycloakWithoutVesselView = {
  ...keycloak,
  realmAccess: {
    roles: [],
  },
};

afterEach(cleanup);

describe('Should render Dashboard properly', () => {
  test.skip('Should display overview EEOI (Top 3) widget', () => {
    render(<Dashboard keycloak={keycloak} />);
    const linkElement = screen.getByText('EEOI (Top 3)');
    expect(linkElement).toBeInTheDocument();
  });

  test.skip('Should display Fuel Consumption (Top 3) widget', () => {
    render(<Dashboard keycloak={keycloak} />);
    const linkElement = screen.getByText('Fuel Consumption (Top 3)');
    expect(linkElement).toBeInTheDocument();
  });

  test('Should display Vessels', async () => {
    act(() => {
      render(<Dashboard keycloak={keycloak} />);
    });

    await waitFor(() => {
      const linkElement = screen.getByText('Vessels');
      expect(linkElement).toBeInTheDocument();
    });
  });

  test.skip('Should display Youtube area for tutorials', () => {
    render(<Dashboard keycloak={keycloak} />);
    const videoTitle = screen.getByText('Tutorials on How to Use Dashboards');
    expect(videoTitle).toBeInTheDocument();
  });

  test.skip('Should display EEOI tableau dashboards after successful authentication with Tableau proxy server', async () => {
    act(() => {
      render(<Dashboard keycloak={keycloak} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('tableau-dashboard-gaseous-emissions')).toBeInTheDocument();
    });
  });

  test.skip('Should display Fuel Consumption tableau dashboards after successful authentication with Tableau proxy server', async () => {
    act(() => {
      render(<Dashboard keycloak={keycloak} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('tableau-dashboard-fuel-consumption')).toBeInTheDocument();
    });
  });

  test.skip('should display backdrop and get started button if is_user_onboarded user attribute is not present', async () => {
    const kc = {
      ...keycloak,
      tokenParsed: {
        ...keycloak.tokenParsed,
        is_user_onboarded: false,
      },
    };
    render(<Dashboard keycloak={kc} />);
    const novaTutorial = screen.getByText('Data Analytics and insights');
    expect(novaTutorial).toBeInTheDocument();
  });

  test.skip('should display seven overlay components', async () => {
    // rest 2 are need widget component to render
    const kc = {
      ...keycloak,
      tokenParsed: {
        ...keycloak.tokenParsed,
        is_user_onboarded: false,
      },
    };
    render(<Dashboard keycloak={kc} />);
    const overlayid = screen.getAllByTestId('custom-overlay');
    expect(overlayid.length).toBe(6);
  });

  test.skip('Should be able to see EEOI dashboard if user vessel view role', async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole(keycloak.realmAccess.roles);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloak} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('eeoi-dashboard')).toBeInTheDocument();
    });
  });

  test("Should not be able to see EEOI dashboard if user doesn't have vessel view role", async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole([]);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloak} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('eeoi-dashboard')).not.toBeInTheDocument();
    });
  });

  test("Should not be able to see Fuel Consumption dashboard if user doesn't have vessel view role", async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole([]);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloakWithoutVesselView} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('fuel-consumption-dashboard')).not.toBeInTheDocument();
    });
  });

  test.skip('Should be able to see Fuel Consumption dashboard if user does have vessel view role', async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole(keycloak.realmAccess.roles);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloak} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('fuel-consumption-dashboard')).toBeInTheDocument();
    });
  });

  test("Should not be able to see Vessels if user doesn't have vessel view role", async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole(keycloakWithoutVesselView.realmAccess.roles);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloakWithoutVesselView} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('vessel-list-main-container')).not.toBeInTheDocument();
    });
  });

  test('Should be able to see Vessels if user does have vessel view role', async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole(keycloakWithoutVesselView.realmAccess.roles);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloakWithoutVesselView} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('vessel-list-main-container')).not.toBeInTheDocument();
    });
  });

  test('Should be able to see Term and Condition', async () => {
    const history = createMemoryHistory();
    const hasAccess = hasVesselViewRole(keycloakWithoutVesselView.realmAccess.roles);
    const path = '/home';

    history.push(path);

    act(() => {
      renderWithRoute(
        history,
        <GlobalContext.Provider
          value={{
            vesselCurrentlyOwned: [],
            isVesselLoading: false,
            activeVesselDetails: [],
          }}
        >
          <Dashboard keycloak={keycloakWithoutVesselView} />
        </GlobalContext.Provider>,
        path,
      );
    });

    await waitFor(() => {
      expect(screen.queryByTestId('term-condition-area')).toBeInTheDocument();
    });
  });
});
