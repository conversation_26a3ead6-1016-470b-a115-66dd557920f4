import { AxiosResponse } from 'axios';
import { httpClient } from '../config/http-client';
import { createHeaders } from './user-service';

const { SHIP_PARTY_HOST } = process.env;

export const getShipPartyDetails = async (
  shipPartyID: number,
): Promise<AxiosResponse<{ result: { iso_country_code: string } }>> => {
  return httpClient.get(`${SHIP_PARTY_HOST}/${shipPartyID}`, {
    headers: await createHeaders(),
  });
};
