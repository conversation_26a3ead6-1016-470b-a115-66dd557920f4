import { createHeaders } from 'src/services/user-service';
import {
  FetchParams,
  IProjectListResponse,
  IRiskAssessmentResponse,
  RaLevel,
  RAStatus,
  TransformedRisk,
  VesselGroup,
  VesselOption,
} from '../types/types';

const raLevelMap: Record<number, keyof typeof RaLevel> = {
  1: 'ROUTINE',
  2: 'SPECIAL',
  3: 'CRITICAL',
  4: 'LEVEL_1_RA',
};
// Handle null case separately in code logic

const raLevelLabels: Record<keyof typeof RaLevel, string> = {
  ROUTINE: 'Routine',
  SPECIAL: 'Special',
  CRITICAL: 'Critical',
  LEVEL_1_RA: 'Level 1 RA',
  Unassigned: 'Unassigned',
};
const statusMap: Record<number, keyof typeof RAStatus> = {
  1: 'DRAFT',
  2: 'PENDING',
  3: 'APPROVED',
  4: 'REJECTED',
  5: 'APPROVED_WITH_CONDITION',
};

const statusLabels: Record<keyof typeof RAStatus, string> = {
  DRAFT: 'Draft',
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  APPROVED_WITH_CONDITION: 'Approved with Condition',
};

const { API_RISK_ASSESSMENT_URL, VESSEL_HOST } = process.env;

// fetching all risks
export async function getVessels3(
  params: FetchParams,
): Promise<IProjectListResponse<TransformedRisk>> {
  const url = `${API_RISK_ASSESSMENT_URL}/risk-dashboard`;
  const headers = await createHeaders();
  headers['Content-Type'] = 'application/json';

  const res = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify({}), // sending empty object as per requirement
  });

  if (!res.ok) {
    const errorBody = await res.text();
    throw new Error(`Failed to fetch from ${url}. Status: ${res.status}. Body: ${errorBody}`);
  }

  const apiResponse: IRiskAssessmentResponse = await res.json();

  const transformedData: TransformedRisk[] = apiResponse.data.map((risk) => {
    const raLevelKey = risk.ra_level !== null && raLevelMap[risk.ra_level] ? raLevelMap[risk.ra_level] : 'Unassigned';

    const raLevelLabel = raLevelLabels[raLevelKey];

    const statusKey = statusMap[risk.status] || 'DRAFT';
    const statusLabel = statusLabels[statusKey];

    return {
      name: risk.vessel_name || 'Unknown Vessel',
      type: raLevelKey,
      vesselData: [risk.task_requiring_ra, raLevelLabel, statusLabel],
      vessel_ownership_id: risk.vessel_ownership_id,
      risk_id: risk.risk_approver?.[0]?.risk_id ?? null,
      vessel_id: risk.vessel_id,
    };
  });

  // Pagination (client-side slicing)
  const totalItems = transformedData.length;
  const page = params.page || 1;
  const limit = params.limit || 10;
  const totalPages = Math.ceil(totalItems / limit);
  const paginatedData = transformedData.slice((page - 1) * limit, page * limit);

  return {
    data: paginatedData,
    pagination: {
      totalItems,
      totalPages,
      page,
      pageSize: limit,
    },
  };
}

export async function fetchVesselOwnerships(): Promise<VesselGroup[]> {
  const headers = await createHeaders();

  const response = await fetch(
    `${VESSEL_HOST}/ownerships?order=created_at+desc&status=active&flatten=true&f=name&f=id&f=vessel.id&f=vessel_type.type`,
    {
      method: 'GET',
      headers: headers,
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch vessel ownerships: ${response.status}`);
  }

  const json = await response.json();
  const vesselArray = json?.results;

  if (!Array.isArray(vesselArray)) {
    throw new Error("Unexpected API response format: 'results' is not an array");
  }

  const vesselOptions: VesselOption[] = vesselArray
    .filter((item: any) => item.name && item.vessel_id)
    .map((item: any) => ({
      vessel_id: item.vessel_id,
      name: item.name,
      vessel_ownership_id: item.id,
    }));

  const vesselGroups1: VesselGroup[] = [
    {
      id: 1,
      title: '',
      vessels: vesselOptions,
    },
  ];

  return vesselGroups1;
}
