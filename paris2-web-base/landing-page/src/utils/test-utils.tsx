import React, { ReactChild } from "react";
import { render } from "@testing-library/react";
import { MemoryHistory, LocationState } from "history";
import { Route, Router } from "react-router";

function renderWithRoute(history: MemoryHistory<LocationState>, child: ReactChild, path: string) {
  render(
    <Router history={history}>
      <Route exact path={path}>
        {child}
      </Route>
    </Router>
  );
}

export { renderWithRoute };
