export function capitalizeFirstLetter(str: string | null | undefined): string {
  if (!str || typeof str !== 'string') return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export const getOcimfPillColor = (val: string): { color: string; background: string } => {
  const colors: Record<string, { color: string; background: string }> = {
    green: { color: 'white', background: '#28A747' },
    yellow: { color: 'black', background: '#FFC107' },
    orange: { color: 'white', background: '#F08100' },
    red: { color: 'white', background: '#D41B56' },
  };
  return colors[val] || { color: 'black', background: 'transparent' }; // Fallback style
};

export const RANKS_MAPPING: Record<string, string> = {
  MASTER: 'MASTER',
  CO: 'CHIEF OFFICER',
  '2O': '2ND OFFICER',
  '3O': '3RD OFFICER',
  CE: 'CHIEF ENGINEER',
  '2E': '2ND ENGINEER',
  '3E': '3RD ENGINEER',
  '4E': '4TH ENGINEER',
};

export const getRanksForTooltip = (commaSeparatedRanks: string): string => {
  if (!commaSeparatedRanks) return '';
  const ranks = commaSeparatedRanks.split(',').map(rank => RANKS_MAPPING[rank] || rank);
  return ranks.length >= 2 ? `${ranks[0]} + ${ranks[1]}` : ranks[0] || '';
};

export const getTypeOfCompliance = (key: string): string => {
  if (!key) return '';
  const formattedKey = capitalizeFirstLetter(key.replace(/_/g, ' '));
  return `"Exp. in ${formattedKey}" fails OCIMF Compliance`;
};
