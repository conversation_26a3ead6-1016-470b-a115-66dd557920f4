import { compare } from '../utility';
import { VesselOwned } from '../../types/vessel';

describe('compare function', () => {
  it('should return a negative number when the first name is less than the second', () => {
    const vesselA: VesselOwned = { name: 'Alpha' };
    const vesselB: VesselOwned = { name: 'Beta' };
    expect(compare(vesselA, vesselB)).toBeLessThan(0);
  });

  it('should return a positive number when the first name is greater than the second', () => {
    const vesselA: VesselOwned = { name: 'Gamma' };
    const vesselB: VesselOwned = { name: 'Beta' };
    expect(compare(vesselA, vesselB)).toBeGreaterThan(0);
  });

  it('should return 0 when both names are the same', () => {
    const vesselA: VesselOwned = { name: 'Delta' };
    const vesselB: VesselOwned = { name: 'Delta' };
    expect(compare(vesselA, vesselB)).toBe(0);
  });
});
