import { getTableauOptions, getCapitalizedName } from '../dashboard';

jest.mock('react-device-detect', () => ({
  deviceType: 'tablet',
}));

describe('getTableauOptions', () => {
  it('should return default device as detected deviceType', () => {
    const options = getTableauOptions();
    expect(options).toEqual({ device: 'tablet', hideTabs: true });
  });

  it('should use provided device when deviceType is undefined', () => {
    jest.mock('react-device-detect', () => ({ deviceType: undefined }));
    const options = getTableauOptions('mobile');
    expect(options).toEqual({ device: 'tablet', hideTabs: true });
  });
});

describe('getCapitalizedName', () => {
  it('should return an empty string when input is empty or null', () => {
    expect(getCapitalizedName('')).toBe('');
    expect(getCapitalizedName(null)).toBe('');
  });

  it('should capitalize a single word', () => {
    expect(getCapitalizedName('hello')).toBe('Hello');
  });

  it('should capitalize multiple words', () => {
    expect(getCapitalizedName('hello world')).toBe('Hello World');
  });

  it('should handle extra spaces', () => {
    expect(getCapitalizedName('  hello   world  ')).toBe('  Hello   World  ');
  });

  it('should handle already capitalized names correctly', () => {
    expect(getCapitalizedName('Hello World')).toBe('Hello World');
  });
});
