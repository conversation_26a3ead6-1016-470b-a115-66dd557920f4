import {
  capitalizeFirstLetter,
  getOcimfPillColor,
  getRanksForTooltip,
  getTypeOfCompliance,
} from '../ocimf';

describe('Utility Function Tests', () => {
  describe('capitalizeFirstLetter', () => {
    test('Should capitalize the first letter of a string', () => {
      expect(capitalizeFirstLetter('hello')).toBe('Hello');
    });

    test('Should return an empty string for null or undefined input', () => {
      expect(capitalizeFirstLetter(null)).toBe('');
      expect(capitalizeFirstLetter(undefined)).toBe('');
    });

    test('Should return an empty string for non-string input', () => {
      expect(capitalizeFirstLetter((123 as unknown) as string)).toBe('');
    });

    test('Should handle empty string input', () => {
      expect(capitalizeFirstLetter('')).toBe('');
    });
  });

  describe('getOcimfPillColor', () => {
    test('Should return correct color and background for valid input', () => {
      expect(getOcimfPillColor('green')).toEqual({ color: 'white', background: '#28A747' });
      expect(getOcimfPillColor('yellow')).toEqual({ color: 'black', background: '#FFC107' });
      expect(getOcimfPillColor('orange')).toEqual({ color: 'white', background: '#F08100' });
      expect(getOcimfPillColor('red')).toEqual({ color: 'white', background: '#D41B56' });
    });

    test('Should return fallback styles for invalid input', () => {
      expect(getOcimfPillColor('blue')).toEqual({ color: 'black', background: 'transparent' });
      expect(getOcimfPillColor('')).toEqual({ color: 'black', background: 'transparent' });
    });
  });

  describe('getRanksForTooltip', () => {
    test('Should return mapped ranks for valid input', () => {
      expect(getRanksForTooltip('MASTER,CO')).toBe('MASTER + CHIEF OFFICER');
      expect(getRanksForTooltip('2O,3O')).toBe('2ND OFFICER + 3RD OFFICER');
    });

    test('Should return unmapped ranks as-is', () => {
      expect(getRanksForTooltip('UNKNOWN,RANDOM')).toBe('UNKNOWN + RANDOM');
    });

    test('Should handle single rank inputs', () => {
      expect(getRanksForTooltip('MASTER')).toBe('MASTER');
    });

    test('Should return an empty string for empty input', () => {
      expect(getRanksForTooltip('')).toBe('');
    });
  });

  describe('getTypeOfCompliance', () => {
    test('Should return formatted compliance message for valid key', () => {
      expect(getTypeOfCompliance('ocimf_jr')).toBe('"Exp. in Ocimf jr" fails OCIMF Compliance');
      expect(getTypeOfCompliance('ocimf_sr')).toBe('"Exp. in Ocimf sr" fails OCIMF Compliance');
    });

    test('Should handle keys with underscores', () => {
      expect(getTypeOfCompliance('experience_issues')).toBe(
        '"Exp. in Experience issues" fails OCIMF Compliance',
      );
    });

    test('Should return an empty string for empty or null key', () => {
      expect(getTypeOfCompliance('')).toBe('');
      expect(getTypeOfCompliance((null as unknown) as string)).toBe('');
    });
  });
});
