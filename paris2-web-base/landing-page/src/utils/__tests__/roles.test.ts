import { getTechGroupOfUser, hasAccessToVessel } from '../roles';
import { Keycloak } from '../../types/keycloak';

describe('UserRoleController', () => {
  const groups = ['/Department/IT/Admin', '/Department/IT/DevTeam', '/Fleet'];
  const kc = {
    tokenParsed: {
      email: '<EMAIL>',
      rank: 'Software Engineer',
      name: 'Test User',
      user_id: '1sd213d',
    },
  } as Keycloak;

  it('should return tech group based on group', () => {
    expect(
      getTechGroupOfUser({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [...groups, '/Tech Group/CY Tech D2'],
        },
      }),
    ).toBe('CY Tech D2');
  });

  it('should return null if user is not assigned to any tech group', () => {
    expect(
      getTechGroupOfUser({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [...groups],
        },
      }),
    ).toBe(null);
  });

  it('should grant access to Vessel List if user is an owner', () => {
    expect(
      hasAccessToVessel({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          ship_party_id: 1,
          group: [],
        },
        realmAccess: {
          roles: ['nova|view', 'vessel|view'],
        },
      }),
    ).toBe(true);
  });

  it('should grant access to Vessel List if user is a fleet staff executive with access to all vessels', () => {
    expect(
      hasAccessToVessel({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [],
        },
        realmAccess: {
          roles: ['vessel|view'],
        },
      }),
    ).toBe(true);
  });

  it('should grant access to Vessel List if user belongs to a tech group', () => {
    expect(
      hasAccessToVessel({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [...groups, '/Tech Group/CY Tech D2'],
        },
        realmAccess: {
          roles: [],
        },
      }),
    ).toBe(true);
  });

  it('should grant access to Vessel List if user has tanker view role', () => {
    expect(
      hasAccessToVessel({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [],
        },
        realmAccess: {
          roles: ['vessel|view|tanker'],
        },
      }),
    ).toBe(true);
  });

  it('should grant access to Vessel List if user has dry view role', () => {
    expect(
      hasAccessToVessel({
        ...kc,
        tokenParsed: {
          ...kc.tokenParsed,
          group: [],
        },
        realmAccess: {
          roles: ['vessel|view|dry'],
        },
      }),
    ).toBe(true);
  });
});
