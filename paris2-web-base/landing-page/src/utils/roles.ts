import { Keycloak } from 'src/types/keycloak';
import { ROLES, VesselTypeRoles, OWNER_GROUP_PREFIX, KPI_SCORECARD_VIEW } from '../constants/roles';

const OWNER_GROUP = '/External/Owner';

function hasNovaView(keycloak: Keycloak) {
  if (!keycloak.realmAccess.roles || !keycloak.realmAccess.roles.length) return false;
  return keycloak.realmAccess.roles.includes(ROLES.VIEW);
}

function hasKpiScorecardView(keycloak: Keycloak) {
  if (!keycloak.realmAccess.roles || !keycloak.realmAccess.roles.length) return false;
  return keycloak.realmAccess.roles.includes(KPI_SCORECARD_VIEW);
}

function hasVesselViewBaseOnTypeRole(roles: string[]) {
  return roles.some(userRole => VesselTypeRoles.indexOf(userRole) >= 0);
}

function hasAccessToVessel(keycloak: Keycloak) {
  if (!keycloak.realmAccess.roles) return false;
  return (
    hasVesselViewRole(keycloak.realmAccess.roles) ||
    getTechGroupOfUser(keycloak) !== null ||
    hasVesselViewBaseOnTypeRole(keycloak.realmAccess.roles)
  );
}

function hasVesselViewRole(roles: string[]) {
  return roles.includes(ROLES.VESSEL_VIEW);
}

function getTechGroupOfUser(kc: Keycloak) {
  const approvalTechGroups = kc.tokenParsed.group
    .filter(group => group.startsWith('/Tech Group/'))
    .flatMap(techGroup => techGroup.split('/Tech Group/').filter(Boolean));

  return approvalTechGroups.length > 0 ? approvalTechGroups[0] : null;
}

function isOwnerOrRegisteredOwner(keycloak: Keycloak) {
  return OWNER_GROUP_PREFIX.some(prefix =>
    keycloak?.tokenParsed?.group?.find((group: string) => group.includes(prefix)),
  );
}

function hasOfrAccess(keycloak: Keycloak) {
  return keycloak?.tokenParsed?.financialOwnerReportingAccess === 'allow';
}

export {
  hasNovaView,
  hasKpiScorecardView,
  getTechGroupOfUser,
  hasVesselViewRole,
  hasAccessToVessel,
  isOwnerOrRegisteredOwner,
  hasOfrAccess,
};
