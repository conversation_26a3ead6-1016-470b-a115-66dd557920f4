import moment from 'moment';

export const getLocalGreetingsString = () =>{
    const checkPeriod = 'HH:mm:ss'
    const currentTime = moment()
    const isMorning = (
    currentTime.isBetween(moment('00:00:00', checkPeriod), moment('11:59:59', checkPeriod)) ||
    currentTime.isSame(moment('00:00:00', checkPeriod)) ||
    currentTime.isSame(moment('11:59:59', checkPeriod))
    )
    const isAfternoon = (
        currentTime.isBetween(moment('12:00:00', checkPeriod), moment('17:59:59', checkPeriod)) ||
        currentTime.isSame(moment('12:00:00', checkPeriod)) ||
        currentTime.isSame(moment('17:59:59', checkPeriod))
        )
    const isEvening = (
        currentTime.isBetween(moment('18:00:00', checkPeriod), moment('23:59:59', checkPeriod)) ||
        currentTime.isSame(moment('18:00:00', checkPeriod)) ||
        currentTime.isSame(moment('23:59:59', checkPeriod))
        )
    if (isMorning)
        return "Good Morning"
    if (isAfternoon)
        return "Good Afternoon"
    if (isEvening)
        return "Good Evening"
    return "Good Morning"
}

export const getCurrentTimeZone = ()=>{
    return moment.tz(moment.tz.guess()).zoneAbbr()  ;
}

export const getLocalDateStr = (date: Date, formatStr = 'YYYY-MM-DD HH:mm:ss') => {
    const dateStr = moment(date).format(formatStr);
    return dateStr;
  };

export const getUtcDateStr = (date: Date, formatStr = 'YYYY-MM-DD HH:mm:ss') => {
  const dateStr = moment(date).utc().format(formatStr);
  return dateStr;
};
