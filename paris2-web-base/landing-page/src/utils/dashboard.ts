import { deviceType } from 'react-device-detect';

export const getTableauOptions = (device: string | null = 'desktop') => (
  {
    device: deviceType ?? device,
    hideTabs: true,
  }
);

export const getCapitalizedName = (name: string) => {
  if (!name || name === '') {
    return '';
  }
  const capitalizedName = name
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  return capitalizedName;
};
