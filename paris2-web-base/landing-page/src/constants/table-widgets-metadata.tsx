import React from "react";
import { VesselItinerary } from "../context/dashboard-context";
import { dateOrDash } from '../utils/date-utils';
import { RenderVesselRow } from "../components/dashboard/RenderVesselRow";

// https://parisdev.fleetship.com/fml/PARIS?display=survey

export const ItineraryColumns = (shipPartyID: number) => [
  {
    Header: 'Vessel Name',
    accessor: ({
      vessel_name, vessel_id, ownership_id,
    }: VesselItinerary) => <RenderVesselRow name={vessel_name} vesselID={vessel_id} ownershipID={ownership_id} category="Itinerary" />,
    customHeaderClass: 'vessel-column-header',
    customRowClass: 'vessel-link-row first-column',
  },
  {
    Header: 'Port',
    accessor: ({ port, country }: VesselItinerary) => `${port}, ${country}`,
    customRowClass: 'basic-column-text',
  },
  {
    Header: 'ETA',
    accessor: ({ estimated_arrival }: VesselItinerary) => dateOrDash(estimated_arrival, 'DD MMM'),
    customHeaderClass: 'certificate-column-header',
    customRowClass: 'last-column basic-column-text',
  },
];

export const VesselColumns = (shipPartyID: number) => [
  {
    Header: 'Vessel Name',
    accessor: ({
      name, vessel, id,
    }: VesselItinerary) => <RenderVesselRow name={name} vesselID={vessel.id} ownershipID={id} category="Vessel" />,
    customHeaderClass: 'vessel-column-header',
    customRowClass: 'vessel-link-row first-column',
  },
];


