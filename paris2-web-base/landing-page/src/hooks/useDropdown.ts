import { useState, useRef, useEffect, useCallback } from "react";

/**
 * This hook encapsulates the logic for managing a dropdown menu's state.
 * It handles opening/closing the menu and detecting clicks outside of it to close it.
 *
 * @returns An object containing the dropdown's ref, its open state, and a function to toggle it.
 */
export const useDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const closeDropdown = useCallback(() => {
    setIsOpen(false);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        closeDropdown();
      }
    };

    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [closeDropdown]);

  return { dropdownRef, isOpen, toggleDropdown };
};
