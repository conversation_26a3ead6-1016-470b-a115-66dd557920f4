import { useState, useEffect, useCallback } from "react";
import { IProjectListResponse } from "../types/types";

// Options for the hook, including pagination and any other query params.
type UseInfiniteQueryOptions = {
  page?: number;
  limit?: number;
} & Record<string, any>; // Allows for additional, arbitrary filter parameters.

// The return type of the hook, providing state and methods to the component. *
interface UseInfiniteQueryResult<T> {
  data: IProjectListResponse<T> | null;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => Promise<void>;
  hasNextPage: boolean;
  error: Error | null;
  refetch: () => void;
}

/**
 * A custom hook to abstract the logic for fetching paginated data infinitely.
 *
 * @param fetchFn - The function that fetches data. Should return a promise resolving to IProjectListResponse<T>.
 * @param options - Configuration for the query, like page, limit, and other filters.
 */
const useInfiniteQuery = <T>(
  fetchFn: (params: {
    page: number;
    limit: number;
    [key: string]: any;
  }) => Promise<IProjectListResponse<T>>,
  options: UseInfiniteQueryOptions = {},
) => {
  const { page: initialPage = 1, limit = 50, ...queryParams } = options;

  const [data, setData] = useState<IProjectListResponse<T> | null>(null);
  const [page, setPage] = useState(initialPage);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Using JSON.stringify for dependency arrays can be inefficient
  // as it can cause re-renders if key order changes or if objects are new instances
  // but have the same values. For complex objects, consider using a deep-compare
  // effect hook or ensuring the `options` object passed to this hook is memoized
  // with `useMemo` in the parent component.
  const queryParamsString = JSON.stringify(queryParams);

  const executeFetch = useCallback(
    async (currentPage: number) => {
      const isFirstPage = currentPage === initialPage;
      if (isFirstPage) {
        setIsLoading(true);
      } else {
        setIsFetchingNextPage(true);
      }
      setError(null);

      try {
        const response = await fetchFn({
          page: currentPage,
          limit,
          ...queryParams,
        });
        setData((prev) =>
          isFirstPage
            ? response
            : { ...response, data: [...(prev?.data ?? []), ...response.data] },
        );
        setPage(currentPage);
        setHasNextPage(
          response.pagination.page < response.pagination.totalPages,
        );
      } catch (err) {
        setError(err as Error);
      } finally {
        if (isFirstPage) {
          setIsLoading(false);
        } else {
          setIsFetchingNextPage(false);
        }
      }
    },
    [fetchFn, initialPage, limit, queryParamsString],
  );

  const fetchNextPage = useCallback(async () => {
    if (!hasNextPage || isFetchingNextPage || isLoading) return;
    executeFetch(page + 1);
  }, [hasNextPage, isFetchingNextPage, isLoading, page, executeFetch]);

  const refetch = useCallback(() => {
    setData(null); // Clear existing data
    executeFetch(initialPage);
  }, [executeFetch, initialPage]);

  // Effect to fetch initial data or refetch when query parameters change.
  useEffect(() => {
    refetch();
  }, [refetch]);

  return {
    data,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    error,
    refetch,
  };
};

export default useInfiniteQuery;
