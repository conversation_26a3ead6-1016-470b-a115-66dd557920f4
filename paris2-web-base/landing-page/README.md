# React Single Spa Boilerplate (Microfrontend)

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Dependencies

- [React](https://reactjs.org) for building UI.

- [Single Spa](https://single-spa.js.org/) for building microfrontends.

- [Webpack](https://webpack.js.org/) for module bundling.

- [Babel](https://babeljs.io/) for JS compilation.

- [React Test Renderer](https://reactjs.org/docs/test-renderer.html) for writing UI automated test.

- [Root Config of Single Spa](https://bitbucket.org/fleetshipteam/paris2-web-base/src/develop/) repository of single spa root to register your application.

- [single-spa-inspector Chrome plugin](https://chrome.google.com/webstore/detail/single-spa-inspector/emldbibkihanfiaiaghebffnbahjcgcp?hl=en) for overriding your application and running or debugging locally.

## Environment Setup

1. Clone the root of single-spa which is [paris2-web-base](https://bitbucket.org/fleetshipteam/paris2-web-base/src/develop/)

2. Add the module or microfrontend project name in `assets/importmap/modules.json` in *paris2-web-base*. EG: `paris2-paris2-frontend-boiler-plat`

3. Add the prefix route in `root-config/src/activity-functions.js` in *paris2-web-base*. EG: `paris2-paris2-frontend-boiler-plat`.

Example below:

```js
export function tableau(location) {
  return prefix(location, "tableau");
}
```

4. Then you need to commit and submit a pull request to develop so that you can override it using the [single-spa-inspector Chrome plugin](https://chrome.google.com/webstore/detail/single-spa-inspector/emldbibkihanfiaiaghebffnbahjcgcp?hl=en)

## Running the project and debugging locally

In the project directory, you can run:

### `npm run start`

Runs app in the development mode.\
Open [https://localhost:9050/](https://localhost:9050/) to view it in the browser. The browser might  ask you to proceed.

Don't forget to override your *module* under *single-spa-inspector* tab of Chrome or browser you are using.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.
