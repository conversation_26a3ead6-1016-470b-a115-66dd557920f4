module.exports = {
  "presets": [
      "@babel/preset-env",
      "@babel/preset-react",
      "@babel/preset-typescript"
  ],
  "plugins": [
    "@babel/plugin-transform-react-jsx",
    "@babel/plugin-syntax-jsx",
      [
          "@babel/plugin-transform-runtime",
          {
              "regenerator": true
          }
      ],
      "babel-plugin-styled-components",
      "@babel/plugin-proposal-class-properties",
      [
          "@babel/plugin-proposal-decorators",
          {
              "decoratorsBeforeExport": true
          }
      ]
  ]
}