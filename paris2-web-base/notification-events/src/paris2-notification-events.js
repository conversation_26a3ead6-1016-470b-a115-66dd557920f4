import axios from 'axios';
import auth from '@paris2/auth';

let sseSource = null;
const listeners = [];
let messageStore = [];

let user = null;

const NOTIFICATION_HOST = process.env.NOTIFICATION_HOST;

const API_HOST = `${NOTIFICATION_HOST}/api`;
export const EVENT_TYPE_MESSAGES = 'messages';
export const EVENT_TYPE_MESSAGE_UPDATE = 'message_update';
export const EVENT_TYPE_MESSAGE_REMOVE = 'message_remove';
export const EVENT_TYPE_MESSAGE_REMOVE_ALL = 'message_remove_all';
export const EVENT_TYPE_INFO = 'info';
export const EVENT_TYPE_USER = 'user';

export const MESSAGE_STATUS_DELIVERED = 'delivered';
export const MESSAGE_STATUS_READ = 'read';

const createHeaders = (accessToken) => ({
  'Content-Type': 'application/json',
  'Access-Control-Allow-Origin': '*',
  Authorization: `Bearer ${accessToken}`,
});

const notifyUpdate = (event) => listeners.forEach((listener) => {
  try {
    listener(event);
  } catch (e) { console.error (e); }
});

export async function connect() {
  const accessToken = await auth.getToken();
  console.log('load notification token first');
  const { status } = await axios.post(`${API_HOST}/user/auth`, {}, {
    headers: createHeaders(accessToken),
    withCredentials: true,
  });
  if (status !== 200) {
    console.error('failed to authorize notification server');
    return null;
  }
  // eslint-disable-next-line no-undef
  console.log('connect notification service...');
  closeConnection(); // closing existing connection if there's one.
  sseSource = new EventSource(`${NOTIFICATION_HOST}/event-stream`, {
    withCredentials: true,
  });
  sseSource.onmessage = (e) => {
    console.log('received notification');
    const messageData = e.data;
    try {
      const event = JSON.parse(messageData);
      if (!event.type) {
        console.error('missing event type', event);
        return;
      }

      let processedEvent = event;

      switch(event.type) {
        case EVENT_TYPE_MESSAGES:{
          const newMessages = [];
          event.messages.forEach((message) => {
            const messageExists = !!messageStore.find(({ id }) => id === message.id);
            if (!messageExists) {
              newMessages.push(message);
            }
          });
          messageStore = [...newMessages, ...messageStore];
          processedEvent = {
            ...event,
            messages: newMessages,
          }
          break;
        }
        case EVENT_TYPE_USER:
          user = event.user;
          break;
      }

      notifyUpdate(processedEvent);
    } catch (e) {
        console.error(messageData, e);
    }
  };
  return sseSource;
}

export function getUser() {
  return user;
}

export async function getMessages({
  offset,
  limit,
  orderColumn = 'id',
  orderDirection = 'desc',
}) {
  const accessToken = await auth.getToken();
  const urlParams = `offset=${offset}&limit=${limit}&order=${orderColumn}+${orderDirection}`;
  const { status, data } = await axios.get(`${API_HOST}/messages?${urlParams}`, {
    headers: createHeaders(accessToken),
    withCredentials: true,
  });
  if (status !== 200) {
    console.error('failed to get more messages');
    throw new Error('failed to get more messages');
  }
  return data;
}

export function listen(listener) {
  let index = listeners.indexOf(listener);
  if (index != -1) {
    return function close() {
      listeners.slice(index, 1);
    }
  }
  listeners.push(listener);
  index = listeners.length - 1;
  // send cached message and current user status
  if (messageStore.length > 0) {
    listener({
      type: EVENT_TYPE_MESSAGES,
      messages: messageStore,
    });
  }
  if (user) {
    listener({
      type: EVENT_TYPE_USER,
      user,
    });
  }
  return function close() {
    listeners.slice(index, 1);
  }
}

export function closeConnection() {
  if (!sseSource) return;
  console.log('close sse');
  sseSource.close();
}

const postApi = async (api, body = {}) => {
  const accessToken = await auth.getToken();
  const { status } = await axios.post(`${API_HOST}/${api}`, body, {
    headers: createHeaders(accessToken),
  });
  return status === 200;
}

export async function resetUncheckedMessageCount() {
  if (!user) return;
  user.uncheckedMessageCount = 0;
  notifyUpdate({
    type: EVENT_TYPE_USER,
    user,
  });
  return postApi('user/reset-unchecked-message-count');
}

export async function removeMessage(messageId) {
  const index = messageStore.findIndex(({ id }) => id === messageId);
  if (index !== -1) {
    messageStore.splice(index, 1);
  }
  notifyUpdate({
    type: EVENT_TYPE_MESSAGE_REMOVE,
    messageId,
  });
  return postApi('messages/remove', {
    messageIds: [messageId],
  })
}

export async function removeAllMessages() {
  const clearingMessageIds = messageStore.map(({ id }) => id);
  messageStore = [];
  notifyUpdate({
    type: EVENT_TYPE_MESSAGE_REMOVE_ALL,
    messageId:clearingMessageIds
  });
  return postApi('messages/remove-all')
}

export async function markMessageRead(messageId) {
  return markMessagesRead([messageId]);
}

export async function markMessagesRead(messageIds) {
  messageIds.forEach((messageId) => {
    const message = messageStore.find(({ id }) => id === messageId);
    if (!message) return;
    message.status = MESSAGE_STATUS_READ;
    notifyUpdate({
      type: EVENT_TYPE_MESSAGE_UPDATE,
      message,
    });
  });
  return postApi('messages/mark-read', {
    messageIds,
  });
}

export async function markAllMessagesRead() {
  messageStore.forEach((message) => {
    message.status = MESSAGE_STATUS_READ;
    notifyUpdate({
      type: EVENT_TYPE_MESSAGE_UPDATE,
      message,
    });
  });
  return postApi('messages/mark-all-read');
}

export function NotificationEventListener ({ onMessages, onUser, onMessageUpdate, onMessageRemove, onAllMessageRemove } ) {
  return (event) => {
    switch(event.type) {
      case EVENT_TYPE_MESSAGES:
        if (onMessages) {
          onMessages(event.messages);
        }
        break;
      case EVENT_TYPE_USER:
        if (onUser) {
          onUser(event.user);
        }
        break;
      case EVENT_TYPE_MESSAGE_UPDATE:
        if (onMessageUpdate) {
          onMessageUpdate(event.message);
        }
        break;
      case EVENT_TYPE_MESSAGE_REMOVE:
        if (onMessageRemove) {
          onMessageRemove(event.messageId);
        }
        break;
      case EVENT_TYPE_MESSAGE_REMOVE_ALL:
        if (onAllMessageRemove) {
            onAllMessageRemove();
        }
        break;
    }
  }
}

export default {
  connect,
  listen,
  closeConnection,
  getUser,
  resetUncheckedMessageCount,
  markMessageRead,
  markMessagesRead,
  removeMessage,
  markAllMessagesRead,
  removeAllMessages,
  getMessages,
}