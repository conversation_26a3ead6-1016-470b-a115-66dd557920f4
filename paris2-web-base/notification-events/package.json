{"name": "paris2-notification-events", "scripts": {"start": "webpack-dev-server --mode=development --server-type https", "lint": "eslint src", "test": "jest", "prettier": "prettier --write './**'", "build": "webpack --mode=production"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.21.5", "@babel/runtime": "^7.21.5", "@types/systemjs": "^6.13.1", "babel-eslint": "^11.0.0-beta.2", "babel-loader": "^9.1.2", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.1.1", "eslint": "^6.7.2", "eslint-config-important-stuff": "^1.1.0", "eslint-config-prettier": "^6.7.0", "eslint-plugin-prettier": "^3.1.1", "html-webpack-plugin": "^5.5.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "serve": "^11.2.0", "systemjs-webpack-interop": "^2.0.0", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-dev-server": "^4.13.3"}, "dependencies": {"axios": "^0.27.2", "history": "^4.10.1", "keycloak-js": "^19.0.3", "single-spa": "^6.0.0"}}