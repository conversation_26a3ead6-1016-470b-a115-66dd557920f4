/* eslint-disable no-undef */
const path = require('path');
const webpack = require('webpack');

const { ENV = 'dev' } = process.env
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });

const { CleanWebpackPlugin } = require('clean-webpack-plugin');

const { AUTH_SERVER_URL, NOTIFICATION_HOST } = process.env;

module.exports = env => ({
    entry: path.resolve(__dirname, 'src/paris2-notification-events'),
    output: {
        filename: 'paris2-notification-events.js',
        libraryTarget: 'system',
        path: path.resolve(__dirname, 'dist', ENV),
    },
    devtool: 'source-map',
    module: {
        rules: [
            { parser: { system: false } },
            {
                test: /\.m?js$/,
                resolve: {
                    fullySpecified: false
                },
                exclude: /node_modules/,
                use: [{ loader: 'babel-loader' }],
            },
        ],
    },
    devServer: {
        historyApiFallback: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        allowedHosts: 'all',
        port: 9018
    },
    plugins: [
        new CleanWebpackPlugin(),
        new webpack.DefinePlugin({
          "process.env": {
            "AUTH_SERVER_URL": JSON.stringify(AUTH_SERVER_URL || ""),
            "NOTIFICATION_HOST": JSON.stringify(NOTIFICATION_HOST || "")
          }
        }),
    ],
    externals: [
        'single-spa',
        /^@paris2\/.+$/,
        // /^styled-components\/?.*$/,
        // /^react-i18next\/?.*$/, 
        // /^react-dom\/?.*$/,
        // /^react-bootstrap\/?.*$/,
        // /^react\/lib.*/,
        // /^axios\/?.*$/,
    ],
});
