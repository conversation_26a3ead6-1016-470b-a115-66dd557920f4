/* eslint-disable no-undef */
const path = require('path');
const webpack = require('webpack');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

const { ENV = 'dev' } = process.env
require('dotenv').config({ path: path.resolve(__dirname, '..', 'paris2-configuration.env') });
const { AUTH_SERVER_URL } = process.env

module.exports = env => ({
    entry: path.resolve(__dirname, 'src/external-config'),
    output: {
        filename: 'external-config.js',
        libraryTarget: 'system',
        path: path.resolve(__dirname, 'dist', ENV),
    },
    devtool: 'sourcemap',
    module: {
        rules: [
            { parser: { system: false } },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: [{ loader: 'babel-loader' }],
            },
        ],
    },
    devServer: {
        historyApiFallback: true,
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        disableHostCheck: true,
    },
    plugins: [
      new CleanWebpackPlugin(),
      new webpack.DefinePlugin({
        "process.env": {
          "AUTH_SERVER_URL": JSON.stringify(AUTH_SERVER_URL || "")
        }
      }),
    ],
    externals: [
        'single-spa',
        /^@fleetship\/.+$/, 
        // /^@paris2\/.+$/, 
        // /^styled-components\/?.*$/,
        // /^react-i18next\/?.*$/, 
        // /^react-dom\/?.*$/,
        // /^react-bootstrap\/?.*$/,
        // /^react\/lib.*/,
        // /^axios\/?.*$/,
    ],
});
