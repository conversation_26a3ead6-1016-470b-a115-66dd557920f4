/* eslint-disable no-undef */
const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react");

module.exports = (webpackConfigEnv) => {

  const defaultConfig = singleSpaDefaults({
    orgName: "paris2",
    projectName: "localization",
    webpackConfigEnv,
  });

  const externals = {
    externals: [
        // 'single-spa', 
        // /^styled-components\/?.*$/,
        // /^@paris2\/.+$/, 
        /^react-i18next\/?.*$/, 
        // /^react-dom\/?.*$/,
        // /^react-bootstrap\/?.*$/,
        // /^react\/lib.*/,
        // /^axios\/?.*$/,
    ],
  };

  return merge(defaultConfig, externals, {
    module: {
      rules: [],
    },
    devServer: {
      port: 9010,
    },
  });
};
