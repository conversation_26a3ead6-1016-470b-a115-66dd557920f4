// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Stepper Component matches snapshot 1`] = `
<DocumentFragment>
  <div
    class="d-flex flex-column"
    style="gap: 1rem;"
  >
    <div
      class="card"
      style="border: 1px solid #ddd; box-shadow: none; border-radius: 6px; padding: 1rem; cursor: pointer; opacity: 1;"
    >
      <div
        class="align-items-center justify-content-between row"
      >
        <div
          class="col"
        >
          <div
            class="fs-14 fw-500 secondary-color"
          >
            Step 1
          </div>
          <div
            class="stepper-lbl"
          >
            First Step
          </div>
        </div>
        <div
          class="col-auto"
        >
          <svg
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              fill="#0DA666"
              r="11.5"
              stroke="#0DA666"
            />
            <g
              clip-path="url(#clip0_859_3700)"
            >
              <path
                d="M18.795 7.06441C18.5216 6.79103 18.0784 6.79103 17.805 7.06441L9.4186 15.4509L6.19499 12.2273C5.92163 11.9539 5.47845 11.954 5.20504 12.2273C4.93165 12.5007 4.93165 12.9439 5.20504 13.2172L8.92362 16.9358C9.1969 17.2091 9.64041 17.2089 9.91358 16.9358L18.795 8.05437C19.0684 7.78101 19.0683 7.33779 18.795 7.06441Z"
                fill="white"
              />
            </g>
            <defs>
              <clippath
                id="clip0_859_3700"
              >
                <rect
                  fill="white"
                  height="14"
                  transform="translate(5 5)"
                  width="14"
                />
              </clippath>
            </defs>
          </svg>
        </div>
      </div>
    </div>
    <div
      class="card"
      style="border: 1px solid #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2); border-radius: 6px; padding: 1rem; cursor: default; opacity: 1;"
    >
      <div
        class="align-items-center justify-content-between row"
      >
        <div
          class="col"
        >
          <div
            class="fs-14 fw-500 secondary-color"
          >
            Step 2
          </div>
          <div
            class="stepper-lbl"
          >
            Second Step
          </div>
        </div>
        <div
          class="col-auto"
        >
          <div
            style="width: 23px; height: 23px; border-radius: 50%; border: 1px solid #ccc;"
          />
        </div>
      </div>
    </div>
    <div
      class="card"
      style="border: 1px solid #ddd; box-shadow: none; border-radius: 6px; padding: 1rem; cursor: default; opacity: 0.6;"
    >
      <div
        class="align-items-center justify-content-between row"
      >
        <div
          class="col"
        >
          <div
            class="fs-14 fw-500 secondary-color"
          >
            Step 3
          </div>
          <div
            class="stepper-lbl"
          >
            Third Step
          </div>
        </div>
        <div
          class="col-auto"
        >
          <div
            style="width: 23px; height: 23px; border-radius: 50%; border: 1px solid #ccc;"
          />
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
